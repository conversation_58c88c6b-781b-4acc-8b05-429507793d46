#!/usr/bin/env python3
"""
Security Configuration Verification Script
Verifies that Django settings are properly configured for production
"""

import os
import sys
from pathlib import Path

# Add backend to Python path before Django imports
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "novel.settings")

# Django imports after path setup  # noqa: E402
import django  # noqa: E402
from django.conf import settings  # noqa: E402

django.setup()


def check_security_settings():
    """Check critical security settings"""
    issues = []
    warnings = []

    print("🔒 Django Security Configuration Check")
    print("=" * 50)

    # Critical checks
    if settings.DEBUG:
        issues.append("❌ DEBUG is True - NEVER enable in production!")
    else:
        print("✅ DEBUG is False")

    if hasattr(settings, "SECRET_KEY") and "django-insecure" in settings.SECRET_KEY:
        issues.append("❌ Using insecure default SECRET_KEY")
    else:
        print("✅ SECRET_KEY appears secure")

    if getattr(settings, "CORS_ALLOW_ALL_ORIGINS", False):
        issues.append("❌ CORS_ALLOW_ALL_ORIGINS is True - security risk!")
    else:
        print("✅ CORS_ALLOW_ALL_ORIGINS is False")

    # Security headers check
    if not settings.DEBUG:
        if getattr(settings, "SECURE_BROWSER_XSS_FILTER", False):
            print("✅ XSS Filter enabled")
        else:
            warnings.append("⚠️ XSS Filter not enabled")

        if getattr(settings, "SECURE_CONTENT_TYPE_NOSNIFF", False):
            print("✅ Content Type Nosniff enabled")
        else:
            warnings.append("⚠️ Content Type Nosniff not enabled")

    # ALLOWED_HOSTS check
    if "*" in settings.ALLOWED_HOSTS:
        issues.append("❌ ALLOWED_HOSTS contains '*' - security risk!")
    elif settings.ALLOWED_HOSTS:
        print(f"✅ ALLOWED_HOSTS configured: {settings.ALLOWED_HOSTS}")
    else:
        warnings.append("⚠️ ALLOWED_HOSTS is empty")

    # Admin URL check
    admin_url = getattr(settings, "ADMIN_URL", "admin/")
    if admin_url == "admin/":
        warnings.append("⚠️ Using default admin URL - consider changing for production")
    else:
        print("✅ Custom admin URL configured")

    # Database check
    db_config = settings.DATABASES["default"]
    if "localhost" in db_config.get("HOST", "") and not settings.DEBUG:
        warnings.append("⚠️ Using localhost database in production mode")

    # Results
    print("\n" + "=" * 50)
    print("📊 Security Check Results:")
    print(f"✅ Passed: {6 - len(issues) - len(warnings)} checks")

    if warnings:
        print(f"⚠️ Warnings: {len(warnings)}")
        for warning in warnings:
            print(f"  {warning}")

    if issues:
        print(f"❌ Critical Issues: {len(issues)}")
        for issue in issues:
            print(f"  {issue}")
        print("\n🚨 CRITICAL: Fix these issues before deploying to production!")
        return False
    else:
        print("\n🎉 All critical security checks passed!")
        return True


if __name__ == "__main__":
    try:
        success = check_security_settings()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        sys.exit(1)
