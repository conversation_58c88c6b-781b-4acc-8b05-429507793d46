#!/bin/bash

echo "開始部署到測試環境..."

# 設置環境變量
export DJANGO_SETTINGS_MODULE=novel.settings.test
export NODE_ENV=test

# 創建必要的目錄
mkdir -p backend/logs
mkdir -p backend/staticfiles
mkdir -p backend/mediafiles

# 後端部署
echo "部署後端..."
cd backend

# 創建並激活虛擬環境
python -m venv venv
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt

# 創建測試數據庫
psql -U postgres -c "DROP DATABASE IF EXISTS novel_test_db;"
psql -U postgres -c "CREATE DATABASE novel_test_db;"

# 執行遷移
python manage.py migrate

# 創建超級用戶
echo "from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin123') if not User.objects.filter(username='admin').exists() else None" | python manage.py shell

# 收集靜態文件
python manage.py collectstatic --noinput

# 加載測試數據
python manage.py loaddata test_data.json

# 啟動 Gunicorn
gunicorn novel.wsgi:application \
    --bind 127.0.0.1:8000 \
    --workers 2 \
    --access-logfile logs/gunicorn-access.log \
    --error-logfile logs/gunicorn-error.log \
    --daemon

# 前端部署
echo "部署前端..."
cd ../frontend

# 安裝依賴
npm install

# 使用測試環境配置
cp .env.test .env

# 構建
npm run build

# 設置 Nginx
echo "配置 Nginx..."
sudo tee /etc/nginx/sites-available/novel-test << EOF
server {
    listen 80;
    server_name test.example.com;

    # API 和管理界面
    location /api {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
    }

    location /admin {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
    }

    # 靜態文件
    location /static {
        alias /path/to/backend/staticfiles;
    }

    location /media {
        alias /path/to/backend/mediafiles;
    }

    # 前端
    location / {
        root /path/to/frontend/build;
        try_files \$uri \$uri/ /index.html;
    }

    # 日誌
    access_log /var/log/nginx/novel-test-access.log;
    error_log /var/log/nginx/novel-test-error.log;
}
EOF

# 啟用站點
sudo ln -sf /etc/nginx/sites-available/novel-test /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx

# 運行測試
echo "運行測試..."
cd ../backend
python manage.py test

cd ../frontend
npm test

echo "測試環境部署完成！"
echo "後端 API: http://test.example.com/api"
echo "前端: http://test.example.com"
echo "管理界面: http://test.example.com/admin"
echo "超級用戶: admin / admin123"

# 檢查服務狀態
echo "檢查服務狀態..."
ps aux | grep gunicorn
sudo systemctl status nginx | grep Active
