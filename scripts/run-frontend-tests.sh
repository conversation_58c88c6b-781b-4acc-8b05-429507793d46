#!/bin/bash
# 版本: v1.0
set -e

cd frontend

echo '📦 容器環境驗證:'
echo "Node.js 版本: $(node --version)"
echo "pnpm 版本: $(pnpm --version)"
echo "當前路徑: $(pwd)"
echo ''

# 🚀 關鍵修復：直接使用CI映像中已安裝的依賴，無需重複安裝
if [ -d "node_modules" ]; then
  echo '✅ 檢測到 node_modules 已存在，跳過安裝步驟'
else
  echo '🚀 安裝依賴...'
  # 配置 pnpm 使用掛載的 store (如果存在)
  if [ -d "/workspace/.pnpm-store" ]; then
    echo '🔧 配置 pnpm 使用掛載的 store...'
    pnpm config set store-dir /workspace/.pnpm-store
    echo "✅ pnpm store 配置為: $(pnpm config get store-dir)"
  fi
  pnpm install --frozen-lockfile
fi

echo ''
echo '🔐 執行安全漏洞檢查...'
echo "檢查已知安全漏洞..."

# 執行安全審計檢查 - 先檢查危急漏洞
pnpm audit --audit-level=critical
if [ $? -eq 0 ]; then
  echo '✅ 安全檢查通過：未發現危急漏洞'
else
  echo '❌ 安全檢查失敗：發現危急漏洞'
  exit 1
fi

# 檢查高危漏洞
pnpm audit --audit-level=high
if [ $? -eq 0 ]; then
  echo '✅ 安全檢查通過：未發現高危漏洞'
else
  echo '❌ 安全檢查失敗：發現高危漏洞'
  exit 1
fi

echo ''
echo '🧪 執行 frontend 測試...'
echo "使用Node版本: $(node --version)"

# 確保使用容器內的Node環境
NODE_ENV=test pnpm test

echo ''
echo '✅ Frontend test 完成!'
