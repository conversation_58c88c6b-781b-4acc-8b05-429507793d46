# Turborepo Remote Caching 設置指南

## 🚀 設置 Vercel Remote Caching

### 步驟 1: 註冊並配置 Vercel Remote Caching

1. **本地設置**：
   ```bash
   # 登入 Vercel (如果還沒有帳號會自動註冊)
   npx turbo login
   
   # 將專案鏈接到 Vercel
   npx turbo link
   ```

2. **獲取 Token 和 Team ID**：
   執行 `npx turbo link` 後，會顯示類似以下資訊：
   ```
   >>> Remote Caching
   
   Your team's Remote Cache is enabled. Turborepo will cache your builds in the cloud.
   
   Team: your-team-name (team_xxxxxxxxxx)
   ```

### 步驟 2: 設置 GitHub Secrets

前往 GitHub 倉庫 Settings > Secrets and variables > Actions，添加以下 secrets：

1. **TURBO_TOKEN**：
   - 從 Vercel Dashboard > Settings > Tokens 創建新的 token
   - 或者從 `~/.turbo/config.json` 中獲取

2. **TURBO_TEAM**：
   - 從 `npx turbo link` 輸出中獲取 team ID
   - 格式通常是 `team_xxxxxxxxxx`

### 步驟 3: 更新 GitHub Actions CI 配置

更新 `.github/workflows/main-ci.yml` 以支持 Turborepo Remote Caching：

```yaml
name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.4.0

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - name: Setup pnpm cache
      uses: actions/cache@v3
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Build with Turborepo Remote Caching
      run: pnpm turbo build
      env:
        TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
        TURBO_TEAM: ${{ secrets.TURBO_TEAM }}

    - name: Run tests
      run: pnpm turbo test
      env:
        TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
        TURBO_TEAM: ${{ secrets.TURBO_TEAM }}

    - name: Run linting
      run: pnpm turbo lint
      env:
        TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
        TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
```

### 步驟 4: 驗證設置

```bash
# 測試遠程緩存是否正常工作
TURBO_TOKEN=your_token TURBO_TEAM=your_team pnpm turbo build

# 第二次運行應該顯示 CACHE HIT
pnpm turbo build
```

## ⚙️ Turborepo 配置文件

確保 `turbo.json` 配置支持遠程緩存：

```json
{
  "$schema": "https://turbo.build/schema.json",
  "globalDependencies": ["**/.env.*local"],
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", "build/**", ".next/**"],
      "env": ["NODE_ENV"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "outputs": [],
      "dependsOn": ["^build"]
    },
    "test": {
      "outputs": ["coverage/**"],
      "dependsOn": ["^build"]
    },
    "clean": {
      "cache": false
    }
  },
  "remoteCache": {
    "signature": true
  }
}
```

## 🧪 測試場景

### 場景 A: 基準測試 (無變更)
- 創建空 PR，觀察 CI 是否正確跳過

```bash
# 本地測試
pnpm turbo build --dry
# 應該顯示: Tasks:    0 successful, 0 total
```

### 場景 B: Web App 變更
- 修改 `apps/web/src/App.tsx`
- 預期：只有 @novelwebsite/web 重新構建

```bash
# 檢查變更影響
pnpm turbo build --dry
# 應該只顯示 web app 需要重新構建
```

### 場景 C: 共享包變更 (未來)
- 修改 `packages/ui/` 中的組件
- 預期：ui 包和依賴它的 apps 都重新構建

```bash
# 查看依賴圖
pnpm turbo build --graph
```

### 場景 D: 配置變更
- 修改 `turbo.json` 或 `package.json`
- 預期：所有任務重新運行

```bash
# 強制重新構建
pnpm turbo build --force
```

## 📊 Remote Caching 性能監控

### 設置緩存命中率監控

在 CI 中添加緩存統計：

```yaml
    - name: Turborepo Cache Statistics
      run: |
        echo "📊 Turborepo 緩存統計："
        pnpm turbo build --summarize
      env:
        TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
        TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
```

### 監控指標

- **緩存命中率**: 目標 > 80%
- **構建時間**: 目標減少 50-70%
- **網路傳輸**: 快取下載/上傳時間

## 🔍 故障排除

### 常見問題

1. **遠程緩存不工作**：
   ```bash
   # 檢查配置
   cat ~/.turbo/config.json
   
   # 驗證連接
   npx turbo login --check
   ```

2. **權限問題**：
   ```bash
   # 重新登入
   npx turbo logout
   npx turbo login
   ```

3. **構建失敗**：
   ```bash
   # 檢查 workspace 配置
   pnpm list --depth=0
   
   # 確認所有依賴都已安裝
   pnpm install --frozen-lockfile
   ```

4. **緩存過期或損壞**：
   ```bash
   # 清理本地緩存
   npx turbo clean
   
   # 強制重新構建
   pnpm turbo build --force
   ```

### 有用的調試命令

```bash
# 查看 Turborepo 配置
pnpm turbo --help

# 查看構建計劃（不實際執行）
pnpm turbo build --dry

# 查看依賴關係圖
pnpm turbo build --graph

# 查看詳細日誌
pnpm turbo build --verbosity=2

# 強制重新構建（忽略緩存）
pnpm turbo build --force

# 清理所有緩存
pnpm turbo clean

# 查看緩存統計
pnpm turbo build --summarize

# 檢查遠程緩存狀態
npx turbo login --check
```

## 🔐 安全最佳實踐

### GitHub Secrets 管理

1. **限制 Token 權限**：
   - 只授予必要的緩存權限
   - 定期輪換 tokens

2. **Team 隔離**：
   - 為不同環境使用不同的 teams
   - 生產環境使用專用的 team

3. **訪問控制**：
   - 限制誰可以訪問緩存
   - 啟用審計日誌

### 環境變數安全

```yaml
# 在 CI 中安全地使用環境變數
env:
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
  # 避免在日誌中暴露敏感資訊
```

## 📈 性能優化建議

### 1. 緩存策略優化

```json
{
  "pipeline": {
    "build": {
      "outputs": ["dist/**", "build/**"],
      "inputs": ["src/**", "package.json", "tsconfig.json"]
    }
  }
}
```

### 2. 並行執行優化

```bash
# 最大化並行執行
pnpm turbo build --parallel

# 限制並發數（避免資源耗盡）
pnpm turbo build --concurrency=4
```

### 3. 依賴圖優化

- 減少不必要的依賴關係
- 合理設計 package 結構
- 避免循環依賴

## 🚀 下一步行動

### 立即執行清單

- [ ] 設置 Vercel Remote Caching
- [ ] 配置 GitHub Secrets
- [ ] 更新 CI/CD 配置
- [ ] 測試緩存功能
- [ ] 監控性能指標

### 長期優化計劃

- [ ] 建立緩存性能監控儀表板
- [ ] 實施緩存策略自動調優
- [ ] 設置緩存命中率告警
- [ ] 建立緩存最佳實踐文檔

---

## 📞 支援資源

- [Turborepo 官方文檔](https://turbo.build/repo/docs)
- [Vercel Remote Caching 指南](https://turbo.build/repo/docs/core-concepts/remote-caching)
- [GitHub Actions 整合](https://turbo.build/repo/docs/ci/github-actions)

---

**最後更新**: 2025-06-26  
**適用版本**: Turborepo 1.10+, pnpm 9.4.0+