# NovelWebsite Dockerfile 使用情況分析報告

## 📋 執行摘要

本報告分析了 NovelWebsite 項目中所有 **9** 個 Dockerfile 文件的使用情況，識別出活躍使用與過渡性質的文件。先前標記的遺留文件（`frontend-lean.Dockerfile`, `backend-debug.Dockerfile`）已於 2025-06-24 移除。

### 🎯 主要發現
- **活躍使用**: 8 個文件
- **疑似遺留**: 0 個文件
- **過渡性質**: 1 個文件

---

## 📊 文件分類詳情

### ✅ 活躍使用的 Dockerfile (8個)

| 文件名 | 用途 | 引用位置 | 狀態 |
|--------|------|----------|------|
| `frontend-tier2.Dockerfile` | 生產級前端映像 | • GitHub Actions CI<br>• ECR部署腳本<br>• 構建腳本 | 🟢 活躍 |
| `backend-tier2.Dockerfile` | 生產級後端映像 | • GitHub Actions CI<br>• ECR部署腳本<br>• 構建腳本 | 🟢 活躍 |
| `frontend-ci.Dockerfile` | CI測試環境前端 | • GitHub Actions CI | 🟢 活躍 |
| `backend-ci.Dockerfile` | CI測試環境後端 | • GitHub Actions CI | 🟢 活躍 |
| `Dockerfile` | 基礎爬蟲服務 | • docker-compose.crawler.yml<br>• docker-compose.ttkan.yml | 🟢 活躍 |
| `Dockerfile.dev` | 本地開發環境 | • docker-compose.dev.yml | 🟢 活躍 |
| `Dockerfile.act` | GitHub Actions本地測試 | • docker-compose.act.yml | 🟢 活躍 |
| `backend.Dockerfile` | Tier 1.5後端映像 | • 技術債務文檔記錄 | 🟡 過渡期 |

### 🔄 過渡性質的 Dockerfile (1個)

| 文件名 | 狀態 | 說明 |
|--------|------|------|
| `frontend.Dockerfile` | Tier 1.5 → Tier 2 | 性能優化升級中 |

---

## 🔍 詳細使用場景分析

### GitHub Actions CI/CD 使用
```yaml
# .github/workflows/main-ci.yml 中的引用
frontend-tier2.Dockerfile  # 生產前端構建
frontend-ci.Dockerfile     # CI測試環境
backend-tier2.Dockerfile   # 生產後端構建
backend-ci.Dockerfile      # CI測試環境
```

### Docker Compose 服務使用
```yaml
docker-compose.crawler.yml:  Dockerfile           # 爬蟲服務
docker-compose.ttkan.yml:    Dockerfile           # TTKAN爬蟲
docker-compose.dev.yml:      Dockerfile.dev       # 開發環境
docker-compose.act.yml:      Dockerfile.act       # 本地CI測試
```

### ECR 部署腳本使用
```bash
# infra/aws-ecr/local-docker-test.sh 中的引用
frontend-tier2.Dockerfile  # 生產部署
backend-tier2.Dockerfile   # 生產部署
```

---

## ⚠️ 風險評估

### 高風險遺留文件
**無發現** - 所有文件都有某種程度的使用跡象

### 中風險遺留文件
- **過渡期文件**: `backend.Dockerfile`, `frontend.Dockerfile`

### 低風險文件
- 所有 tier2 和 ci 版本的 Dockerfile 都有明確的使用場景

---

## 🎯 優化建議

### 立即行動 (高優先級)
1. **確認疑似遺留文件** ✅ 已完成（2025-06-24 刪除）

2. **技術債務清理**
   - 在`backend.Dockerfile`和`frontend.Dockerfile`頂部添加註釋
   - 標明預計移除時間表
   - 確認 Tier 2 遷移完成狀態

### 中期規劃 (中優先級)
1. **目錄重組**
   ```
   infra/docker/
   ├── production/          # tier2 文件
   ├── ci/                  # ci 文件
   ├── development/         # dev, act 文件
   ├── legacy/              # 待移除文件
   └── services/            # 服務專用文件
   ```

2. **文檔完善**
   - 為每個 Dockerfile 添加頂部註釋說明用途
   - 更新 README 說明不同版本的使用場景

### 長期維護 (低優先級)
1. **自動化檢測**
   - 添加 CI 檢查未使用的 Dockerfile
   - 定期掃描和報告文件使用情況

2. **版本管理**
   - 建立 Dockerfile 版本命名規範
   - 實施廢棄文件的標準流程

---

## 📋 執行檢查清單

### 🔍 立即確認 (本週)
- [x] 搜索`frontend-lean.Dockerfile`的實際使用 (已刪除)
- [x] 搜索`backend-debug.Dockerfile`的實際使用 (已刪除)
- [ ] 確認`backend.Dockerfile`移除時間表
- [ ] 確認`frontend.Dockerfile`移除時間表

### 📝 文檔更新 (2週內)
- [ ] 為所有 Dockerfile 添加用途說明註釋
- [ ] 更新項目文檔中的 Docker 使用指南
- [ ] 建立 Dockerfile 命名和版本管理規範

### 🗂️ 結構優化 (1個月內)
- [ ] 重組 infra/docker 目錄結構
- [ ] ~~移除確認的遺留文件~~ (已完成)
- [ ] 實施自動化檢測腳本

### 🔄 流程改進 (持續)
- [ ] 建立新 Dockerfile 的審查流程
- [ ] 定期執行使用情況分析
- [ ] 維護技術債務清單

---

## 🏁 結論

NovelWebsite 項目的 Dockerfile 管理整體良好，大部分文件都有明確的使用場景。主要問題集中在：

1. **過渡期文件**需要明確的遷移時間表
2. **目錄結構**可以進一步優化組織

建議按照上述優先級逐步實施改進措施，確保 Docker 配置的清潔性和可維護性。

---

**生成時間**: 2025年6月25日
**分析範圍**: 9個 Dockerfile 文件
**建議執行**: 按優先級分階段實施
