# GitHub Secrets 配置指南 - Turborepo Remote Caching

## 🚀 配置步驟

### 步驟 1: 設置 Vercel Remote Caching

首先需要在本地設置 Turborepo 並獲取必要的 tokens：

```bash
# 1. 登入 Vercel (如果還沒有帳號會自動註冊)
npx turbo login

# 2. 將專案鏈接到 Vercel
npx turbo link
```

### 步驟 2: 獲取 TURBO_TOKEN 和 TURBO_TEAM

執行 `npx turbo link` 後，您會看到類似以下輸出：

```
>>> Remote Caching

Your team's Remote Cache is enabled. Turborepo will cache your builds in the cloud.

Team: your-team-name (team_xxxxxxxxxx)
```

**獲取 TURBO_TOKEN 的方法**：

**方法 1: 從 Vercel Dashboard**
1. 登入 [Vercel Dashboard](https://vercel.com/dashboard)
2. 前往 Settings > Tokens
3. 創建新的 token，選擇適當的權限
4. 複製生成的 token

**方法 2: 從本地配置檔案**
```bash
# 查看本地 Turbo 配置
cat ~/.turbo/config.json
```

應該會看到類似：
```json
{
  "teamId": "team_xxxxxxxxxx",
  "token": "your_turbo_token_here"
}
```

### 步驟 3: 在 GitHub 中設置 Secrets

前往您的 GitHub 倉庫：`https://github.com/MumuTW/novel-web/settings/secrets/actions`

需要添加以下兩個 Repository secrets：

#### 3.1 添加 TURBO_TOKEN

1. 點擊 "New repository secret"
2. Name: `TURBO_TOKEN`
3. Secret: 貼上從步驟 2 獲取的 token
4. 點擊 "Add secret"

#### 3.2 添加 TURBO_TEAM

1. 點擊 "New repository secret"
2. Name: `TURBO_TEAM`
3. Secret: 貼上 team ID（格式通常是 `team_xxxxxxxxxx`）
4. 點擊 "Add secret"

## 🔧 驗證設置

### 本地驗證

```bash
# 使用環境變數測試遠程緩存
TURBO_TOKEN=your_token TURBO_TEAM=your_team pnpm turbo build

# 第二次運行應該顯示 CACHE HIT
pnpm turbo build
```

### CI 驗證

在設置完 secrets 後，下次 push 或 PR 時，CI 應該會自動使用遠程緩存。您可以在 GitHub Actions 日誌中看到類似的輸出：

```
✓ @novelwebsite/web:build: cache hit (remote), replaying logs 
```

## 📋 完整的 Secrets 清單

為了確保 CI/CD 完整運行，以下是所有需要的 secrets：

### Turborepo Remote Caching
- [ ] `TURBO_TOKEN` - Vercel Turborepo token
- [ ] `TURBO_TEAM` - Vercel team ID

### AWS ECR (已存在)
- [ ] `AWS_ECR_ROLE_ARN` - AWS IAM role for ECR access

### 性能測試 (可選)
- [ ] `PERCY_TOKEN` - Percy 視覺回歸測試 token (如果需要)

## 🔐 安全最佳實踐

### Token 權限控制

1. **最小權限原則**
   - Turbo token 只授予緩存相關權限
   - 避免授予不必要的 Vercel 管理權限

2. **定期輪換**
   - 建議每 90 天輪換一次 tokens
   - 設置日曆提醒

3. **環境隔離**
   - 生產環境和開發環境使用不同的 teams
   - 考慮為不同分支使用不同的緩存策略

### GitHub Secrets 管理

1. **訪問控制**
   - 限制誰可以查看/編輯 secrets
   - 啟用 branch protection rules

2. **審計**
   - 定期檢查 secrets 使用記錄
   - 監控異常的 API 調用

## 🧪 測試腳本

創建以下腳本來測試 Remote Caching 設置：

### test-remote-cache.sh

```bash
#!/bin/bash

echo "🧪 測試 Turborepo Remote Caching 設置"

# 檢查環境變數
if [ -z "$TURBO_TOKEN" ] || [ -z "$TURBO_TEAM" ]; then
    echo "❌ 錯誤: TURBO_TOKEN 和 TURBO_TEAM 環境變數必須設置"
    echo "請執行:"
    echo "export TURBO_TOKEN=your_token"
    echo "export TURBO_TEAM=your_team"
    exit 1
fi

echo "✅ 環境變數檢查通過"

# 清理本地緩存
echo "🧹 清理本地緩存..."
pnpm turbo clean

# 第一次構建 (應該 MISS)
echo "🔄 第一次構建 (預期 CACHE MISS)..."
time pnpm turbo build

# 第二次構建 (應該 HIT)
echo "🔄 第二次構建 (預期 CACHE HIT)..."
time pnpm turbo build

echo "✅ Remote Caching 測試完成"
echo "如果第二次構建顯示 'cache hit (remote)'，表示設置成功！"
```

### 使用方法

```bash
# 設置環境變數
export TURBO_TOKEN=your_actual_token
export TURBO_TEAM=your_actual_team

# 執行測試
chmod +x test-remote-cache.sh
./test-remote-cache.sh
```

## 🚨 故障排除

### 常見問題

#### 1. 遠程緩存不工作

**症狀**: 總是顯示 CACHE MISS，從不顯示 CACHE HIT

**解決方案**:
```bash
# 檢查 token 是否有效
npx turbo login --check

# 重新登入
npx turbo logout
npx turbo login

# 檢查 team 設置
cat ~/.turbo/config.json
```

#### 2. GitHub Actions 中的權限錯誤

**症狀**: CI 中顯示權限被拒絕

**解決方案**:
1. 確認 GitHub Secrets 設置正確
2. 檢查 token 權限
3. 驗證 team ID 格式 (`team_xxxxxxxxxx`)

#### 3. 網路連接問題

**症狀**: 無法連接到 Vercel API

**解決方案**:
```bash
# 測試網路連接
curl -H "Authorization: Bearer $TURBO_TOKEN" \
     https://vercel.com/api/v2/user

# 檢查防火牆設置
```

### 調試命令

```bash
# 查看詳細日誌
pnpm turbo build --verbosity=2

# 強制重新構建
pnpm turbo build --force

# 查看緩存統計
pnpm turbo build --summarize

# 檢查 Turbo 配置
npx turbo login --check
```

## 📈 監控和優化

### 緩存效率監控

在 CI 中添加緩存統計報告：

```yaml
- name: Turborepo Cache Statistics
  run: |
    echo "📊 Turborepo 緩存統計：" >> $GITHUB_STEP_SUMMARY
    pnpm turbo build --summarize | tee turbo-summary.txt
    
    # 解析緩存命中率
    if grep -q "cache hit" turbo-summary.txt; then
        echo "✅ 遠程緩存正常工作" >> $GITHUB_STEP_SUMMARY
    else
        echo "⚠️ 未檢測到緩存命中" >> $GITHUB_STEP_SUMMARY
    fi
  env:
    TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
    TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
```

### 性能目標

- **緩存命中率**: > 80%
- **構建時間減少**: > 50%
- **CI 執行時間**: 保持 < 2 秒（基於智能變更檢測）

## 🔄 設置完成檢查清單

- [ ] 本地執行 `npx turbo login`
- [ ] 本地執行 `npx turbo link`
- [ ] 獲取 TURBO_TOKEN
- [ ] 獲取 TURBO_TEAM ID
- [ ] 在 GitHub 中設置 `TURBO_TOKEN` secret
- [ ] 在 GitHub 中設置 `TURBO_TEAM` secret
- [ ] 本地測試遠程緩存功能
- [ ] 創建 PR 測試 CI 中的遠程緩存
- [ ] 驗證緩存命中率 > 80%
- [ ] 設置監控和告警

## 📞 獲取幫助

如果遇到問題，可以參考：

- [Turborepo Remote Caching 官方文檔](https://turbo.build/repo/docs/core-concepts/remote-caching)
- [Vercel API 文檔](https://vercel.com/docs/rest-api)
- [GitHub Secrets 文檔](https://docs.github.com/en/actions/security-guides/encrypted-secrets)

---

**最後更新**: 2025-06-26  
**適用版本**: Turborepo 1.10+, GitHub Actions