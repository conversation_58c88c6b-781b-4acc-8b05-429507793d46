import type { Meta, StoryObj } from '@storybook/react-webpack5';
import { useState } from 'react';
import FontSizeSlider from './FontSizeSlider';

const meta: Meta<typeof FontSizeSlider> = {
  title: 'Components/FontSizeSlider',
  component: FontSizeSlider,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
FontSizeSlider 是一個受控組件，用於調整字體大小。

### 特點：
- **受控組件模式** - 無內部狀態，完全由父組件控制
- **無障礙設計** - 包含適當的 ARIA 標籤
- **視覺反饋** - 滑桿進度條和 hover/focus 狀態
- **靈活配置** - 可自定義範圍、步進值和顯示選項

### 使用範例：
\`\`\`tsx
const [fontSize, setFontSize] = useState(16);

<FontSizeSlider
  value={fontSize}
  onChange={setFontSize}
  min={12}
  max={32}
/>
\`\`\`
        `,
      },
    },
  },
  argTypes: {
    value: {
      control: { type: 'range', min: 12, max: 32, step: 1 },
      description: '當前字體大小值',
    },
    onChange: {
      action: 'onChange',
      description: '字體大小變更回調函數',
    },
    min: {
      control: { type: 'number' },
      description: '最小值',
      defaultValue: 12,
    },
    max: {
      control: { type: 'number' },
      description: '最大值',
      defaultValue: 32,
    },
    step: {
      control: { type: 'number' },
      description: '步進值',
      defaultValue: 1,
    },
    showValue: {
      control: 'boolean',
      description: '是否顯示當前值標籤',
      defaultValue: true,
    },
    showMinMax: {
      control: 'boolean',
      description: '是否顯示最小/最大標籤',
      defaultValue: true,
    },
    ariaLabel: {
      control: 'text',
      description: '無障礙標籤',
      defaultValue: '字體大小調整',
    },
    className: {
      control: 'text',
      description: '自定義 CSS 類別',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基本範例
export const Default: Story = {
  args: {
    value: 16,
  },
};

// 完整功能展示（使用 render 函數來維持狀態）
export const Interactive: Story = {
  render: function Render(args) {
    const [value, setValue] = useState(args.value || 16);

    return (
      <div className="w-80 p-6 bg-white rounded-lg shadow-sm">
        <FontSizeSlider {...args} value={value} onChange={setValue} />

        <div className="mt-6 p-4 bg-gray-50 rounded">
          <p style={{ fontSize: `${value}px` }} className="text-gray-700">
            這是一段示例文字，會根據滑桿調整字體大小。 目前字體大小為 {value}px。
          </p>
        </div>
      </div>
    );
  },
  args: {
    value: 16,
    min: 12,
    max: 32,
    step: 1,
    showValue: true,
    showMinMax: true,
  },
};

// 最小配置
export const Minimal: Story = {
  args: {
    value: 16,
    showValue: false,
    showMinMax: false,
  },
};

// 自定義範圍
export const CustomRange: Story = {
  args: {
    value: 20,
    min: 14,
    max: 28,
    step: 2,
  },
};

// 無標籤版本
export const NoLabels: Story = {
  render: function Render(args) {
    const [value, setValue] = useState(args.value || 16);

    return (
      <div className="w-80">
        <FontSizeSlider
          {...args}
          value={value}
          onChange={setValue}
          showValue={false}
          showMinMax={false}
        />
      </div>
    );
  },
  args: {
    value: 16,
  },
};

// 多個滑桿比較
export const Comparison: Story = {
  render: function Render() {
    const [small, setSmall] = useState(14);
    const [medium, setMedium] = useState(16);
    const [large, setLarge] = useState(20);

    return (
      <div className="space-y-6 w-96 p-6 bg-white rounded-lg shadow-sm">
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-2">小號字體</h3>
          <FontSizeSlider value={small} onChange={setSmall} min={12} max={16} />
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-2">中號字體</h3>
          <FontSizeSlider value={medium} onChange={setMedium} min={14} max={20} />
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-2">大號字體</h3>
          <FontSizeSlider value={large} onChange={setLarge} min={18} max={32} />
        </div>

        <div className="pt-4 border-t space-y-2">
          <p style={{ fontSize: `${small}px` }}>小號字體示例 ({small}px)</p>
          <p style={{ fontSize: `${medium}px` }}>中號字體示例 ({medium}px)</p>
          <p style={{ fontSize: `${large}px` }}>大號字體示例 ({large}px)</p>
        </div>
      </div>
    );
  },
};
