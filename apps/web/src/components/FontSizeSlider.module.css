/* 自定義滑桿樣式 */
.slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  border-radius: 0.5rem;
  outline: none;
  transition: opacity 0.2s;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.15s ease-in-out;
}

.slider::-webkit-slider-thumb:hover {
  box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1);
}

.slider::-webkit-slider-thumb:active {
  box-shadow: 0 0 0 12px rgba(59, 130, 246, 0.2);
}

.slider::-moz-range-thumb {
  width: 1.25rem;
  height: 1.25rem;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.15s ease-in-out;
}

.slider::-moz-range-thumb:hover {
  box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1);
}

.slider::-moz-range-thumb:active {
  box-shadow: 0 0 0 12px rgba(59, 130, 246, 0.2);
}

.slider:focus {
  outline: none;
}

.slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
}

.slider:focus::-moz-range-thumb {
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
}
