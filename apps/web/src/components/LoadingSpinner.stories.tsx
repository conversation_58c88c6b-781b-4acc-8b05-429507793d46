import type { Meta, StoryObj } from '@storybook/react-webpack5';
import LoadingSpinner from './LoadingSpinner';

const meta = {
  title: 'Components/LoadingSpinner',
  component: LoadingSpinner,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A reusable loading spinner component with customizable size, color, and label options.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
      description: 'Size variant of the spinner',
    },
    color: {
      control: { type: 'select' },
      options: ['blue', 'gray', 'green'],
      description: 'Color theme of the spinner',
    },
    label: {
      control: { type: 'text' },
      description: 'Display text shown alongside the spinner',
    },
    centered: {
      control: { type: 'boolean' },
      description: 'Whether to center the spinner in its container',
    },
  },
} satisfies Meta<typeof LoadingSpinner>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    label: '載入中...',
    size: 'md',
    color: 'blue',
    centered: false,
  },
};

// Size variations
export const Small: Story = {
  args: {
    ...Default.args,
    size: 'sm',
    label: '載入中...',
  },
};

export const Large: Story = {
  args: {
    ...Default.args,
    size: 'lg',
    label: '載入大檔案...',
  },
};

// Color variations
export const GrayTheme: Story = {
  args: {
    ...Default.args,
    color: 'gray',
    label: '處理中...',
  },
};

export const GreenTheme: Story = {
  args: {
    ...Default.args,
    color: 'green',
    label: '儲存中...',
  },
};

// Layout variations
export const Centered: Story = {
  args: {
    ...Default.args,
    centered: true,
    label: '載入頁面內容...',
  },
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    Story => (
      <div style={{ height: '400px', width: '100%' }}>
        <Story />
      </div>
    ),
  ],
};

// Use case examples
export const APILoading: Story = {
  args: {
    label: '載入小說列表...',
    size: 'md',
    color: 'blue',
  },
};

export const BookContent: Story = {
  args: {
    label: '載入章節內容...',
    size: 'sm',
    color: 'gray',
  },
};

export const Saving: Story = {
  args: {
    label: '儲存閱讀進度...',
    size: 'sm',
    color: 'green',
  },
};

// Edge cases
export const NoLabel: Story = {
  args: {
    label: '',
    size: 'md',
    color: 'blue',
  },
};

export const LongLabel: Story = {
  args: {
    label: '正在載入非常長的內容，請耐心等待...',
    size: 'md',
    color: 'blue',
  },
};
