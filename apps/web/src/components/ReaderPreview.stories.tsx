import type { Meta, StoryObj } from '@storybook/react-webpack5';
import ReaderPreview from './ReaderPreview';
import { SettingsProvider } from '../contexts/SettingsContext';
import { CSSVariablesBridge } from './CSSVariablesBridge';

const meta: Meta<typeof ReaderPreview> = {
  title: 'Components/ReaderPreview',
  component: ReaderPreview,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
ReaderPreview 組件用於展示和測試 CSS 變數的效果。它使用了 ReaderSettings 提供的 CSS 變數來控制：

- 字體大小 (--reader-font-size)
- 行高 (--reader-line-height)
- 字體族 (--reader-font-family)
- 背景色 (--reader-bg-color)
- 文字色 (--reader-text-color)
- 邊框色 (--reader-border-color)

這個組件演示了如何在實際組件中使用這些 CSS 變數，並且可以配合設定面板進行即時預覽。
        `,
      },
    },
  },
  decorators: [
    Story => (
      <SettingsProvider>
        <CSSVariablesBridge />
        <div style={{ padding: '2rem', minHeight: '60vh' }}>
          <Story />
        </div>
      </SettingsProvider>
    ),
  ],
  argTypes: {
    title: {
      control: 'text',
      description: '章節標題',
    },
    content: {
      control: 'text',
      description: '章節內容',
    },
    className: {
      control: 'text',
      description: '自定義 CSS 類別',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const CustomContent: Story = {
  args: {
    title: '第二章：冒險開始',
    content: `在遙遠的東方，有一個被群山環繞的小村莊。

村莊裡住著一位年輕的劍客，他的名字叫做李明。從小就對武學有著濃厚興趣的他，每天都會在村後的竹林中練習劍法。

這一天，當他正在練習時，突然聽到了一陣急促的馬蹄聲...`,
  },
};

export const ShortContent: Story = {
  args: {
    title: '簡短範例',
    content: '這是一個簡短的內容範例，用於測試不同內容長度下的排版效果。',
  },
};
