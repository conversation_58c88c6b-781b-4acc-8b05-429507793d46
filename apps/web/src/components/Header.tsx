import React, { useState } from 'react';
import { useMobileMenu } from '../hooks/useMobileMenu';

export interface HeaderProps {
  /** 品牌名稱或標誌文字 */
  brand?: string;
  /** 是否顯示搜尋功能 */
  showSearch?: boolean;
  /** 是否顯示認證按鈕 */
  showAuth?: boolean;
  /** Header 視覺變體 */
  variant?: 'default' | 'transparent' | 'solid';
  /** 是否固定在頂部 */
  fixed?: boolean;
  /** 自定義 CSS 類別 */
  className?: string;
  /** 導航項目點擊回調 */
  onNavigate?: (path: string) => void;
  /** 搜尋提交回調 */
  onSearch?: (query: string) => void;
  /** 登入按鈕點擊回調 */
  onLogin?: () => void;
  /** 註冊按鈕點擊回調 */
  onRegister?: () => void;
}

// 導航項目配置
const navigationItems = [
  { label: '首頁', path: '/' },
  { label: '小說分類', path: '/categories' },
  { label: '排行榜', path: '/rankings' },
  { label: '最新更新', path: '/latest' },
  { label: '完本小說', path: '/completed' },
];

const Header: React.FC<HeaderProps> = ({
  brand = '小說閱讀',
  showSearch = true,
  showAuth = true,
  variant = 'default',
  fixed = true,
  className = '',
  onNavigate,
  onSearch,
  onLogin,
  onRegister,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const { isOpen, toggleMenu, closeMenu, menuRef, buttonRef } = useMobileMenu({
    closeOnOutsideClick: true,
    closeOnEscape: true,
    lockBodyScroll: true,
  });

  // 變體樣式配置
  const variantClasses = {
    default: 'bg-white border-b border-gray-200 shadow-sm',
    transparent: 'bg-white/80 backdrop-blur-md border-b border-white/20',
    solid: 'bg-gray-900 border-b border-gray-800',
  };

  // 文字顏色配置
  const textClasses = {
    default: 'text-gray-900',
    transparent: 'text-gray-900',
    solid: 'text-white',
  };

  // 組合 CSS 類別
  const headerClasses = [
    'w-full z-50 transition-all duration-200',
    fixed ? 'fixed top-0 left-0 right-0' : 'relative',
    variantClasses[variant],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // 處理導航項目點擊
  const handleNavigationClick = (path: string) => {
    closeMenu();
    onNavigate?.(path);
  };

  // 處理搜尋提交
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearch?.(searchQuery.trim());
      setSearchQuery('');
      closeMenu();
    }
  };

  // 處理認證按鈕點擊
  const handleAuthClick = (action: 'login' | 'register') => {
    closeMenu();
    if (action === 'login') {
      onLogin?.();
    } else {
      onRegister?.();
    }
  };

  return (
    <header className={headerClasses}>
      <nav
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        role="navigation"
        aria-label="主要導航"
      >
        <div className="flex justify-between items-center h-16">
          {/* 品牌標誌 */}
          <div className="flex-shrink-0">
            <button
              className={`text-xl font-bold ${textClasses[variant]} hover:opacity-80 transition-opacity focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1`}
              onClick={() => handleNavigationClick('/')}
              aria-label={`返回 ${brand} 首頁`}
            >
              {brand}
            </button>
          </div>

          {/* 桌面版導航 */}
          <div className="hidden lg:flex lg:items-center lg:space-x-8">
            {/* 導航連結 */}
            <div className="flex space-x-6">
              {navigationItems.map(item => (
                <button
                  key={item.path}
                  className={`${textClasses[variant]} hover:text-blue-600 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1`}
                  onClick={() => handleNavigationClick(item.path)}
                >
                  {item.label}
                </button>
              ))}
            </div>
          </div>

          {/* 桌面版右側功能 */}
          <div className="hidden lg:flex lg:items-center lg:space-x-4">
            {/* 搜尋框 */}
            {showSearch && (
              <form onSubmit={handleSearchSubmit} className="relative">
                <label htmlFor="desktop-search" className="sr-only">
                  搜尋小說
                </label>
                <input
                  id="desktop-search"
                  type="search"
                  placeholder="搜尋小說..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-64 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  aria-label="搜尋小說"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                  aria-label="執行搜尋"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </button>
              </form>
            )}

            {/* 認證按鈕 */}
            {showAuth && (
              <div className="flex space-x-3">
                <button
                  className={`${textClasses[variant]} border border-current px-4 py-2 rounded-lg hover:bg-current hover:text-white transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
                  onClick={() => handleAuthClick('login')}
                >
                  登入
                </button>
                <button
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  onClick={() => handleAuthClick('register')}
                >
                  註冊
                </button>
              </div>
            )}
          </div>

          {/* 行動版選單按鈕 */}
          <div className="lg:hidden">
            <button
              ref={buttonRef}
              className={`${textClasses[variant]} p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors`}
              onClick={toggleMenu}
              aria-expanded={isOpen}
              aria-controls="mobile-menu"
              aria-label={isOpen ? '關閉選單' : '開啟選單'}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* 行動版選單 */}
        {isOpen && (
          <div
            ref={menuRef}
            id="mobile-menu"
            className="lg:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="mobile-menu-button"
          >
            <div className="px-4 py-6 space-y-4">
              {/* 搜尋框 */}
              {showSearch && (
                <form onSubmit={handleSearchSubmit} className="mb-6">
                  <label htmlFor="mobile-search" className="sr-only">
                    搜尋小說
                  </label>
                  <div className="relative">
                    <input
                      id="mobile-search"
                      type="search"
                      placeholder="搜尋小說..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="搜尋小說"
                    />
                    <button
                      type="submit"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                      aria-label="執行搜尋"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                    </button>
                  </div>
                </form>
              )}

              {/* 導航連結 */}
              <div className="space-y-2">
                {navigationItems.map(item => (
                  <button
                    key={item.path}
                    className="block w-full text-left px-4 py-3 text-gray-900 hover:bg-gray-50 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={() => handleNavigationClick(item.path)}
                    role="menuitem"
                  >
                    {item.label}
                  </button>
                ))}
              </div>

              {/* 認證按鈕 */}
              {showAuth && (
                <div className="border-t border-gray-200 pt-4 space-y-3">
                  <button
                    className="w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={() => handleAuthClick('login')}
                  >
                    登入
                  </button>
                  <button
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={() => handleAuthClick('register')}
                  >
                    註冊
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;
