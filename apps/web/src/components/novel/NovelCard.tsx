import React from 'react';
import { Card, CardContent, CardMedia, Typography, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

interface NovelCardProps {
  id: string;
  title: string;
  author: string;
  cover: string;
  description: string;
}

const NovelCard: React.FC<NovelCardProps> = ({ id, title, author, cover, description }) => {
  return (
    <Card sx={{ maxWidth: 345, m: 2 }}>
      <CardMedia component="img" height="140" image={cover} alt={title} />
      <CardContent>
        <Typography gutterBottom variant="h5" component="div">
          {title}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          作者：{author}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {description}
        </Typography>
        <Button
          component={RouterLink}
          to={`/novel/${id}`}
          variant="contained"
          color="primary"
          fullWidth
        >
          閱讀更多
        </Button>
      </CardContent>
    </Card>
  );
};

export default NovelCard;
