/**
 * CSS Variables Bridge Component
 *
 * 將 ReaderSettings 的值動態應用為 CSS 自定義屬性（CSS Variables）
 * 這些變數可在全域 CSS 中使用，實現動態主題切換
 */

import { useEffect } from 'react';
import { useSettings } from '../hooks/useSettings';

/**
 * CSS 變數橋接組件
 *
 * 此組件不渲染任何 UI，僅負責：
 * 1. 監聽 ReaderSettings 變化
 * 2. 將設定值轉換為 CSS Variables
 * 3. 動態應用到 document.documentElement
 */
export const CSSVariablesBridge = () => {
  const { getCSSVariables, isLoading } = useSettings();

  useEffect(() => {
    // 等待設定載入完成後再應用 CSS Variables
    if (isLoading) {
      return;
    }

    const cssVariables = getCSSVariables();
    const documentElement = document.documentElement;

    // 動態設定 CSS 自定義屬性
    Object.entries(cssVariables).forEach(([property, value]) => {
      documentElement.style.setProperty(property, value);
    });

    // 清理函數 - 移除設定的 CSS 變數（可選）
    // 注意：通常不需要清理，因為新值會覆蓋舊值
    return () => {
      // 如果需要清理，可以在此處移除 CSS 變數
      // Object.keys(cssVariables).forEach((property) => {
      //   documentElement.style.removeProperty(property);
      // });
    };
  }, [getCSSVariables, isLoading]);

  // 此組件不渲染任何內容
  return null;
};

export default CSSVariablesBridge;
