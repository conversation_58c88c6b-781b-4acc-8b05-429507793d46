/**
 * SettingsPanel Component
 *
 * 閱讀器設定面板容器組件
 * 負責整合所有設定控制項並管理狀態
 */

import React from 'react';
import { useSettings } from '../hooks/useSettings';
import FontSizeSlider from './FontSizeSlider';

interface SettingsPanelProps {
  /** 是否顯示面板 */
  isOpen?: boolean;
  /** 關閉面板回調 */
  onClose?: () => void;
  /** 自定義 CSS 類別 */
  className?: string;
  /** 測試識別標記 */
  testId?: string;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen = true,
  onClose,
  className = '',
  testId = 'settings-panel',
}) => {
  const { settings, updateSetting, resetSettings, isDefaultSettings, constraints } = useSettings();

  if (!isOpen) {
    return null;
  }

  return (
    <div
      className={`settings-panel bg-white rounded-lg shadow-lg p-6 ${className}`}
      data-testid={testId}
    >
      {/* 面板標題 */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">閱讀設定</h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="關閉設定面板"
            data-testid={`${testId}-close`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>

      {/* 設定項目 */}
      <div className="space-y-6">
        {/* 字體大小設定 */}
        <div>
          <FontSizeSlider
            value={settings.fontSize}
            onChange={value => updateSetting('fontSize', value)}
            min={constraints.fontSize.min}
            max={constraints.fontSize.max}
            testId={`${testId}-font-size`}
          />
        </div>

        {/* 行高設定 (MVP 階段暫時隱藏) */}
        {/* <div>
          <LineHeightSlider
            value={settings.lineHeight}
            onChange={(value) => updateSetting('lineHeight', value)}
            min={constraints.lineHeight.min}
            max={constraints.lineHeight.max}
          />
        </div> */}

        {/* 主題選擇 (MVP 階段暫時隱藏) */}
        {/* <div>
          <ThemeSelector
            value={settings.theme}
            onChange={(value) => updateSetting('theme', value)}
          />
        </div> */}

        {/* 字體族選擇 (MVP 階段暫時隱藏) */}
        {/* <div>
          <FontFamilySelector
            value={settings.fontFamily}
            onChange={(value) => updateSetting('fontFamily', value)}
          />
        </div> */}
      </div>

      {/* 重置按鈕 */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <button
          onClick={resetSettings}
          disabled={isDefaultSettings}
          className={`
            w-full py-2 px-4 rounded-md text-sm font-medium transition-colors
            ${
              isDefaultSettings
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }
          `}
          data-testid={`${testId}-reset`}
        >
          重置為預設值
        </button>
      </div>
    </div>
  );
};

export default SettingsPanel;
