import React from 'react';
import AppRoutes from './routes';
import './index.css';
import { SettingsProvider } from './contexts/SettingsContext';
import { CSSVariablesBridge } from './components/CSSVariablesBridge';

// Test: Verify Turborepo remote caching in CI - Issue #144

const App: React.FC = () => {
  return (
    <SettingsProvider>
      <CSSVariablesBridge />
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm">
          <div className="max-w-6xl mx-auto px-4 py-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {process.env.REACT_APP_TITLE || '小說網站'}
            </h1>
          </div>
        </header>
        <main>
          <AppRoutes />
        </main>
      </div>
    </SettingsProvider>
  );
};

export default App;
// Test comment
