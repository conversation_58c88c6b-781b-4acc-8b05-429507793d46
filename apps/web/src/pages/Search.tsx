import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';
import { searchNovels } from '../services/api';

interface SearchResult {
  id: string;
  title: string;
  author: string;
  cover: string;
  description: string;
  status?: string;
  category?: string;
}

interface SearchFilters {
  category: string;
  status: string;
}

// 搜索歷史的類型定義
interface SearchHistoryItem {
  query: string;
  timestamp: number;
}

// Trie數據結構用於自動完成
class TrieNode {
  children: Map<string, TrieNode> = new Map();
  isEndOfWord: boolean = false;
  word: string = '';
}

class Trie {
  root: TrieNode = new TrieNode();

  insert(word: string) {
    let current = this.root;
    for (const char of word.toLowerCase()) {
      if (!current.children.has(char)) {
        current.children.set(char, new TrieNode());
      }
      current = current.children.get(char)!;
    }
    current.isEndOfWord = true;
    current.word = word;
  }

  search(prefix: string, maxResults: number = 5): string[] {
    const results: string[] = [];
    const current = this.findNode(prefix.toLowerCase());

    if (current) {
      this.dfs(current, results, maxResults);
    }

    return results;
  }

  private findNode(prefix: string): TrieNode | null {
    let current = this.root;
    for (const char of prefix) {
      if (!current.children.has(char)) {
        return null;
      }
      current = current.children.get(char)!;
    }
    return current;
  }

  private dfs(node: TrieNode, results: string[], maxResults: number) {
    if (results.length >= maxResults) return;

    if (node.isEndOfWord) {
      results.push(node.word);
    }

    for (const child of node.children.values()) {
      this.dfs(child, results, maxResults);
    }
  }
}

const Search: React.FC = () => {
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    category: '',
    status: '',
  });
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // 創建Trie實例
  const trie = useMemo(() => {
    const newTrie = new Trie();
    // 預設一些小說名稱用於自動完成
    const sampleTitles = [
      '斗羅大陸',
      '鬥破蒼穹',
      '完美世界',
      '一念永恆',
      '我欲封天',
      '仙逆',
      '星辰變',
      '盤龍',
      '吞噬星空',
      '武動乾坤',
      '大主宰',
      '元尊',
      '天蠶土豆',
      '唐家三少',
      '我吃西紅柿',
    ];
    sampleTitles.forEach(title => newTrie.insert(title));
    return newTrie;
  }, []);

  // 從localStorage加載搜索歷史
  useEffect(() => {
    const savedHistory = localStorage.getItem('searchHistory');
    if (savedHistory) {
      try {
        const parsedHistory = JSON.parse(savedHistory);
        setSearchHistory(parsedHistory.slice(0, 10)); // 只保留最近10條
      } catch (error) {
        console.error('Failed to parse search history:', error);
      }
    }
  }, []);

  // 保存搜索歷史到localStorage
  const saveSearchHistory = useCallback(
    (searchQuery: string) => {
      if (!searchQuery.trim()) return;

      const newItem: SearchHistoryItem = {
        query: searchQuery,
        timestamp: Date.now(),
      };

      const updatedHistory = [
        newItem,
        ...searchHistory.filter(item => item.query !== searchQuery),
      ].slice(0, 10);

      setSearchHistory(updatedHistory);
      localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
    },
    [searchHistory]
  );

  // 搜索執行函數
  const executeSearch = useCallback(
    async (searchQuery: string, searchFilters: SearchFilters) => {
      if (!searchQuery.trim()) {
        setResults([]);
        return;
      }

      setIsSearching(true);
      try {
        const data = await searchNovels(searchQuery);
        let filteredResults = data.novels || [];

        // 應用過濾器
        if (searchFilters.category) {
          filteredResults = filteredResults.filter(
            (novel: SearchResult) => novel.category === searchFilters.category
          );
        }
        if (searchFilters.status) {
          filteredResults = filteredResults.filter(
            (novel: SearchResult) => novel.status === searchFilters.status
          );
        }

        setResults(filteredResults);
        saveSearchHistory(searchQuery);
      } catch (err) {
        console.error('Search failed:', err);
        setResults([]);
      } finally {
        setIsSearching(false);
      }
    },
    [saveSearchHistory]
  );

  // 去抖動搜索函數
  const debouncedSearch = useMemo(() => debounce(executeSearch, 500), [executeSearch]);

  // 處理查詢變化
  const handleQueryChange = (value: string) => {
    setQuery(value);

    // 生成自動完成建議
    if (value.trim()) {
      const trieSuggestions = trie.search(value, 5);
      const historySuggestions = searchHistory
        .filter(item => item.query.toLowerCase().includes(value.toLowerCase()))
        .map(item => item.query)
        .slice(0, 3);

      const combinedSuggestions = [...new Set([...trieSuggestions, ...historySuggestions])].slice(
        0,
        5
      );
      setSuggestions(combinedSuggestions);
      setShowSuggestions(combinedSuggestions.length > 0);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }

    // 執行去抖動搜索
    debouncedSearch(value, filters);
  };

  // 處理建議選擇
  const handleSuggestionSelect = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    debouncedSearch(suggestion, filters);
  };

  // 處理過濾器變化
  const handleFilterChange = (filterType: keyof SearchFilters, value: string) => {
    const newFilters = { ...filters, [filterType]: value };
    setFilters(newFilters);
    if (query.trim()) {
      debouncedSearch(query, newFilters);
    }
  };

  // 清除搜索歷史
  const clearSearchHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  };

  return (
    <div className="max-w-4xl mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-6">搜尋小說</h1>

      {/* 搜索輸入區域 */}
      <div className="mb-8 relative">
        <div className="flex gap-2 mb-4">
          <div className="flex-1 relative">
            <input
              type="text"
              value={query}
              onChange={e => handleQueryChange(e.target.value)}
              onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              placeholder="輸入小說名稱、作者或關鍵字"
              className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />

            {/* 自動完成建議 */}
            {showSuggestions && suggestions.length > 0 && (
              <div className="absolute top-full left-0 right-0 bg-white border border-t-0 rounded-b-md shadow-lg z-10">
                {suggestions.map((suggestion, index) => (
                  <div
                    key={index}
                    className="p-2 hover:bg-gray-100 cursor-pointer text-sm"
                    onMouseDown={() => handleSuggestionSelect(suggestion)}
                  >
                    {suggestion}
                  </div>
                ))}
              </div>
            )}
          </div>

          <button
            type="button"
            disabled={isSearching}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
          >
            {isSearching ? '搜尋中...' : '搜尋'}
          </button>
        </div>

        {/* 搜索過濾器 */}
        <div className="flex gap-4 flex-wrap">
          <select
            value={filters.category}
            onChange={e => handleFilterChange('category', e.target.value)}
            className="p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有分類</option>
            <option value="fantasy">玄幻</option>
            <option value="cultivation">修真</option>
            <option value="urban">都市</option>
            <option value="romance">言情</option>
            <option value="historical">歷史</option>
          </select>

          <select
            value={filters.status}
            onChange={e => handleFilterChange('status', e.target.value)}
            className="p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有狀態</option>
            <option value="completed">已完結</option>
            <option value="ongoing">連載中</option>
            <option value="paused">暫停</option>
          </select>
        </div>
      </div>

      {/* 搜索歷史 */}
      {searchHistory.length > 0 && !query && (
        <div className="mb-6 p-4 bg-gray-50 rounded">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-semibold text-gray-700">搜索歷史</h3>
            <button
              onClick={clearSearchHistory}
              className="text-xs text-blue-500 hover:text-blue-700"
            >
              清除
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {searchHistory.slice(0, 5).map((item, index) => (
              <button
                key={index}
                onClick={() => handleQueryChange(item.query)}
                className="px-3 py-1 text-sm bg-white border rounded hover:bg-gray-100"
              >
                {item.query}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 搜索結果 */}
      {results.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {results.map(novel => (
            <div
              key={novel.id}
              className="flex gap-4 p-4 border rounded hover:shadow-md transition-shadow"
            >
              <img
                src={novel.cover || '/placeholder-book.png'}
                alt={novel.title}
                className="w-24 h-32 object-cover rounded"
                onError={e => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder-book.png';
                }}
              />
              <div className="flex-1">
                <h2 className="text-lg font-semibold mb-1">{novel.title}</h2>
                <p className="text-sm text-gray-600 mb-1">作者：{novel.author}</p>
                {novel.category && (
                  <p className="text-xs text-blue-600 mb-1">分類：{novel.category}</p>
                )}
                {novel.status && (
                  <p className="text-xs text-green-600 mb-2">狀態：{novel.status}</p>
                )}
                <p className="text-sm text-gray-700 line-clamp-2">{novel.description}</p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          {isSearching ? (
            <div className="flex justify-center items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600">搜尋中...</span>
            </div>
          ) : (
            <p className="text-gray-500">{query ? '沒有找到相關小說' : '請輸入搜尋關鍵字'}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default Search;
