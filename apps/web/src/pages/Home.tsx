import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { getNovelList } from '../services/api';
import { Novel } from '../types/novel';

export interface HomeProps {
  novels?: Array<{
    id: string;
    title: string;
    author: string;
    cover: string;
    description: string;
  }>;
}

const Home: React.FC<HomeProps> = () => {
  const [novels, setNovels] = useState<Novel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNovels = async () => {
      try {
        setLoading(true);
        const data = await getNovelList();
        setNovels(data.novels);
      } catch (err) {
        setError('無法載入小說列表');
        console.error('Error fetching novels:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchNovels();
  }, []);

  if (loading) return <div className="text-center p-4">載入中...</div>;
  if (error) return <div className="text-center text-red-500 p-4">{error}</div>;

  return (
    <div className="max-w-6xl mx-auto p-4">
      {novels.length > 0 ? (
        <div>
          <h2 className="text-2xl font-bold mb-4">最新小說</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {novels.map(novel => (
              <Link
                key={novel.id} // Keep using novel.id as key for React rendering if it's the unique PK
                to={`/novels/${novel.slug}`} // Use novel.slug for the route
                className="block p-4 bg-white rounded-lg shadow hover:shadow-lg transition-shadow"
              >
                <div className="flex gap-4">
                  <img
                    src={
                      novel.cover_url || novel.coverUrl || novel.coverImage || '/default-cover.jpg'
                    }
                    alt={novel.title}
                    className="w-20 h-28 object-cover rounded"
                  />
                  <div>
                    <h3 className="font-bold mb-1">{novel.title}</h3>
                    <p className="text-sm text-gray-600">作者：{novel.author}</p>
                    {novel.description && (
                      <p className="text-sm text-gray-500 mt-1 line-clamp-2">{novel.description}</p>
                    )}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      ) : (
        !loading && <p className="text-center">目前沒有小說。</p> // Show message if no novels and not loading
      )}
    </div>
  );
};

export default Home;
