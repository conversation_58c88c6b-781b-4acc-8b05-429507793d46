import React from 'react';
import { act } from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import Home from '../Home';
import * as api from '../../services/api';

// Mock the entire api module with a factory function
jest.mock('../../services/api', () => ({
  __esModule: true,
  getNovelList: jest.fn(),
}));

describe('<Home />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch and display novels on initial render', async () => {
    const mockNovels = [
      { id: '1', title: 'Novel 1', author: 'Author 1', cover: '', description: '' },
    ];
    (api.getNovelList as jest.Mock).mockResolvedValue({ novels: mockNovels, total: 1 });

    await act(async () => {
      render(
        <MemoryRouter>
          <Home />
        </MemoryRouter>
      );
    });

    expect(api.getNovelList).toHaveBeenCalledTimes(1);
    expect(api.getNovelList).toHaveBeenCalledWith();

    await act(async () => {
      expect(await screen.findByText('Novel 1')).toBeInTheDocument();
    });
  });

  it('should display loading state while fetching novels', () => {
    (api.getNovelList as jest.Mock).mockImplementation(() => new Promise(() => {}));

    render(
      <MemoryRouter>
        <Home />
      </MemoryRouter>
    );

    expect(screen.getByText('載入中...')).toBeInTheDocument();
  });
});
