/**
 * Reader Demo Page
 *
 * 示範如何整合 ReaderSettings 到實際的閱讀頁面
 * 這是一個完整的集成範例
 */

import React, { useState } from 'react';
import Layout from '../components/Layout';
import Header from '../components/Header';
import SettingsPanel from '../components/SettingsPanel';
// Using inline SVG icon to avoid react-icons TypeScript issues

const ReaderDemo: React.FC = () => {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // 示例章節內容
  const chapterContent = {
    title: '第一章：旅程的開始',
    content: `晨曦初現，東方的天際泛起一抹魚肚白。李雲站在山頂，眺望著遠方連綿起伏的群山。晨風拂過他的面頰，帶來一絲涼意。

他深吸一口氣，感受著清晨特有的清新空氣。今天，是他踏上修仙之路的第一天。作為青雲門的新入門弟子，他心中既有期待，也有些許不安。

"師弟，該走了。"身後傳來一個溫和的聲音。李雲回過頭，看到他的師兄王陽正微笑著看著他。王陽是外門弟子中的佼佼者，為人和善，深受同門喜愛。

"是，師兄。"李雲點點頭，最後看了一眼山下的村莊。那裡，是他生活了十六年的地方。如今，他即將開始一段全新的人生旅程。

兩人並肩走在山間小道上，朝陽的光芒透過樹葉的縫隙灑在地上，形成斑駁的光影。山林中不時傳來鳥鳴聲，為這寧靜的清晨增添了幾分生機。

"師弟不必緊張，"王陽似乎看出了李雲的心思，"青雲門雖然規矩森嚴，但對待弟子還是很寬厚的。只要你勤奮修煉，假以時日，必能有所成就。"

李雲感激地看了師兄一眼："多謝師兄指點。"

隨著太陽逐漸升高，前方的道路也變得越來越寬闊。遠處，青雲門的山門已經隱約可見。那巍峨的建築群依山而建，在陽光下顯得格外壯觀。

這一刻，李雲的心中充滿了對未來的憧憬。他知道，真正的挑戰才剛剛開始...`,
  };

  return (
    <>
      <Header />

      <Layout maxWidth="reading" padding="md">
        <div className="min-h-screen">
          {/* 工具欄 */}
          <div className="sticky top-16 z-30 bg-white border-b border-gray-200 px-4 py-2">
            <div className="flex items-center justify-between max-w-4xl mx-auto">
              <h1 className="text-lg font-medium text-gray-900">{chapterContent.title}</h1>

              <button
                onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                aria-label="開啟設定"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" />
                </svg>
              </button>
            </div>
          </div>

          {/* 主要內容區域 */}
          <div className="flex gap-8 mt-8">
            {/* 閱讀內容 */}
            <article
              className="flex-1 prose prose-gray max-w-none"
              style={{
                // 應用 CSS 變數
                fontSize: 'var(--reader-font-size, 16px)',
                lineHeight: 'var(--reader-line-height, 1.6)',
                fontFamily: 'var(--reader-font-family, serif)',
                color: 'var(--reader-text-color, #1f2937)',
              }}
            >
              <div
                className="px-8 py-12 rounded-lg"
                style={{
                  backgroundColor: 'var(--reader-bg-color, #ffffff)',
                  borderColor: 'var(--reader-border-color, #e5e7eb)',
                  borderWidth: '1px',
                  borderStyle: 'solid',
                }}
              >
                <h1 className="text-3xl font-bold mb-8" style={{ color: 'inherit' }}>
                  {chapterContent.title}
                </h1>

                <div className="whitespace-pre-line">{chapterContent.content}</div>

                {/* 章節導航 */}
                <div
                  className="flex justify-between items-center mt-12 pt-8 border-t"
                  style={{ borderColor: 'var(--reader-border-color, #e5e7eb)' }}
                >
                  <button
                    className="px-4 py-2 text-sm rounded-lg transition-colors"
                    style={{
                      backgroundColor: 'var(--reader-bg-color, #ffffff)',
                      color: 'var(--reader-text-color, #1f2937)',
                      border: '1px solid var(--reader-border-color, #e5e7eb)',
                    }}
                    disabled
                  >
                    上一章
                  </button>

                  <span className="text-sm" style={{ color: 'var(--reader-text-color, #1f2937)' }}>
                    第 1 / 100 章
                  </span>

                  <button
                    className="px-4 py-2 text-sm rounded-lg transition-colors hover:opacity-80"
                    style={{
                      backgroundColor: 'var(--reader-text-color, #1f2937)',
                      color: 'var(--reader-bg-color, #ffffff)',
                    }}
                  >
                    下一章
                  </button>
                </div>
              </div>
            </article>

            {/* 設定面板 - 桌面版側邊欄 */}
            <aside
              className={`hidden lg:block transition-all duration-300 ${isSettingsOpen ? 'w-80' : 'w-0'}`}
            >
              {isSettingsOpen && (
                <div className="sticky top-32">
                  <SettingsPanel isOpen={true} onClose={() => setIsSettingsOpen(false)} />
                </div>
              )}
            </aside>
          </div>
        </div>

        {/* 設定面板 - 行動版覆蓋 */}
        {isSettingsOpen && (
          <div className="lg:hidden">
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsSettingsOpen(false)}
            />

            <div className="fixed inset-x-0 bottom-0 z-50">
              <SettingsPanel
                isOpen={true}
                onClose={() => setIsSettingsOpen(false)}
                className="rounded-t-xl"
              />
            </div>
          </div>
        )}
      </Layout>
    </>
  );
};

export default ReaderDemo;
