// Jest DOM type definitions for testing
// This file ensures TypeScript recognizes jest-dom matchers

import '@testing-library/jest-dom';

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(...classNames: string[]): R;
      toHaveAttribute(attr: string, value?: string): R;
      toHaveTextContent(text: string | RegExp): R;
      toHaveValue(value: string | number): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeInvalid(): R;
      toBeValid(): R;
      toBeVisible(): R;
      toBeChecked(): R;
      toHaveFocus(): R;
      toHaveStyle(css: string | object): R;
      toContainElement(element: HTMLElement | null): R;
      toContainHTML(htmlText: string): R;
      toHaveDisplayValue(value: string | RegExp | (string | RegExp)[]): R;
      toHaveFormValues(expectedValues: Record<string, any>): R;
      toHaveLength(length: number): R;
      toHaveProperty(property: string, value?: any): R;
    }
  }
}
