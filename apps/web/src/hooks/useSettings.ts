/**
 * useSettings Hook
 *
 * 便捷的設定管理 Hook，重新導出 SettingsContext 的 useSettings
 * 提供額外的輔助函數和計算屬性
 */

import { useSettings as useSettingsContext } from '../contexts/SettingsContext';
import { ReaderSettings, SETTINGS_CONSTRAINTS } from '../types/reader-settings';

/**
 * 擴展的設定 Hook 介面
 */
export interface UseSettingsReturn extends ReturnType<typeof useSettingsContext> {
  /** 是否為預設設定 */
  isDefaultSettings: boolean;
  /** 設定約束條件 */
  constraints: typeof SETTINGS_CONSTRAINTS;
  /** 取得 CSS 變數物件 */
  getCSSVariables: () => Record<string, string>;
}

/**
 * 主要的設定管理 Hook
 *
 * @returns 設定狀態和操作函數
 */
export const useSettings = (): UseSettingsReturn => {
  const contextValue = useSettingsContext();
  const { settings } = contextValue;

  /**
   * 檢查是否為預設設定
   */
  const isDefaultSettings =
    settings.fontSize === 16 &&
    settings.lineHeight === 1.6 &&
    settings.theme === 'light' &&
    settings.fontFamily === 'serif';

  /**
   * 產生 CSS 變數物件
   * 這些變數可以直接應用到元素的 style 屬性
   */
  const getCSSVariables = (): Record<string, string> => ({
    '--reader-font-size': `${settings.fontSize}px`,
    '--reader-line-height': settings.lineHeight.toString(),
    '--reader-font-family': getFontFamilyValue(settings.fontFamily),
    ...getThemeVariables(settings.theme),
  });

  return {
    ...contextValue,
    isDefaultSettings,
    constraints: SETTINGS_CONSTRAINTS,
    getCSSVariables,
  };
};

/**
 * 取得字體族的 CSS 值
 */
const getFontFamilyValue = (fontFamily: ReaderSettings['fontFamily']): string => {
  switch (fontFamily) {
    case 'serif':
      return 'Georgia, "Times New Roman", Times, serif';
    case 'sans-serif':
      return 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    default:
      return 'Georgia, "Times New Roman", Times, serif';
  }
};

/**
 * 取得主題相關的 CSS 變數
 */
const getThemeVariables = (theme: ReaderSettings['theme']): Record<string, string> => {
  switch (theme) {
    case 'light':
      return {
        '--reader-bg-color': '#ffffff',
        '--reader-text-color': '#1f2937',
        '--reader-border-color': '#e5e7eb',
      };
    case 'dark':
      return {
        '--reader-bg-color': '#111827',
        '--reader-text-color': '#f9fafb',
        '--reader-border-color': '#374151',
      };
    case 'sepia':
      return {
        '--reader-bg-color': '#f7f3e9',
        '--reader-text-color': '#5d4e37',
        '--reader-border-color': '#d4c5a9',
      };
    default:
      return getThemeVariables('light');
  }
};

export default useSettings;
