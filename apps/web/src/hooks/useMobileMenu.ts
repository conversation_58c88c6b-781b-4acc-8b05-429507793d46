import { useState, useEffect, useCallback, useRef } from 'react';

export interface UseMobileMenuReturn {
  /** 選單是否開啟 */
  isOpen: boolean;
  /** 切換選單狀態 */
  toggleMenu: () => void;
  /** 關閉選單 */
  closeMenu: () => void;
  /** 開啟選單 */
  openMenu: () => void;
  /** 選單容器 ref (用於外部點擊檢測) */
  menuRef: React.RefObject<HTMLDivElement>;
  /** 觸發按鈕 ref (用於焦點管理) */
  buttonRef: React.RefObject<HTMLButtonElement>;
}

export interface UseMobileMenuOptions {
  /** 初始開啟狀態 */
  initialOpen?: boolean;
  /** 是否啟用外部點擊關閉 */
  closeOnOutsideClick?: boolean;
  /** 是否啟用 Escape 鍵關閉 */
  closeOnEscape?: boolean;
  /** 是否鎖定背景滾動 */
  lockBodyScroll?: boolean;
  /** 選單開啟時的回調 */
  onOpen?: () => void;
  /** 選單關閉時的回調 */
  onClose?: () => void;
}

/**
 * 行動端選單狀態管理 Hook
 *
 * 提供完整的行動端選單功能，包括：
 * - 狀態管理
 * - 鍵盤導航支援
 * - 外部點擊關閉
 * - 背景滾動鎖定
 * - 無障礙支援
 */
export const useMobileMenu = (options: UseMobileMenuOptions = {}): UseMobileMenuReturn => {
  const {
    initialOpen = false,
    closeOnOutsideClick = true,
    closeOnEscape = true,
    lockBodyScroll = true,
    onOpen,
    onClose,
  } = options;

  const [isOpen, setIsOpen] = useState(initialOpen);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // 開啟選單
  const openMenu = useCallback(() => {
    setIsOpen(true);
    onOpen?.();
  }, [onOpen]);

  // 關閉選單
  const closeMenu = useCallback(() => {
    setIsOpen(false);
    onClose?.();

    // 將焦點返回到觸發按鈕
    if (buttonRef.current) {
      buttonRef.current.focus();
    }
  }, [onClose]);

  // 切換選單狀態
  const toggleMenu = useCallback(() => {
    if (isOpen) {
      closeMenu();
    } else {
      openMenu();
    }
  }, [isOpen, closeMenu, openMenu]);

  // 背景滾動鎖定
  useEffect(() => {
    if (!lockBodyScroll) return;

    if (isOpen) {
      // 記錄當前滾動位置
      const scrollY = window.scrollY;
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
    } else {
      // 恢復滾動位置
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';

      if (scrollY) {
        const scrollPosition = parseInt(scrollY.replace('px', '')) * -1;
        window.scrollTo(0, scrollPosition);
      }
    }

    // 清理函數
    return () => {
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
    };
  }, [isOpen, lockBodyScroll]);

  // Escape 鍵監聽
  useEffect(() => {
    if (!closeOnEscape) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeMenu();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, closeMenu]);

  // 外部點擊監聽
  useEffect(() => {
    if (!closeOnOutsideClick) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (!isOpen) return;

      const target = event.target as Node;
      const isOutsideMenu = menuRef.current && !menuRef.current.contains(target);
      const isOutsideButton = buttonRef.current && !buttonRef.current.contains(target);

      if (isOutsideMenu && isOutsideButton) {
        closeMenu();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, closeOnOutsideClick, closeMenu]);

  return {
    isOpen,
    toggleMenu,
    closeMenu,
    openMenu,
    menuRef,
    buttonRef,
  };
};
