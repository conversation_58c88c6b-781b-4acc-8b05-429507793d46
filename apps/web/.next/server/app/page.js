(() => {
  var e = {};
  (e.id = 974),
    (e.ids = [974]),
    (e.modules = {
      846: e => {
        'use strict';
        e.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js');
      },
      3033: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/work-unit-async-storage.external.js');
      },
      3295: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/after-task-async-storage.external.js');
      },
      3362: (e, t, r) => {
        Promise.resolve().then(r.t.bind(r, 477, 23)),
          Promise.resolve().then(r.t.bind(r, 7197, 23)),
          Promise.resolve().then(r.t.bind(r, 5301, 23)),
          Promise.resolve().then(r.t.bind(r, 4852, 23)),
          Promise.resolve().then(r.t.bind(r, 252, 23)),
          Promise.resolve().then(r.t.bind(r, 8640, 23)),
          Promise.resolve().then(r.t.bind(r, 254, 23)),
          Promise.resolve().then(r.t.bind(r, 2024, 23));
      },
      3587: (e, t, r) => {
        'use strict';
        r.r(t),
          r.d(t, {
            GlobalError: () => i.a,
            __next_app__: () => u,
            pages: () => p,
            routeModule: () => c,
            tree: () => l,
          });
        var s = r(8602),
          n = r(669),
          o = r(5301),
          i = r.n(o),
          a = r(2266),
          d = {};
        for (let e in a)
          0 >
            ['default', 'tree', 'pages', 'GlobalError', '__next_app__', 'routeModule'].indexOf(e) &&
            (d[e] = () => a[e]);
        r.d(t, d);
        let l = [
            '',
            {
              children: [
                '__PAGE__',
                {},
                {
                  page: [
                    () => Promise.resolve().then(r.bind(r, 5331)),
                    '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/page.tsx',
                  ],
                },
              ],
            },
            {
              layout: [
                () => Promise.resolve().then(r.bind(r, 9692)),
                '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/layout.tsx',
              ],
              'not-found': [
                () => Promise.resolve().then(r.t.bind(r, 9419, 23)),
                'next/dist/client/components/not-found-error',
              ],
              forbidden: [
                () => Promise.resolve().then(r.t.bind(r, 4470, 23)),
                'next/dist/client/components/forbidden-error',
              ],
              unauthorized: [
                () => Promise.resolve().then(r.t.bind(r, 4636, 23)),
                'next/dist/client/components/unauthorized-error',
              ],
            },
          ],
          p = ['/Users/<USER>/Documents/project/NovelWebsite/frontend/app/page.tsx'],
          u = { require: r, loadChunk: () => Promise.resolve() },
          c = new s.AppPageRouteModule({
            definition: {
              kind: n.RouteKind.APP_PAGE,
              page: '/page',
              pathname: '/',
              bundlePath: '',
              filename: '',
              appPaths: [],
            },
            userland: { loaderTree: l },
          });
      },
      3873: e => {
        'use strict';
        e.exports = require('path');
      },
      4133: (e, t, r) => {
        Promise.resolve().then(r.t.bind(r, 751, 23)),
          Promise.resolve().then(r.t.bind(r, 6483, 23)),
          Promise.resolve().then(r.t.bind(r, 6631, 23)),
          Promise.resolve().then(r.t.bind(r, 9638, 23)),
          Promise.resolve().then(r.t.bind(r, 7618, 23)),
          Promise.resolve().then(r.t.bind(r, 8454, 23)),
          Promise.resolve().then(r.t.bind(r, 5780, 23)),
          Promise.resolve().then(r.t.bind(r, 6454, 23));
      },
      4837: () => {},
      5331: (e, t, r) => {
        'use strict';
        r.r(t), r.d(t, { default: () => s });
        let s = (0, r(4618).registerClientReference)(
          function () {
            throw Error(
              'Attempted to call the default export of "/Users/<USER>/Documents/project/NovelWebsite/frontend/app/page.tsx" from the server, but it\'s on the client. It\'s not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.'
            );
          },
          '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/page.tsx',
          'default'
        );
      },
      5485: (e, t, r) => {
        Promise.resolve().then(r.bind(r, 5331));
      },
      6297: () => {},
      6925: (e, t, r) => {
        Promise.resolve().then(r.bind(r, 7613));
      },
      7613: (e, t, r) => {
        'use strict';
        r.r(t), r.d(t, { default: () => n });
        var s = r(1640);
        function n() {
          return (0, s.jsx)('main', {
            className: 'flex min-h-screen items-center justify-center bg-gray-100',
            children: (0, s.jsx)('h1', {
              className: 'text-2xl font-bold',
              children: 'Welcome to Next.js',
            }),
          });
        }
      },
      9121: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/action-async-storage.external.js');
      },
      9294: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/work-async-storage.external.js');
      },
      9692: (e, t, r) => {
        'use strict';
        r.r(t), r.d(t, { default: () => a, metadata: () => i });
        var s = r(4234),
          n = r(281),
          o = r.n(n);
        r(4837);
        let i = {
          title: 'NovelWebsite - Next.js',
          description: 'A modern novel reading platform.',
        };
        function a({ children: e }) {
          return (0, s.jsx)('html', {
            lang: 'en',
            children: (0, s.jsx)('body', { className: o().className, children: e }),
          });
        }
      },
      9849: () => {},
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    s = t.X(0, [387], () => r(3587));
  module.exports = s;
})();
