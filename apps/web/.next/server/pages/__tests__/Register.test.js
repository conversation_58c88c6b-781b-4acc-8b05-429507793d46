'use strict';
(() => {
  var e = {};
  (e.id = 469),
    (e.ids = [220, 469]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return a;
          },
        });
        var a = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, a) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return o;
            },
          });
        let r = a(8752),
          s = a(6351),
          n = r._(a(2015)),
          i = a(5310);
        async function l(e) {
          let { Component: t, ctx: a } = e;
          return { pageProps: await (0, i.loadGetInitialProps)(t, a) };
        }
        class o extends n.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, s.jsx)(e, { ...t });
          }
        }
        (o.origGetInitialProps = l),
          (o.getInitialProps = l),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      4563: (e, t, a) => {
        a.r(t), a.d(t, { default: () => n });
        var r = a(6351),
          s = a(2015);
        let n = ({ onRegister: e }) => {
          let [t, a] = (0, s.useState)(''),
            [n, i] = (0, s.useState)(''),
            [l, o] = (0, s.useState)(''),
            [c, u] = (0, s.useState)(!1),
            d = async a => {
              if ((a.preventDefault(), !(l.length < 6) && e && t && n && l)) {
                u(!0);
                try {
                  await e(t, n, l);
                } catch (e) {
                  console.error('Registration failed:', e);
                } finally {
                  u(!1);
                }
              }
            };
          return (0, r.jsxs)('div', {
            className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
            children: [
              (0, r.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '註冊' }),
              (0, r.jsxs)('form', {
                onSubmit: d,
                className: 'space-y-4',
                children: [
                  (0, r.jsxs)('div', {
                    children: [
                      (0, r.jsx)('label', {
                        htmlFor: 'username',
                        className: 'block text-sm font-medium text-gray-700 mb-1',
                        children: '用戶名',
                      }),
                      (0, r.jsx)('input', {
                        type: 'text',
                        id: 'username',
                        value: t,
                        onChange: e => a(e.target.value),
                        required: !0,
                        className:
                          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                        placeholder: '請輸入用戶名',
                      }),
                    ],
                  }),
                  (0, r.jsxs)('div', {
                    children: [
                      (0, r.jsx)('label', {
                        htmlFor: 'email',
                        className: 'block text-sm font-medium text-gray-700 mb-1',
                        children: '電子郵件',
                      }),
                      (0, r.jsx)('input', {
                        type: 'email',
                        id: 'email',
                        value: n,
                        onChange: e => i(e.target.value),
                        required: !0,
                        className:
                          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                        placeholder: '請輸入電子郵件',
                      }),
                    ],
                  }),
                  (0, r.jsxs)('div', {
                    children: [
                      (0, r.jsx)('label', {
                        htmlFor: 'password',
                        className: 'block text-sm font-medium text-gray-700 mb-1',
                        children: '密碼',
                      }),
                      (0, r.jsx)('input', {
                        type: 'password',
                        id: 'password',
                        value: l,
                        onChange: e => o(e.target.value),
                        required: !0,
                        minLength: 6,
                        className:
                          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                        placeholder: '請輸入密碼（至少6位）',
                      }),
                    ],
                  }),
                  (0, r.jsx)('button', {
                    type: 'submit',
                    disabled: c || !t || !n || !l,
                    className:
                      'w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors',
                    children: c ? '註冊中...' : '註冊',
                  }),
                ],
              }),
            ],
          });
        };
      },
      4912: (e, t, a) => {
        a.r(t),
          a.d(t, {
            config: () => P,
            default: () => f,
            getServerSideProps: () => h,
            getStaticPaths: () => v,
            getStaticProps: () => w,
            reportWebVitals: () => B,
            routeModule: () => L,
            unstable_getServerProps: () => R,
            unstable_getServerSideProps: () => T,
            unstable_getStaticParams: () => S,
            unstable_getStaticPaths: () => _,
            unstable_getStaticProps: () => j,
          });
        var r = {};
        a.r(r);
        var s = a(718),
          n = a(208),
          i = a(8732),
          l = a(5488),
          o = a.n(l),
          c = a(752),
          u = a.n(c),
          d = a(6351),
          p = a(2015),
          g = a(5270);
        let b = require('@testing-library/user-event');
        var m = a.n(b),
          y = a(4563);
        let x = jest.fn(() => Promise.resolve());
        describe('<Register />', () => {
          beforeEach(() => {
            jest.clearAllMocks();
          }),
            it('renders initial state with disabled submit', () => {
              (0, g.render)((0, d.jsx)(y.default, { onRegister: x })),
                expect(g.screen.getByLabelText('用戶名')).toHaveValue(''),
                expect(g.screen.getByLabelText('電子郵件')).toHaveValue(''),
                expect(g.screen.getByLabelText('密碼')).toHaveValue(''),
                expect(g.screen.getByRole('button', { name: '註冊' })).toBeDisabled();
            }),
            it('shows native validation messages for invalid email', async () => {
              (0, g.render)((0, d.jsx)(y.default, { onRegister: x }));
              let e = g.screen.getByLabelText('電子郵件');
              await (0, p.act)(async () => {
                await m().type(e, 'invalid'), await m().tab();
              }),
                expect(e).toBeInvalid();
            }),
            it('shows validation messages when submitting invalid form', async () => {
              (0, g.render)((0, d.jsx)(y.default, { onRegister: x }));
              let e = g.screen.getByRole('button', { name: '註冊' }),
                t = g.screen.getByLabelText('電子郵件');
              g.fireEvent.click(e),
                expect(t.validationMessage).not.toBe(''),
                expect(x).not.toHaveBeenCalled();
            }),
            it('calls onRegister with form values on success', async () => {
              await (0, p.act)(async () => {
                (0, g.render)((0, d.jsx)(y.default, { onRegister: x }));
              }),
                await (0, p.act)(async () => {
                  await m().type(g.screen.getByLabelText('用戶名'), 'tester'),
                    await m().type(g.screen.getByLabelText('電子郵件'), '<EMAIL>'),
                    await m().type(g.screen.getByLabelText('密碼'), '123456');
                });
              let e = g.screen.getByRole('button', { name: '註冊' });
              expect(e).toBeEnabled(),
                await (0, p.act)(async () => {
                  await m().click(e);
                }),
                expect(x).toHaveBeenCalledWith('tester', '<EMAIL>', '123456');
            }),
            it('sets password minimum length requirement', () => {
              (0, g.render)((0, d.jsx)(y.default, { onRegister: x })),
                expect(g.screen.getByLabelText('密碼')).toHaveAttribute('minLength', '6');
            }),
            it('prevents submission with too short password', async () => {
              (0, g.render)((0, d.jsx)(y.default, { onRegister: x })),
                await (0, p.act)(async () => {
                  await m().type(g.screen.getByLabelText('用戶名'), 'tester'),
                    await m().type(g.screen.getByLabelText('電子郵件'), '<EMAIL>');
                  let e = g.screen.getByLabelText('密碼');
                  await m().type(e, '123'), e.setCustomValidity('too short'), await m().tab();
                }),
                expect(g.screen.getByLabelText('密碼')).toBeInvalid(),
                await (0, p.act)(async () => {
                  await m().click(g.screen.getByRole('button', { name: '註冊' }));
                }),
                expect(x).not.toHaveBeenCalled();
            }),
            it('shows loading state during registration attempt and button text changes', async () => {
              let e = () => {},
                t = jest.fn(
                  () =>
                    new Promise(t => {
                      e = t;
                    })
                );
              await (0, p.act)(async () => {
                (0, g.render)((0, d.jsx)(y.default, { onRegister: t }));
              }),
                await (0, p.act)(async () => {
                  await m().type(g.screen.getByLabelText('用戶名'), 'tester'),
                    await m().type(g.screen.getByLabelText('電子郵件'), '<EMAIL>'),
                    await m().type(g.screen.getByLabelText('密碼'), '123456');
                });
              let a = g.screen.getByRole('button', { name: '註冊' });
              expect(a).toBeEnabled(),
                await (0, p.act)(async () => {
                  await m().click(a);
                }),
                expect(t).toHaveBeenCalledTimes(1),
                expect(a).toBeDisabled(),
                expect(a).toHaveTextContent('註冊中...'),
                await (0, p.act)(async () => {
                  e();
                }),
                await g.screen.findByRole('button', { name: '註冊' });
            }),
            it('re-enables submit button after registration error', async () => {
              let e = jest.fn(() => Promise.reject(Error('fail'))),
                t = jest.spyOn(console, 'error').mockImplementation(() => {});
              await (0, p.act)(async () => {
                (0, g.render)((0, d.jsx)(y.default, { onRegister: e }));
              }),
                await (0, p.act)(async () => {
                  await m().type(g.screen.getByLabelText('用戶名'), 'tester'),
                    await m().type(g.screen.getByLabelText('電子郵件'), '<EMAIL>'),
                    await m().type(g.screen.getByLabelText('密碼'), '123456');
                }),
                await (0, p.act)(async () => {
                  await m().click(g.screen.getByRole('button', { name: '註冊' }));
                }),
                await (0, p.act)(async () => {
                  await g.screen.findByRole('button', { name: '註冊' });
                }),
                expect(g.screen.getByRole('button', { name: '註冊' })).toBeEnabled(),
                expect(t).toHaveBeenCalled(),
                t.mockRestore();
            });
        });
        let f = (0, i.M)(r, 'default'),
          w = (0, i.M)(r, 'getStaticProps'),
          v = (0, i.M)(r, 'getStaticPaths'),
          h = (0, i.M)(r, 'getServerSideProps'),
          P = (0, i.M)(r, 'config'),
          B = (0, i.M)(r, 'reportWebVitals'),
          j = (0, i.M)(r, 'unstable_getStaticProps'),
          _ = (0, i.M)(r, 'unstable_getStaticPaths'),
          S = (0, i.M)(r, 'unstable_getStaticParams'),
          R = (0, i.M)(r, 'unstable_getServerProps'),
          T = (0, i.M)(r, 'unstable_getServerSideProps'),
          L = new s.PagesRouteModule({
            definition: {
              kind: n.A.PAGES,
              page: '/__tests__/Register.test',
              pathname: '/__tests__/Register.test',
              bundlePath: '',
              filename: '',
            },
            components: { App: u(), Document: o() },
            userland: r,
          });
      },
      5270: e => {
        e.exports = require('@testing-library/react');
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, a) {
              return a in t
                ? t[a]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, a))
                  : 'function' == typeof t && 'default' === a
                    ? t
                    : void 0;
            };
          },
        });
      },
    });
  var t = require('../../webpack-runtime.js');
  t.C(e);
  var a = e => t((t.s = e)),
    r = t.X(0, [488], () => a(4912));
  module.exports = r;
})();
