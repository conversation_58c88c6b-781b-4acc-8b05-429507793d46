'use strict';
(() => {
  var e = {};
  (e.id = 347),
    (e.ids = [220, 347]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return i;
            },
          });
        let a = r(8752),
          s = r(6351),
          n = a._(r(2015)),
          l = r(5310);
        async function o(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, l.loadGetInitialProps)(t, r) };
        }
        class i extends n.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, s.jsx)(e, { ...t });
          }
        }
        (i.origGetInitialProps = o),
          (i.getInitialProps = o),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      4563: (e, t, r) => {
        r.r(t), r.d(t, { default: () => n });
        var a = r(6351),
          s = r(2015);
        let n = ({ onRegister: e }) => {
          let [t, r] = (0, s.useState)(''),
            [n, l] = (0, s.useState)(''),
            [o, i] = (0, s.useState)(''),
            [u, d] = (0, s.useState)(!1),
            c = async r => {
              if ((r.preventDefault(), !(o.length < 6) && e && t && n && o)) {
                d(!0);
                try {
                  await e(t, n, o);
                } catch (e) {
                  console.error('Registration failed:', e);
                } finally {
                  d(!1);
                }
              }
            };
          return (0, a.jsxs)('div', {
            className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
            children: [
              (0, a.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '註冊' }),
              (0, a.jsxs)('form', {
                onSubmit: c,
                className: 'space-y-4',
                children: [
                  (0, a.jsxs)('div', {
                    children: [
                      (0, a.jsx)('label', {
                        htmlFor: 'username',
                        className: 'block text-sm font-medium text-gray-700 mb-1',
                        children: '用戶名',
                      }),
                      (0, a.jsx)('input', {
                        type: 'text',
                        id: 'username',
                        value: t,
                        onChange: e => r(e.target.value),
                        required: !0,
                        className:
                          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                        placeholder: '請輸入用戶名',
                      }),
                    ],
                  }),
                  (0, a.jsxs)('div', {
                    children: [
                      (0, a.jsx)('label', {
                        htmlFor: 'email',
                        className: 'block text-sm font-medium text-gray-700 mb-1',
                        children: '電子郵件',
                      }),
                      (0, a.jsx)('input', {
                        type: 'email',
                        id: 'email',
                        value: n,
                        onChange: e => l(e.target.value),
                        required: !0,
                        className:
                          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                        placeholder: '請輸入電子郵件',
                      }),
                    ],
                  }),
                  (0, a.jsxs)('div', {
                    children: [
                      (0, a.jsx)('label', {
                        htmlFor: 'password',
                        className: 'block text-sm font-medium text-gray-700 mb-1',
                        children: '密碼',
                      }),
                      (0, a.jsx)('input', {
                        type: 'password',
                        id: 'password',
                        value: o,
                        onChange: e => i(e.target.value),
                        required: !0,
                        minLength: 6,
                        className:
                          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                        placeholder: '請輸入密碼（至少6位）',
                      }),
                    ],
                  }),
                  (0, a.jsx)('button', {
                    type: 'submit',
                    disabled: u || !t || !n || !o,
                    className:
                      'w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors',
                    children: u ? '註冊中...' : '註冊',
                  }),
                ],
              }),
            ],
          });
        };
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
      9206: (e, t, r) => {
        r.r(t),
          r.d(t, {
            config: () => f,
            default: () => c,
            getServerSideProps: () => g,
            getStaticPaths: () => m,
            getStaticProps: () => p,
            reportWebVitals: () => b,
            routeModule: () => y,
            unstable_getServerProps: () => S,
            unstable_getServerSideProps: () => v,
            unstable_getStaticParams: () => h,
            unstable_getStaticPaths: () => x,
            unstable_getStaticProps: () => P,
          });
        var a = r(718),
          s = r(208),
          n = r(8732),
          l = r(5488),
          o = r.n(l),
          i = r(752),
          u = r.n(i),
          d = r(4563);
        let c = (0, n.M)(d, 'default'),
          p = (0, n.M)(d, 'getStaticProps'),
          m = (0, n.M)(d, 'getStaticPaths'),
          g = (0, n.M)(d, 'getServerSideProps'),
          f = (0, n.M)(d, 'config'),
          b = (0, n.M)(d, 'reportWebVitals'),
          P = (0, n.M)(d, 'unstable_getStaticProps'),
          x = (0, n.M)(d, 'unstable_getStaticPaths'),
          h = (0, n.M)(d, 'unstable_getStaticParams'),
          S = (0, n.M)(d, 'unstable_getServerProps'),
          v = (0, n.M)(d, 'unstable_getServerSideProps'),
          y = new a.PagesRouteModule({
            definition: {
              kind: s.A.PAGES,
              page: '/Register',
              pathname: '/Register',
              bundlePath: '',
              filename: '',
            },
            components: { App: u(), Document: o() },
            userland: d,
          });
      },
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    a = t.X(0, [488], () => r(9206));
  module.exports = a;
})();
