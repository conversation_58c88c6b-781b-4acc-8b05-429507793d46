'use strict';
(() => {
  var e = {};
  (e.id = 153),
    (e.ids = [153, 220]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return i;
            },
          });
        let a = r(8752),
          s = r(6351),
          l = a._(r(2015)),
          n = r(5310);
        async function o(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, n.loadGetInitialProps)(t, r) };
        }
        class i extends l.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, s.jsx)(e, { ...t });
          }
        }
        (i.origGetInitialProps = o),
          (i.getInitialProps = o),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      802: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.d(t, { Cz: () => p, Zc: () => d, ef: () => u, xj: () => c });
            var s = r(1428),
              l = e([s]);
            s = (l.then ? (await l)() : l)[0];
            let n = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
              o = s.default.create({ baseURL: n, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
              i = { log: e => {}, error: (e, t) => {} };
            o.interceptors.request.use(
              e => (i.log(`Making request to: ${e.baseURL}${e.url}`), e),
              e => (i.error('Request error:', e), Promise.reject(e))
            ),
              o.interceptors.response.use(
                e => e,
                async e => {
                  let { config: t } = e;
                  return e.message.includes('Network Error') && t.retry > 0
                    ? ((t.retry -= 1),
                      i.log(`Retrying request to ${t.url}, ${t.retry} attempts left`),
                      await new Promise(e => setTimeout(e, t.retryDelay)),
                      o(t))
                    : Promise.reject(e);
                }
              );
            let c = async (e = 1, t = 10) => {
                try {
                  return (await o.get('/novels/', { params: { page: e, limit: t } })).data;
                } catch (e) {
                  return (
                    i.error('Error fetching novel list:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              },
              u = async e => {
                try {
                  return (await o.get(`/novels/${e}/`)).data;
                } catch (e) {
                  throw (i.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
                }
              },
              d = async e => {
                try {
                  return (await o.get(`/chapters/${e}/`)).data;
                } catch (t) {
                  throw (
                    (i.error(`Error fetching chapter content for chapterId ${e}:`, t),
                    Error('無法載入章節內容'))
                  );
                }
              },
              p = async e => {
                try {
                  return (await o.get('/novels/search', { params: { query: e } })).data;
                } catch (e) {
                  return (
                    i.error('Error searching novels:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      1428: e => {
        e.exports = import('axios');
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      4822: e => {
        e.exports = require('react-router-dom');
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
      9209: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t), r.d(t, { default: () => c });
            var s = r(6351),
              l = r(2015),
              n = r(4822),
              o = r(802),
              i = e([o]);
            o = (i.then ? (await i)() : i)[0];
            let c = () => {
              let { novelSlug: e } = (0, n.useParams)(),
                [t, r] = (0, l.useState)(null),
                [a, i] = (0, l.useState)(!0),
                [c, u] = (0, l.useState)(null);
              return ((0, l.useEffect)(() => {
                (async () => {
                  if (e)
                    try {
                      i(!0);
                      let t = await (0, o.ef)(e);
                      r(t);
                    } catch (t) {
                      u('無法載入小說資訊'),
                        console.error(`Error fetching novel details for ${e}:`, t);
                    } finally {
                      i(!1);
                    }
                })();
              }, [e]),
              a)
                ? (0, s.jsx)('div', { className: 'text-center p-4', children: '載入中...' })
                : c
                  ? (0, s.jsx)('div', { className: 'text-center text-red-500 p-4', children: c })
                  : t
                    ? (0, s.jsx)('div', {
                        className: 'max-w-4xl mx-auto p-4',
                        children: (0, s.jsxs)('div', {
                          className: 'bg-white rounded-lg shadow-lg p-6',
                          children: [
                            (0, s.jsxs)('div', {
                              className: 'flex flex-col md:flex-row gap-6',
                              children: [
                                (0, s.jsx)('img', {
                                  src:
                                    t.cover_url ||
                                    t.coverUrl ||
                                    t.coverImage ||
                                    '/default-cover.jpg',
                                  alt: t.title,
                                  className: 'w-48 h-64 object-cover rounded-lg shadow-md',
                                }),
                                (0, s.jsxs)('div', {
                                  className: 'flex-1',
                                  children: [
                                    (0, s.jsx)('h1', {
                                      className: 'text-3xl font-bold mb-2',
                                      children: t.title,
                                    }),
                                    (0, s.jsxs)('p', {
                                      className: 'text-gray-600 mb-4',
                                      children: ['作者：', t.author],
                                    }),
                                    (0, s.jsx)('div', {
                                      className: 'space-y-2',
                                      children: (0, s.jsx)('p', {
                                        className: 'text-gray-700',
                                        children: t.description || '暫無簡介',
                                      }),
                                    }),
                                  ],
                                }),
                              ],
                            }),
                            (0, s.jsxs)('div', {
                              className: 'mt-8',
                              children: [
                                (0, s.jsx)('h2', {
                                  className: 'text-xl font-bold mb-4',
                                  children: '章節列表',
                                }),
                                t.chapters && t.chapters.length > 0
                                  ? (0, s.jsx)('ul', {
                                      className: 'space-y-2',
                                      children: t.chapters.map(t =>
                                        (0, s.jsx)(
                                          'li',
                                          {
                                            className: 'border-b last:border-b-0 py-2',
                                            children: (0, s.jsx)(n.Link, {
                                              to: `/novels/${e}/chapters/${t.id}`,
                                              className:
                                                'text-blue-600 hover:text-blue-800 hover:underline transition-colors',
                                              children:
                                                t.title ||
                                                `第 ${t.chapter_number || t.chapterNumber} 章`,
                                            }),
                                          },
                                          t.id
                                        )
                                      ),
                                    })
                                  : (0, s.jsx)('p', {
                                      className: 'text-gray-600',
                                      children: '暫無章節。',
                                    }),
                              ],
                            }),
                          ],
                        }),
                      })
                    : (0, s.jsx)('div', { className: 'text-center p-4', children: '找不到小說' });
            };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      9430: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t),
              r.d(t, {
                config: () => x,
                default: () => h,
                getServerSideProps: () => f,
                getStaticPaths: () => g,
                getStaticProps: () => m,
                reportWebVitals: () => P,
                routeModule: () => _,
                unstable_getServerProps: () => j,
                unstable_getServerSideProps: () => S,
                unstable_getStaticParams: () => b,
                unstable_getStaticPaths: () => y,
                unstable_getStaticProps: () => v,
              });
            var s = r(718),
              l = r(208),
              n = r(8732),
              o = r(5488),
              i = r.n(o),
              c = r(752),
              u = r.n(c),
              d = r(9209),
              p = e([d]);
            d = (p.then ? (await p)() : p)[0];
            let h = (0, n.M)(d, 'default'),
              m = (0, n.M)(d, 'getStaticProps'),
              g = (0, n.M)(d, 'getStaticPaths'),
              f = (0, n.M)(d, 'getServerSideProps'),
              x = (0, n.M)(d, 'config'),
              P = (0, n.M)(d, 'reportWebVitals'),
              v = (0, n.M)(d, 'unstable_getStaticProps'),
              y = (0, n.M)(d, 'unstable_getStaticPaths'),
              b = (0, n.M)(d, 'unstable_getStaticParams'),
              j = (0, n.M)(d, 'unstable_getServerProps'),
              S = (0, n.M)(d, 'unstable_getServerSideProps'),
              _ = new s.PagesRouteModule({
                definition: {
                  kind: l.A.PAGES,
                  page: '/NovelDetail',
                  pathname: '/NovelDetail',
                  bundlePath: '',
                  filename: '',
                },
                components: { App: u(), Document: i() },
                userland: d,
              });
            a();
          } catch (e) {
            a(e);
          }
        });
      },
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    a = t.X(0, [488], () => r(9430));
  module.exports = a;
})();
