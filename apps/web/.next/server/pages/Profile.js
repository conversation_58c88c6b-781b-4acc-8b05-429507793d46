'use strict';
(() => {
  var e = {};
  (e.id = 39),
    (e.ids = [39, 220]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return l;
            },
          });
        let a = r(8752),
          s = r(6351),
          n = a._(r(2015)),
          o = r(5310);
        async function i(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, o.loadGetInitialProps)(t, r) };
        }
        class l extends n.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, s.jsx)(e, { ...t });
          }
        }
        (l.origGetInitialProps = i),
          (l.getInitialProps = i),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      802: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.d(t, { Cz: () => p, Zc: () => d, ef: () => c, xj: () => u });
            var s = r(1428),
              n = e([s]);
            s = (n.then ? (await n)() : n)[0];
            let o = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
              i = s.default.create({ baseURL: o, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
              l = { log: e => {}, error: (e, t) => {} };
            i.interceptors.request.use(
              e => (l.log(`Making request to: ${e.baseURL}${e.url}`), e),
              e => (l.error('Request error:', e), Promise.reject(e))
            ),
              i.interceptors.response.use(
                e => e,
                async e => {
                  let { config: t } = e;
                  return e.message.includes('Network Error') && t.retry > 0
                    ? ((t.retry -= 1),
                      l.log(`Retrying request to ${t.url}, ${t.retry} attempts left`),
                      await new Promise(e => setTimeout(e, t.retryDelay)),
                      i(t))
                    : Promise.reject(e);
                }
              );
            let u = async (e = 1, t = 10) => {
                try {
                  return (await i.get('/novels/', { params: { page: e, limit: t } })).data;
                } catch (e) {
                  return (
                    l.error('Error fetching novel list:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              },
              c = async e => {
                try {
                  return (await i.get(`/novels/${e}/`)).data;
                } catch (e) {
                  throw (l.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
                }
              },
              d = async e => {
                try {
                  return (await i.get(`/chapters/${e}/`)).data;
                } catch (t) {
                  throw (
                    (l.error(`Error fetching chapter content for chapterId ${e}:`, t),
                    Error('無法載入章節內容'))
                  );
                }
              },
              p = async e => {
                try {
                  return (await i.get('/novels/search', { params: { query: e } })).data;
                } catch (e) {
                  return (
                    l.error('Error searching novels:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      1428: e => {
        e.exports = import('axios');
      },
      1590: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t),
              r.d(t, {
                config: () => m,
                default: () => P,
                getServerSideProps: () => g,
                getStaticPaths: () => f,
                getStaticProps: () => h,
                reportWebVitals: () => y,
                routeModule: () => S,
                unstable_getServerProps: () => _,
                unstable_getServerSideProps: () => A,
                unstable_getStaticParams: () => x,
                unstable_getStaticPaths: () => b,
                unstable_getStaticProps: () => v,
              });
            var s = r(718),
              n = r(208),
              o = r(8732),
              i = r(5488),
              l = r.n(i),
              u = r(752),
              c = r.n(u),
              d = r(9511),
              p = e([d]);
            d = (p.then ? (await p)() : p)[0];
            let P = (0, o.M)(d, 'default'),
              h = (0, o.M)(d, 'getStaticProps'),
              f = (0, o.M)(d, 'getStaticPaths'),
              g = (0, o.M)(d, 'getServerSideProps'),
              m = (0, o.M)(d, 'config'),
              y = (0, o.M)(d, 'reportWebVitals'),
              v = (0, o.M)(d, 'unstable_getStaticProps'),
              b = (0, o.M)(d, 'unstable_getStaticPaths'),
              x = (0, o.M)(d, 'unstable_getStaticParams'),
              _ = (0, o.M)(d, 'unstable_getServerProps'),
              A = (0, o.M)(d, 'unstable_getServerSideProps'),
              S = new s.PagesRouteModule({
                definition: {
                  kind: n.A.PAGES,
                  page: '/Profile',
                  pathname: '/Profile',
                  bundlePath: '',
                  filename: '',
                },
                components: { App: c(), Document: l() },
                userland: d,
              });
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      7109: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.d(t, { As: () => l }), r(6351);
            var s = r(2015),
              n = r(802),
              o = e([n]);
            n = (o.then ? (await o)() : o)[0];
            let i = (0, s.createContext)(null),
              l = () => {
                let e = (0, s.useContext)(i);
                if (!e) throw Error('useAuth must be used within an AuthProvider');
                return e;
              };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
      9511: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t), r.d(t, { default: () => i });
            var s = r(6351);
            r(2015);
            var n = r(7109),
              o = e([n]);
            n = (o.then ? (await o)() : o)[0];
            let i = () => {
              let { user: e } = (0, n.As)();
              return e
                ? (0, s.jsxs)('div', {
                    className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
                    children: [
                      (0, s.jsx)('h1', {
                        className: 'text-2xl font-bold mb-6',
                        children: '個人資料',
                      }),
                      (0, s.jsxs)('div', {
                        className: 'space-y-4',
                        children: [
                          (0, s.jsxs)('div', {
                            children: [
                              (0, s.jsx)('label', {
                                className: 'font-medium',
                                children: '使用者名稱:',
                              }),
                              (0, s.jsx)('p', { children: e.username }),
                            ],
                          }),
                          (0, s.jsxs)('div', {
                            children: [
                              (0, s.jsx)('label', {
                                className: 'font-medium',
                                children: '電子郵件:',
                              }),
                              (0, s.jsx)('p', { children: e.email }),
                            ],
                          }),
                        ],
                      }),
                    ],
                  })
                : (0, s.jsx)('div', { children: '請先登入' });
            };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    a = t.X(0, [488], () => r(1590));
  module.exports = a;
})();
