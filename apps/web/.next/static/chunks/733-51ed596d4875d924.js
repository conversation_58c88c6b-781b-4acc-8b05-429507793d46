(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [733],
  {
    585: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'pagebreak [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'separator']],
        });
    },
    1206: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-posinset': null,
            'aria-setsize': null,
            'aria-selected': 'false',
          },
          relatedConcepts: [],
          requireContextRole: ['tablist'],
          requiredContextRole: ['tablist'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'sectionhead'],
            ['roletype', 'widget'],
          ],
        });
    },
    1318: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'menuitem' }, module: 'ARIA' }],
          requireContextRole: ['group', 'menu', 'menubar'],
          requiredContextRole: ['group', 'menu', 'menubar'],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [
            ['roletype', 'widget', 'input', 'checkbox'],
            ['roletype', 'widget', 'command', 'menuitem'],
          ],
        });
    },
    1566: (e, t, r) => {
      'use strict';
      var n = r(20284),
        o = r(7835);
      e.exports = function () {
        var e = n();
        return (
          o(
            Object,
            { is: e },
            {
              is: function () {
                return Object.is !== e;
              },
            }
          ),
          e
        );
      };
    },
    1586: (e, t, r) => {
      'use strict';
      var n = r(95024);
      e.exports = function (e) {
        return n(e) || 0 === e ? e : e < 0 ? -1 : 1;
      };
    },
    1605: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
          },
          relatedConcepts: [{ concept: { name: 'list' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['radio']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    2116: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': 'vertical' },
          relatedConcepts: [
            { concept: { name: 'MENU' }, module: 'JAPI' },
            { concept: { name: 'list' }, module: 'ARIA' },
            { concept: { name: 'select' }, module: 'XForms' },
            { concept: { name: 'sidebar' }, module: 'DTB' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [
            ['menuitem', 'group'],
            ['menuitemradio', 'group'],
            ['menuitemcheckbox', 'group'],
            ['menuitem'],
            ['menuitemcheckbox'],
            ['menuitemradio'],
          ],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    2340: e => {
      'use strict';
      e.exports = ReferenceError;
    },
    2427: (e, t, r) => {
      'use strict';
      var n = 'undefined' != typeof Symbol && Symbol,
        o = r(67541);
      e.exports = function () {
        return (
          'function' == typeof n &&
          'function' == typeof Symbol &&
          'symbol' == typeof n('foo') &&
          'symbol' == typeof Symbol('bar') &&
          o()
        );
      };
    },
    2517: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'notice [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'note']],
        });
    },
    2798: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.test = t.serialize = t.default = void 0);
      var n = r(58460);
      let o = '@@__IMMUTABLE_ORDERED__@@',
        a = e => 'Immutable.' + e,
        i = e => '[' + e + ']',
        l = (e, t, r, o, l, u, s) =>
          ++o > t.maxDepth
            ? i(a(s))
            : a(s) + ' {' + (0, n.printIteratorEntries)(e.entries(), t, r, o, l, u) + '}',
        u = (e, t, r, o, l, u) => {
          let s = a(e._name || 'Record');
          return ++o > t.maxDepth
            ? i(s)
            : s +
                ' {' +
                (0, n.printIteratorEntries)(
                  (function (e) {
                    let t = 0;
                    return {
                      next() {
                        if (t < e._keys.length) {
                          let r = e._keys[t++];
                          return { done: !1, value: [r, e.get(r)] };
                        }
                        return { done: !0, value: void 0 };
                      },
                    };
                  })(e),
                  t,
                  r,
                  o,
                  l,
                  u
                ) +
                '}';
        },
        s = (e, t, r, o, l, u) => {
          let s = a('Seq');
          return ++o > t.maxDepth
            ? i(s)
            : e['@@__IMMUTABLE_KEYED__@@']
              ? s +
                ' {' +
                (e._iter || e._object
                  ? (0, n.printIteratorEntries)(e.entries(), t, r, o, l, u)
                  : '…') +
                '}'
              : s +
                ' [' +
                (e._iter || e._array || e._collection || e._iterable
                  ? (0, n.printIteratorValues)(e.values(), t, r, o, l, u)
                  : '…') +
                ']';
        },
        c = (e, t, r, o, l, u, s) =>
          ++o > t.maxDepth
            ? i(a(s))
            : a(s) + ' [' + (0, n.printIteratorValues)(e.values(), t, r, o, l, u) + ']',
        d = (e, t, r, n, a, i) =>
          e['@@__IMMUTABLE_MAP__@@']
            ? l(e, t, r, n, a, i, e[o] ? 'OrderedMap' : 'Map')
            : e['@@__IMMUTABLE_LIST__@@']
              ? c(e, t, r, n, a, i, 'List')
              : e['@@__IMMUTABLE_SET__@@']
                ? c(e, t, r, n, a, i, e[o] ? 'OrderedSet' : 'Set')
                : e['@@__IMMUTABLE_STACK__@@']
                  ? c(e, t, r, n, a, i, 'Stack')
                  : e['@@__IMMUTABLE_SEQ__@@']
                    ? s(e, t, r, n, a, i)
                    : u(e, t, r, n, a, i);
      t.serialize = d;
      let p = e =>
        e && (!0 === e['@@__IMMUTABLE_ITERABLE__@@'] || !0 === e['@@__IMMUTABLE_RECORD__@@']);
      (t.test = p), (t.default = { serialize: d, test: p });
    },
    4278: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-checked': null, 'aria-posinset': null, 'aria-setsize': null },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'radio' }], name: 'input' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    4528: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'search' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'input', 'textbox']],
        });
    },
    4593: (e, t) => {
      'use strict';
      var r = 60103,
        n = 60106,
        o = 60107,
        a = 60108,
        i = 60114,
        l = 60109,
        u = 60110,
        s = 60112,
        c = 60113,
        d = 60120,
        p = 60115,
        f = 60116,
        b = 60121,
        m = 60122,
        y = 60117,
        v = 60129,
        h = 60131;
      if ('function' == typeof Symbol && Symbol.for) {
        var g = Symbol.for;
        (r = g('react.element')),
          (n = g('react.portal')),
          (o = g('react.fragment')),
          (a = g('react.strict_mode')),
          (i = g('react.profiler')),
          (l = g('react.provider')),
          (u = g('react.context')),
          (s = g('react.forward_ref')),
          (c = g('react.suspense')),
          (d = g('react.suspense_list')),
          (p = g('react.memo')),
          (f = g('react.lazy')),
          (b = g('react.block')),
          (m = g('react.server.block')),
          (y = g('react.fundamental')),
          (v = g('react.debug_trace_mode')),
          (h = g('react.legacy_hidden'));
      }
      function P(e) {
        if ('object' == typeof e && null !== e) {
          var t = e.$$typeof;
          switch (t) {
            case r:
              switch ((e = e.type)) {
                case o:
                case i:
                case a:
                case c:
                case d:
                  return e;
                default:
                  switch ((e = e && e.$$typeof)) {
                    case u:
                    case s:
                    case f:
                    case p:
                    case l:
                      return e;
                    default:
                      return t;
                  }
              }
            case n:
              return t;
          }
        }
      }
      var C = l,
        q = r,
        x = s,
        E = o,
        w = f,
        O = p,
        R = n,
        j = i,
        S = a,
        _ = c;
      (t.ContextConsumer = u),
        (t.ContextProvider = C),
        (t.Element = q),
        (t.ForwardRef = x),
        (t.Fragment = E),
        (t.Lazy = w),
        (t.Memo = O),
        (t.Portal = R),
        (t.Profiler = j),
        (t.StrictMode = S),
        (t.Suspense = _),
        (t.isAsyncMode = function () {
          return !1;
        }),
        (t.isConcurrentMode = function () {
          return !1;
        }),
        (t.isContextConsumer = function (e) {
          return P(e) === u;
        }),
        (t.isContextProvider = function (e) {
          return P(e) === l;
        }),
        (t.isElement = function (e) {
          return 'object' == typeof e && null !== e && e.$$typeof === r;
        }),
        (t.isForwardRef = function (e) {
          return P(e) === s;
        }),
        (t.isFragment = function (e) {
          return P(e) === o;
        }),
        (t.isLazy = function (e) {
          return P(e) === f;
        }),
        (t.isMemo = function (e) {
          return P(e) === p;
        }),
        (t.isPortal = function (e) {
          return P(e) === n;
        }),
        (t.isProfiler = function (e) {
          return P(e) === i;
        }),
        (t.isStrictMode = function (e) {
          return P(e) === a;
        }),
        (t.isSuspense = function (e) {
          return P(e) === c;
        }),
        (t.isValidElementType = function (e) {
          return (
            'string' == typeof e ||
            'function' == typeof e ||
            e === o ||
            e === i ||
            e === v ||
            e === a ||
            e === c ||
            e === d ||
            e === h ||
            ('object' == typeof e &&
              null !== e &&
              (e.$$typeof === f ||
                e.$$typeof === p ||
                e.$$typeof === l ||
                e.$$typeof === u ||
                e.$$typeof === s ||
                e.$$typeof === y ||
                e.$$typeof === b ||
                e[0] === m)) ||
            !1
          );
        }),
        (t.typeOf = P);
    },
    6333: e => {
      'use strict';
      e.exports = ('undefined' != typeof Reflect && Reflect.getPrototypeOf) || null;
    },
    6390: (e, t, r) => {
      'use strict';
      var n = r(8241),
        o = r(64215),
        a = r(92914),
        i = r(63784);
      e.exports = function (e) {
        if (e.length < 1 || 'function' != typeof e[0]) throw new o('a function is required');
        return i(n, a, e);
      };
    },
    6653: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { constraints: ['direct descendant of document'], name: 'header' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    6784: (e, t, r) => {
      'use strict';
      var n = r(77051),
        o = r(50510),
        a = r(21089);
      if (r(2427)() || r(67541)()) {
        var i = Symbol.iterator;
        e.exports = function (e) {
          return null != e && void 0 !== e[i] ? e[i]() : o(e) ? Array.prototype[i].call(e) : void 0;
        };
      } else {
        var l = r(67602),
          u = r(43026),
          s = r(71005),
          c = s('%Map%', !0),
          d = s('%Set%', !0),
          p = r(31630),
          f = p('Array.prototype.push'),
          b = p('String.prototype.charCodeAt'),
          m = p('String.prototype.slice'),
          y = function (e, t) {
            if (t + 1 >= e.length) return t + 1;
            var r = b(e, t);
            if (r < 55296 || r > 56319) return t + 1;
            var n = b(e, t + 1);
            return n < 56320 || n > 57343 ? t + 1 : t + 2;
          },
          v = function (e) {
            var t = 0;
            return {
              next: function () {
                var r,
                  n = t >= e.length;
                return n || ((r = e[t]), (t += 1)), { done: n, value: r };
              },
            };
          },
          h = function (e, t) {
            if (l(e) || o(e)) return v(e);
            if (u(e)) {
              var r = 0;
              return {
                next: function () {
                  var t = y(e, r),
                    n = m(e, r, t);
                  return (r = t), { done: t > e.length, value: n };
                },
              };
            }
            if (t && void 0 !== e['_es6-shim iterator_']) return e['_es6-shim iterator_']();
          };
        if (c || d) {
          var g = r(27812),
            P = r(32604),
            C = p('Map.prototype.forEach', !0),
            q = p('Set.prototype.forEach', !0);
          if (void 0 === n || !n.versions || !n.versions.node)
            var x = p('Map.prototype.iterator', !0),
              E = p('Set.prototype.iterator', !0);
          var w = p('Map.prototype.@@iterator', !0) || p('Map.prototype._es6-shim iterator_', !0),
            O = p('Set.prototype.@@iterator', !0) || p('Set.prototype._es6-shim iterator_', !0),
            R = function (e) {
              if (g(e)) {
                if (x) return a(x(e));
                if (w) return w(e);
                if (C) {
                  var t = [];
                  return (
                    C(e, function (e, r) {
                      f(t, [r, e]);
                    }),
                    v(t)
                  );
                }
              }
              if (P(e)) {
                if (E) return a(E(e));
                if (O) return O(e);
                if (q) {
                  var r = [];
                  return (
                    q(e, function (e) {
                      f(r, e);
                    }),
                    v(r)
                  );
                }
              }
            };
          e.exports = function (e) {
            return R(e) || h(e);
          };
        } else
          e.exports = function (e) {
            if (null != e) return h(e, !0);
          };
      }
    },
    7281: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': 'horizontal' },
          relatedConcepts: [{ concept: { name: 'toolbar' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [
            ['menuitem', 'group'],
            ['menuitemradio', 'group'],
            ['menuitemcheckbox', 'group'],
            ['menuitem'],
            ['menuitemcheckbox'],
            ['menuitemradio'],
          ],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select', 'menu'],
            ['roletype', 'structure', 'section', 'group', 'select', 'menu'],
          ],
        });
    },
    7806: (e, t, r) => {
      'use strict';
      var n = r(71005),
        o = r(47600),
        a = r(36074),
        i = r(12751),
        l = r(64215),
        u = n('%WeakMap%', !0),
        s = o('WeakMap.prototype.get', !0),
        c = o('WeakMap.prototype.set', !0),
        d = o('WeakMap.prototype.has', !0),
        p = o('WeakMap.prototype.delete', !0);
      e.exports = u
        ? function () {
            var e,
              t,
              r = {
                assert: function (e) {
                  if (!r.has(e)) throw new l('Side channel does not contain ' + a(e));
                },
                delete: function (r) {
                  if (u && r && ('object' == typeof r || 'function' == typeof r)) {
                    if (e) return p(e, r);
                  } else if (i && t) return t.delete(r);
                  return !1;
                },
                get: function (r) {
                  return u && r && ('object' == typeof r || 'function' == typeof r) && e
                    ? s(e, r)
                    : t && t.get(r);
                },
                has: function (r) {
                  return u && r && ('object' == typeof r || 'function' == typeof r) && e
                    ? d(e, r)
                    : !!t && t.has(r);
                },
                set: function (r, n) {
                  u && r && ('object' == typeof r || 'function' == typeof r)
                    ? (e || (e = new u()), c(e, r, n))
                    : i && (t || (t = i()), t.set(r, n));
                },
              };
            return r;
          }
        : i;
    },
    7835: (e, t, r) => {
      'use strict';
      var n = r(75166),
        o = 'function' == typeof Symbol && 'symbol' == typeof Symbol('foo'),
        a = Object.prototype.toString,
        i = Array.prototype.concat,
        l = r(16975),
        u = r(78486)(),
        s = function (e, t, r, n) {
          if (t in e) {
            if (!0 === n) {
              if (e[t] === r) return;
            } else if ('function' != typeof n || '[object Function]' !== a.call(n) || !n()) return;
          }
          u ? l(e, t, r, !0) : l(e, t, r);
        },
        c = function (e, t) {
          var r = arguments.length > 2 ? arguments[2] : {},
            a = n(t);
          o && (a = i.call(a, Object.getOwnPropertySymbols(t)));
          for (var l = 0; l < a.length; l += 1) s(e, a[l], t[a[l]], r[a[l]]);
        };
      (c.supportsDescriptors = !!u), (e.exports = c);
    },
    7871: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-atomic': 'true', 'aria-live': 'assertive' },
          relatedConcepts: [{ concept: { name: 'alert' }, module: 'XForms' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    8241: (e, t, r) => {
      'use strict';
      var n = r(61293);
      e.exports = Function.prototype.bind || n;
    },
    8377: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-activedescendant': null, 'aria-disabled': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget']],
        });
    },
    9278: (e, t, r) => {
      'use strict';
      var n = r(57644);
      if (n)
        try {
          n([], 'length');
        } catch (e) {
          n = null;
        }
      e.exports = n;
    },
    9369: (e, t, r) => {
      'use strict';
      var n = r(7835),
        o = r(34802),
        a = r(38165),
        i = r(20284),
        l = r(1566),
        u = o(i(), Object);
      n(u, { getPolyfill: i, implementation: a, shim: l }), (e.exports = u);
    },
    9496: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = a(r(70863)),
        o = a(r(12407));
      function a(e) {
        return e && e.__esModule ? e : { default: e };
      }
      function i(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                i = !0,
                l = !1;
              try {
                for (
                  o = o.call(e);
                  !(i = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  i = !0
                );
              } catch (e) {
                (l = !0), (n = e);
              } finally {
                try {
                  i || null == o.return || o.return();
                } finally {
                  if (l) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          l(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function l(e, t) {
        if (e) {
          if ('string' == typeof e) return u(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return u(e, t);
        }
      }
      function u(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      for (var s = [], c = o.default.keys(), d = 0; d < c.length; d++)
        !(function (e) {
          var t = c[e],
            r = o.default.get(t);
          if (r)
            for (var n = [].concat(r.baseConcepts, r.relatedConcepts), a = 0; a < n.length; a++) {
              var i = n[a];
              if ('HTML' === i.module) {
                var l = i.concept;
                if (l) {
                  var u = s.find(function (e) {
                      return e[0] === t;
                    }),
                    d = void 0;
                  (d = u ? u[1] : []).push(l), s.push([t, d]);
                }
              }
            }
        })(d);
      var p = {
        entries: function () {
          return s;
        },
        forEach: function (e) {
          var t,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
            n = (function (e, t) {
              var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
              if (!r) {
                if (Array.isArray(e) || (r = l(e))) {
                  r && (e = r);
                  var n = 0,
                    o = function () {};
                  return {
                    s: o,
                    n: function () {
                      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                    },
                    e: function (e) {
                      throw e;
                    },
                    f: o,
                  };
                }
                throw TypeError(
                  'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                );
              }
              var a,
                i = !0,
                u = !1;
              return {
                s: function () {
                  r = r.call(e);
                },
                n: function () {
                  var e = r.next();
                  return (i = e.done), e;
                },
                e: function (e) {
                  (u = !0), (a = e);
                },
                f: function () {
                  try {
                    i || null == r.return || r.return();
                  } finally {
                    if (u) throw a;
                  }
                },
              };
            })(s);
          try {
            for (n.s(); !(t = n.n()).done; ) {
              var o = i(t.value, 2),
                a = o[0],
                u = o[1];
              e.call(r, u, a, s);
            }
          } catch (e) {
            n.e(e);
          } finally {
            n.f();
          }
        },
        get: function (e) {
          var t = s.find(function (t) {
            return t[0] === e;
          });
          return t && t[1];
        },
        has: function (e) {
          return !!p.get(e);
        },
        keys: function () {
          return s.map(function (e) {
            return i(e, 1)[0];
          });
        },
        values: function () {
          return s.map(function (e) {
            return i(e, 2)[1];
          });
        },
      };
      t.default = (0, n.default)(p, p.entries());
    },
    10701: (e, t, r) => {
      'use strict';
      e.exports = r(45760).getPrototypeOf || null;
    },
    10842: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'dd' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    11351: (e, t, r) => {
      'use strict';
      var n = Function.prototype.call,
        o = Object.prototype.hasOwnProperty;
      e.exports = r(8241).call(n, o);
    },
    11810: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.test = t.serialize = t.default = void 0);
      var n = r(58460);
      let o = ['DOMStringMap', 'NamedNodeMap'],
        a = /^(HTML\w*Collection|NodeList)$/,
        i = e => -1 !== o.indexOf(e) || a.test(e),
        l = e => e && e.constructor && !!e.constructor.name && i(e.constructor.name);
      t.test = l;
      let u = e => 'NamedNodeMap' === e.constructor.name,
        s = (e, t, r, a, i, l) => {
          let s = e.constructor.name;
          return ++a > t.maxDepth
            ? '[' + s + ']'
            : (t.min ? '' : s + ' ') +
                (-1 !== o.indexOf(s)
                  ? '{' +
                    (0, n.printObjectProperties)(
                      u(e)
                        ? Array.from(e).reduce((e, t) => ((e[t.name] = t.value), e), {})
                        : { ...e },
                      t,
                      r,
                      a,
                      i,
                      l
                    ) +
                    '}'
                  : '[' + (0, n.printListItems)(Array.from(e), t, r, a, i, l) + ']');
        };
      (t.serialize = s), (t.default = { serialize: s, test: l });
    },
    12239: e => {
      'use strict';
      e.exports = Math.max;
    },
    12407: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = u(r(78165)),
        o = u(r(95006)),
        a = u(r(99602)),
        i = u(r(48932)),
        l = u(r(70863));
      function u(e) {
        return e && e.__esModule ? e : { default: e };
      }
      function s(e, t) {
        var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
        if (!r) {
          if (Array.isArray(e) || (r = d(e)) || (t && e && 'number' == typeof e.length)) {
            r && (e = r);
            var n = 0,
              o = function () {};
            return {
              s: o,
              n: function () {
                return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
              },
              e: function (e) {
                throw e;
              },
              f: o,
            };
          }
          throw TypeError(
            'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
          );
        }
        var a,
          i = !0,
          l = !1;
        return {
          s: function () {
            r = r.call(e);
          },
          n: function () {
            var e = r.next();
            return (i = e.done), e;
          },
          e: function (e) {
            (l = !0), (a = e);
          },
          f: function () {
            try {
              i || null == r.return || r.return();
            } finally {
              if (l) throw a;
            }
          },
        };
      }
      function c(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                i = !0,
                l = !1;
              try {
                for (
                  o = o.call(e);
                  !(i = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  i = !0
                );
              } catch (e) {
                (l = !0), (n = e);
              } finally {
                try {
                  i || null == o.return || o.return();
                } finally {
                  if (l) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          d(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function d(e, t) {
        if (e) {
          if ('string' == typeof e) return p(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return p(e, t);
        }
      }
      function p(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      var f = [].concat(n.default, o.default, a.default, i.default);
      f.forEach(function (e) {
        var t,
          r = c(e, 2)[1],
          n = s(r.superClass);
        try {
          for (n.s(); !(t = n.n()).done; ) {
            var o,
              a = t.value,
              i = s(a);
            try {
              for (i.s(); !(o = i.n()).done; )
                !(function () {
                  var e = o.value,
                    t = f.find(function (t) {
                      return c(t, 1)[0] === e;
                    });
                  if (t)
                    for (var n = t[1], a = 0, i = Object.keys(n.props); a < i.length; a++) {
                      var l,
                        u,
                        s = i[a];
                      Object.prototype.hasOwnProperty.call(r.props, s) ||
                        Object.assign(
                          r.props,
                          ((l = {}),
                          (u = n.props[s]),
                          s in l
                            ? Object.defineProperty(l, s, {
                                value: u,
                                enumerable: !0,
                                configurable: !0,
                                writable: !0,
                              })
                            : (l[s] = u),
                          l)
                        );
                    }
                })();
            } catch (e) {
              i.e(e);
            } finally {
              i.f();
            }
          }
        } catch (e) {
          n.e(e);
        } finally {
          n.f();
        }
      });
      var b = {
        entries: function () {
          return f;
        },
        forEach: function (e) {
          var t,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
            n = s(f);
          try {
            for (n.s(); !(t = n.n()).done; ) {
              var o = c(t.value, 2),
                a = o[0],
                i = o[1];
              e.call(r, i, a, f);
            }
          } catch (e) {
            n.e(e);
          } finally {
            n.f();
          }
        },
        get: function (e) {
          var t = f.find(function (t) {
            return t[0] === e;
          });
          return t && t[1];
        },
        has: function (e) {
          return !!b.get(e);
        },
        keys: function () {
          return f.map(function (e) {
            return c(e, 1)[0];
          });
        },
        values: function () {
          return f.map(function (e) {
            return c(e, 2)[1];
          });
        },
      };
      t.default = (0, l.default)(b, b.entries());
    },
    12459: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'biblioref [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    12594: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'img']],
        });
    },
    12751: (e, t, r) => {
      'use strict';
      var n = r(71005),
        o = r(47600),
        a = r(36074),
        i = r(64215),
        l = n('%Map%', !0),
        u = o('Map.prototype.get', !0),
        s = o('Map.prototype.set', !0),
        c = o('Map.prototype.has', !0),
        d = o('Map.prototype.delete', !0),
        p = o('Map.prototype.size', !0);
      e.exports =
        !!l &&
        function () {
          var e,
            t = {
              assert: function (e) {
                if (!t.has(e)) throw new i('Side channel does not contain ' + a(e));
              },
              delete: function (t) {
                if (e) {
                  var r = d(e, t);
                  return 0 === p(e) && (e = void 0), r;
                }
                return !1;
              },
              get: function (t) {
                if (e) return u(e, t);
              },
              has: function (t) {
                return !!e && c(e, t);
              },
              set: function (t, r) {
                e || (e = new l()), s(e, t, r);
              },
            };
          return t;
        };
    },
    13117: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'frontmatter' }, module: 'DTB' },
            { concept: { name: 'level' }, module: 'DTB' },
            { concept: { name: 'level' }, module: 'SMIL' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    13172: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    13398: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-activedescendant': null, 'aria-disabled': null },
          relatedConcepts: [
            { concept: { name: 'details' }, module: 'HTML' },
            { concept: { name: 'fieldset' }, module: 'HTML' },
            { concept: { name: 'optgroup' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    13643: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-multiselectable': null,
            'aria-required': null,
            'aria-orientation': 'vertical',
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['treeitem', 'group'], ['treeitem']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    14945: (e, t, r) => {
      'use strict';
      r.d(t, { _1: () => R, D0: () => j });
      var n = Object.prototype.toString;
      function o(e) {
        return 'function' == typeof e || '[object Function]' === n.call(e);
      }
      function a(e, t) {
        var r,
          n,
          a = Array,
          i = Object(e);
        if (null == e)
          throw TypeError('Array.from requires an array-like object - not null or undefined');
        if (void 0 !== t && !o(t))
          throw TypeError('Array.from: when provided, the second argument must be a function');
        for (
          var l = Math.min(
              Math.max(
                isNaN((r = Number(i.length)))
                  ? 0
                  : 0 !== r && isFinite(r)
                    ? (r > 0 ? 1 : -1) * Math.floor(Math.abs(r))
                    : r,
                0
              ),
              0x1fffffffffffff
            ),
            u = o(a) ? Object(new a(l)) : Array(l),
            s = 0;
          s < l;

        )
          (n = i[s]), t ? (u[s] = t(n, s)) : (u[s] = n), (s += 1);
        return (u.length = l), u;
      }
      function i(e) {
        return (i =
          'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  'function' == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? 'symbol'
                  : typeof e;
              })(e);
      }
      function l(e) {
        var t = (function (e, t) {
          if ('object' !== i(e) || null === e) return e;
          var r = e[Symbol.toPrimitive];
          if (void 0 !== r) {
            var n = r.call(e, t || 'default');
            if ('object' !== i(n)) return n;
            throw TypeError('@@toPrimitive must return a primitive value.');
          }
          return ('string' === t ? String : Number)(e);
        })(e, 'string');
        return 'symbol' === i(t) ? t : String(t);
      }
      var u = (function () {
        var e;
        function t() {
          var e,
            r,
            n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
          if (!(this instanceof t)) throw TypeError('Cannot call a class as a function');
          (r = void 0),
            (e = l((e = 'items'))) in this
              ? Object.defineProperty(this, e, {
                  value: r,
                  enumerable: !0,
                  configurable: !0,
                  writable: !0,
                })
              : (this[e] = r),
            (this.items = n);
        }
        return (
          (e = [
            {
              key: 'add',
              value: function (e) {
                return !1 === this.has(e) && this.items.push(e), this;
              },
            },
            {
              key: 'clear',
              value: function () {
                this.items = [];
              },
            },
            {
              key: 'delete',
              value: function (e) {
                var t = this.items.length;
                return (
                  (this.items = this.items.filter(function (t) {
                    return t !== e;
                  })),
                  t !== this.items.length
                );
              },
            },
            {
              key: 'forEach',
              value: function (e) {
                var t = this;
                this.items.forEach(function (r) {
                  e(r, r, t);
                });
              },
            },
            {
              key: 'has',
              value: function (e) {
                return -1 !== this.items.indexOf(e);
              },
            },
            {
              key: 'size',
              get: function () {
                return this.items.length;
              },
            },
          ]),
          (function (e, t) {
            for (var r = 0; r < t.length; r++) {
              var n = t[r];
              (n.enumerable = n.enumerable || !1),
                (n.configurable = !0),
                'value' in n && (n.writable = !0),
                Object.defineProperty(e, l(n.key), n);
            }
          })(t.prototype, e),
          Object.defineProperty(t, 'prototype', { writable: !1 }),
          t
        );
      })();
      let s = 'undefined' == typeof Set ? Set : u;
      function c(e) {
        var t;
        return null != (t = e.localName) ? t : e.tagName.toLowerCase();
      }
      var d = {
          article: 'article',
          aside: 'complementary',
          button: 'button',
          datalist: 'listbox',
          dd: 'definition',
          details: 'group',
          dialog: 'dialog',
          dt: 'term',
          fieldset: 'group',
          figure: 'figure',
          form: 'form',
          footer: 'contentinfo',
          h1: 'heading',
          h2: 'heading',
          h3: 'heading',
          h4: 'heading',
          h5: 'heading',
          h6: 'heading',
          header: 'banner',
          hr: 'separator',
          html: 'document',
          legend: 'legend',
          li: 'listitem',
          math: 'math',
          main: 'main',
          menu: 'list',
          nav: 'navigation',
          ol: 'list',
          optgroup: 'group',
          option: 'option',
          output: 'status',
          progress: 'progressbar',
          section: 'region',
          summary: 'button',
          table: 'table',
          tbody: 'rowgroup',
          textarea: 'textbox',
          tfoot: 'rowgroup',
          td: 'cell',
          th: 'columnheader',
          thead: 'rowgroup',
          tr: 'row',
          ul: 'list',
        },
        p = {
          caption: new Set(['aria-label', 'aria-labelledby']),
          code: new Set(['aria-label', 'aria-labelledby']),
          deletion: new Set(['aria-label', 'aria-labelledby']),
          emphasis: new Set(['aria-label', 'aria-labelledby']),
          generic: new Set(['aria-label', 'aria-labelledby', 'aria-roledescription']),
          insertion: new Set(['aria-label', 'aria-labelledby']),
          paragraph: new Set(['aria-label', 'aria-labelledby']),
          presentation: new Set(['aria-label', 'aria-labelledby']),
          strong: new Set(['aria-label', 'aria-labelledby']),
          subscript: new Set(['aria-label', 'aria-labelledby']),
          superscript: new Set(['aria-label', 'aria-labelledby']),
        };
      function f(e, t) {
        return [
          'aria-atomic',
          'aria-busy',
          'aria-controls',
          'aria-current',
          'aria-describedby',
          'aria-details',
          'aria-dropeffect',
          'aria-flowto',
          'aria-grabbed',
          'aria-hidden',
          'aria-keyshortcuts',
          'aria-label',
          'aria-labelledby',
          'aria-live',
          'aria-owns',
          'aria-relevant',
          'aria-roledescription',
        ].some(function (r) {
          var n;
          return e.hasAttribute(r) && !(null != (n = p[t]) && n.has(r));
        });
      }
      function b(e) {
        return null !== e && e.nodeType === e.ELEMENT_NODE;
      }
      function m(e) {
        return b(e) && 'caption' === c(e);
      }
      function y(e) {
        return b(e) && 'input' === c(e);
      }
      function v(e, t) {
        if (b(e) && e.hasAttribute(t)) {
          var r = e.getAttribute(t).split(' '),
            n = e.getRootNode ? e.getRootNode() : e.ownerDocument;
          return r
            .map(function (e) {
              return n.getElementById(e);
            })
            .filter(function (e) {
              return null !== e;
            });
        }
        return [];
      }
      function h(e, t) {
        return (
          !!b(e) &&
          -1 !==
            t.indexOf(
              (function (e) {
                var t = (function (e) {
                  var t = e.getAttribute('role');
                  if (null !== t) {
                    var r = t.trim().split(' ')[0];
                    if (r.length > 0) return r;
                  }
                  return null;
                })(e);
                if (null === t || 'presentation' === t) {
                  var r = (function (e) {
                    var t = d[c(e)];
                    if (void 0 !== t) return t;
                    switch (c(e)) {
                      case 'a':
                      case 'area':
                      case 'link':
                        if (e.hasAttribute('href')) return 'link';
                        break;
                      case 'img':
                        if ('' === e.getAttribute('alt') && !f(e, 'img')) return 'presentation';
                        return 'img';
                      case 'input':
                        var r = e.type;
                        switch (r) {
                          case 'button':
                          case 'image':
                          case 'reset':
                          case 'submit':
                            return 'button';
                          case 'checkbox':
                          case 'radio':
                            return r;
                          case 'range':
                            return 'slider';
                          case 'email':
                          case 'tel':
                          case 'text':
                          case 'url':
                            if (e.hasAttribute('list')) return 'combobox';
                            return 'textbox';
                          case 'search':
                            if (e.hasAttribute('list')) return 'combobox';
                            return 'searchbox';
                          case 'number':
                            return 'spinbutton';
                          default:
                            return null;
                        }
                      case 'select':
                        if (e.hasAttribute('multiple') || e.size > 1) return 'listbox';
                        return 'combobox';
                    }
                    return null;
                  })(e);
                  if ('presentation' !== t || f(e, r || '')) return r;
                }
                return t;
              })(e)
            )
        );
      }
      function g(e, t) {
        if (!b(e)) return !1;
        if ('range' === t)
          return h(e, ['meter', 'progressbar', 'scrollbar', 'slider', 'spinbutton']);
        throw TypeError(
          "No knowledge about abstract role '".concat(t, "'. This is likely a bug :(")
        );
      }
      function P(e, t) {
        var r = a(e.querySelectorAll(t));
        return (
          v(e, 'aria-owns').forEach(function (e) {
            r.push.apply(r, a(e.querySelectorAll(t)));
          }),
          r
        );
      }
      function C(e) {
        var t = e.getPropertyValue('content');
        return /^["'].*["']$/.test(t) ? t.slice(1, -1) : '';
      }
      function q(e) {
        var t = c(e);
        return (
          'button' === t ||
          ('input' === t && 'hidden' !== e.getAttribute('type')) ||
          'meter' === t ||
          'output' === t ||
          'progress' === t ||
          'select' === t ||
          'textarea' === t
        );
      }
      function x(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
          r = new s(),
          n = (function (e) {
            var t = (null === e.ownerDocument ? e : e.ownerDocument).defaultView;
            if (null === t) throw TypeError('no window available');
            return t;
          })(e),
          o = t.compute,
          i = void 0 === o ? 'name' : o,
          l = t.computedStyleSupportsPseudoElements,
          u = void 0 === l ? void 0 !== t.getComputedStyle : l,
          d = t.getComputedStyle,
          p = void 0 === d ? n.getComputedStyle.bind(n) : d,
          f = t.hidden,
          x = void 0 !== f && f;
        function E(e, t) {
          var r,
            n = '';
          if (b(e) && u) {
            var o = C(p(e, '::before'));
            n = ''.concat(o, ' ').concat(n);
          }
          if (
            ((b(e) && 'slot' === c(e)
              ? 0 === (r = e.assignedNodes()).length
                ? a(e.childNodes)
                : r
              : a(e.childNodes).concat(v(e, 'aria-owns'))
            ).forEach(function (e) {
              var r = O(e, {
                  isEmbeddedInLabel: t.isEmbeddedInLabel,
                  isReferenced: !1,
                  recursion: !0,
                }),
                o = 'inline' !== (b(e) ? p(e).getPropertyValue('display') : 'inline') ? ' ' : '';
              n += ''.concat(o).concat(r).concat(o);
            }),
            b(e) && u)
          ) {
            var i = C(p(e, '::after'));
            n = ''.concat(n, ' ').concat(i);
          }
          return n.trim();
        }
        function w(e, t) {
          var n = e.getAttributeNode(t);
          return null === n || r.has(n) || '' === n.value.trim() ? null : (r.add(n), n.value);
        }
        function O(e, t) {
          if (r.has(e)) return '';
          if (
            !x &&
            (function (e, t) {
              if (!b(e)) return !1;
              if (e.hasAttribute('hidden') || 'true' === e.getAttribute('aria-hidden')) return !0;
              var r = t(e);
              return (
                'none' === r.getPropertyValue('display') ||
                'hidden' === r.getPropertyValue('visibility')
              );
            })(e, p) &&
            !t.isReferenced
          )
            return r.add(e), '';
          var n = b(e) ? e.getAttributeNode('aria-labelledby') : null,
            o = null === n || r.has(n) ? [] : v(e, 'aria-labelledby');
          if ('name' === i && !t.isReferenced && o.length > 0)
            return (
              r.add(n),
              o
                .map(function (e) {
                  return O(e, {
                    isEmbeddedInLabel: t.isEmbeddedInLabel,
                    isReferenced: !0,
                    recursion: !1,
                  });
                })
                .join(' ')
            );
          var l =
            t.recursion &&
            (h(e, ['button', 'combobox', 'listbox', 'textbox']) || g(e, 'range')) &&
            'name' === i;
          if (!l) {
            var u = ((b(e) && e.getAttribute('aria-label')) || '').trim();
            if ('' !== u && 'name' === i) return r.add(e), u;
            if (!h(e, ['none', 'presentation'])) {
              var s = (function (e) {
                if (!b(e)) return null;
                if (b(e) && 'fieldset' === c(e)) {
                  r.add(e);
                  for (var t, n = a(e.childNodes), o = 0; o < n.length; o += 1) {
                    var i = n[o];
                    if (b(i) && 'legend' === c(i))
                      return O(i, { isEmbeddedInLabel: !1, isReferenced: !1, recursion: !1 });
                  }
                } else if (b(e) && 'table' === c(e)) {
                  r.add(e);
                  for (var l = a(e.childNodes), u = 0; u < l.length; u += 1) {
                    var s = l[u];
                    if (m(s))
                      return O(s, { isEmbeddedInLabel: !1, isReferenced: !1, recursion: !1 });
                  }
                } else if (b(e) && 'svg' === c(e)) {
                  r.add(e);
                  for (var d = a(e.childNodes), p = 0; p < d.length; p += 1) {
                    var f = d[p];
                    if (
                      (function (e) {
                        return b(e) && void 0 !== e.ownerSVGElement && 'title' === c(e);
                      })(f)
                    )
                      return f.textContent;
                  }
                  return null;
                } else if ('img' === c(e) || 'area' === c(e)) {
                  var v = w(e, 'alt');
                  if (null !== v) return v;
                } else if (b(e) && 'optgroup' === c(e)) {
                  var g = w(e, 'label');
                  if (null !== g) return g;
                }
                if (y(e) && ('button' === e.type || 'submit' === e.type || 'reset' === e.type)) {
                  var P = w(e, 'value');
                  if (null !== P) return P;
                  if ('submit' === e.type) return 'Submit';
                  if ('reset' === e.type) return 'Reset';
                }
                var C =
                  null === (t = e.labels)
                    ? t
                    : void 0 !== t
                      ? a(t)
                      : q(e)
                        ? a(e.ownerDocument.querySelectorAll('label')).filter(function (t) {
                            return (
                              (function (e) {
                                if (void 0 !== e.control) return e.control;
                                var t = e.getAttribute('for');
                                return null !== t
                                  ? e.ownerDocument.getElementById(t)
                                  : (function e(t) {
                                      if (q(t)) return t;
                                      var r = null;
                                      return (
                                        t.childNodes.forEach(function (t) {
                                          if (null === r && b(t)) {
                                            var n = e(t);
                                            null !== n && (r = n);
                                          }
                                        }),
                                        r
                                      );
                                    })(e);
                              })(t) === e
                            );
                          })
                        : null;
                if (null !== C && 0 !== C.length)
                  return (
                    r.add(e),
                    a(C)
                      .map(function (e) {
                        return O(e, { isEmbeddedInLabel: !0, isReferenced: !1, recursion: !0 });
                      })
                      .filter(function (e) {
                        return e.length > 0;
                      })
                      .join(' ')
                  );
                if (y(e) && 'image' === e.type) {
                  var x = w(e, 'alt');
                  if (null !== x) return x;
                  var R = w(e, 'title');
                  return null !== R ? R : 'Submit Query';
                }
                if (h(e, ['button'])) {
                  var j = E(e, { isEmbeddedInLabel: !1, isReferenced: !1 });
                  if ('' !== j) return j;
                }
                return null;
              })(e);
              if (null !== s) return r.add(e), s;
            }
          }
          if (h(e, ['menu'])) return r.add(e), '';
          if (l || t.isEmbeddedInLabel || t.isReferenced) {
            if (h(e, ['combobox', 'listbox'])) {
              r.add(e);
              var d =
                b(e) && 'select' === c(e)
                  ? e.selectedOptions || P(e, '[selected]')
                  : P(e, '[aria-selected="true"]');
              return 0 === d.length
                ? y(e)
                  ? e.value
                  : ''
                : a(d)
                    .map(function (e) {
                      return O(e, {
                        isEmbeddedInLabel: t.isEmbeddedInLabel,
                        isReferenced: !1,
                        recursion: !0,
                      });
                    })
                    .join(' ');
            }
            if (g(e, 'range'))
              return (r.add(e), e.hasAttribute('aria-valuetext'))
                ? e.getAttribute('aria-valuetext')
                : e.hasAttribute('aria-valuenow')
                  ? e.getAttribute('aria-valuenow')
                  : e.getAttribute('value') || '';
            if (h(e, ['textbox']))
              return (
                r.add(e), y(e) || (b(e) && 'textarea' === c(e)) ? e.value : e.textContent || ''
              );
          }
          if (
            h(e, [
              'button',
              'cell',
              'checkbox',
              'columnheader',
              'gridcell',
              'heading',
              'label',
              'legend',
              'link',
              'menuitem',
              'menuitemcheckbox',
              'menuitemradio',
              'option',
              'radio',
              'row',
              'rowheader',
              'switch',
              'tab',
              'tooltip',
              'treeitem',
            ]) ||
            (b(e) && t.isReferenced) ||
            m(e)
          ) {
            var f = E(e, { isEmbeddedInLabel: t.isEmbeddedInLabel, isReferenced: !1 });
            if ('' !== f) return r.add(e), f;
          }
          if (e.nodeType === e.TEXT_NODE) return r.add(e), e.textContent || '';
          if (t.recursion)
            return r.add(e), E(e, { isEmbeddedInLabel: t.isEmbeddedInLabel, isReferenced: !1 });
          var C = b(e) ? w(e, 'title') : null;
          return null !== C ? (r.add(e), C) : (r.add(e), '');
        }
        return O(e, { isEmbeddedInLabel: !1, isReferenced: 'description' === i, recursion: !1 })
          .trim()
          .replace(/\s\s+/g, ' ');
      }
      function E(e) {
        return (E =
          'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  'function' == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? 'symbol'
                  : typeof e;
              })(e);
      }
      function w(e, t) {
        var r = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var n = Object.getOwnPropertySymbols(e);
          t &&
            (n = n.filter(function (t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })),
            r.push.apply(r, n);
        }
        return r;
      }
      function O(e) {
        for (var t = 1; t < arguments.length; t++) {
          var r = null != arguments[t] ? arguments[t] : {};
          t % 2
            ? w(Object(r), !0).forEach(function (t) {
                var n, o, a;
                (n = e),
                  (o = t),
                  (a = r[t]),
                  (o = (function (e) {
                    var t = (function (e, t) {
                      if ('object' !== E(e) || null === e) return e;
                      var r = e[Symbol.toPrimitive];
                      if (void 0 !== r) {
                        var n = r.call(e, t || 'default');
                        if ('object' !== E(n)) return n;
                        throw TypeError('@@toPrimitive must return a primitive value.');
                      }
                      return ('string' === t ? String : Number)(e);
                    })(e, 'string');
                    return 'symbol' === E(t) ? t : String(t);
                  })(o)) in n
                    ? Object.defineProperty(n, o, {
                        value: a,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0,
                      })
                    : (n[o] = a);
              })
            : Object.getOwnPropertyDescriptors
              ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r))
              : w(Object(r)).forEach(function (t) {
                  Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t));
                });
        }
        return e;
      }
      function R(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
          r = v(e, 'aria-describedby')
            .map(function (e) {
              return x(e, O(O({}, t), {}, { compute: 'description' }));
            })
            .join(' ');
        if ('' === r) {
          var n = e.getAttribute('title');
          r = null === n ? '' : n;
        }
        return r;
      }
      function j(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        return h(e, [
          'caption',
          'code',
          'deletion',
          'emphasis',
          'generic',
          'insertion',
          'paragraph',
          'presentation',
          'strong',
          'subscript',
          'superscript',
        ])
          ? ''
          : x(e, t);
      }
    },
    15230: (e, t, r) => {
      'use strict';
      var n = r(27812),
        o = r(32604),
        a = r(16263),
        i = r(70445);
      e.exports = function (e) {
        if (e && 'object' == typeof e) {
          if (n(e)) return 'Map';
          if (o(e)) return 'Set';
          if (a(e)) return 'WeakMap';
          if (i(e)) return 'WeakSet';
        }
        return !1;
      };
    },
    16134: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-disabled': null },
          relatedConcepts: [{ concept: { name: 'input' }, module: 'XForms' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget']],
        });
    },
    16200: (e, t, r) => {
      'use strict';
      var n = r(47600),
        o = n('Object.prototype.toString'),
        a = r(2427)(),
        i = r(52691);
      if (a) {
        var l = n('Symbol.prototype.toString'),
          u = i(/^Symbol\(.*\)$/);
        e.exports = function (e) {
          if ('symbol' == typeof e) return !0;
          if (!e || 'object' != typeof e || '[object Symbol]' !== o(e)) return !1;
          try {
            return 'symbol' == typeof e.valueOf() && u(l(e));
          } catch (e) {
            return !1;
          }
        };
      } else
        e.exports = function (e) {
          return !1;
        };
    },
    16263: e => {
      'use strict';
      var t,
        r = 'function' == typeof WeakMap && WeakMap.prototype ? WeakMap : null,
        n = 'function' == typeof WeakSet && WeakSet.prototype ? WeakSet : null;
      r ||
        (t = function (e) {
          return !1;
        });
      var o = r ? r.prototype.has : null,
        a = n ? n.prototype.has : null;
      t ||
        o ||
        (t = function (e) {
          return !1;
        }),
        (e.exports =
          t ||
          function (e) {
            if (!e || 'object' != typeof e) return !1;
            try {
              if ((o.call(e, o), a))
                try {
                  a.call(e, a);
                } catch (e) {
                  return !0;
                }
              return e instanceof r;
            } catch (e) {}
            return !1;
          });
    },
    16415: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'aside' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    16751: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-disabled': null, 'aria-expanded': null, 'aria-haspopup': null },
          relatedConcepts: [
            { concept: { attributes: [{ name: 'href' }], name: 'a' }, module: 'HTML' },
            { concept: { attributes: [{ name: 'href' }], name: 'area' }, module: 'HTML' },
            { concept: { attributes: [{ name: 'href' }], name: 'link' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command']],
        });
    },
    16866: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'noteref [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    16975: (e, t, r) => {
      'use strict';
      var n = r(97783),
        o = r(66380),
        a = r(64215),
        i = r(9278);
      e.exports = function (e, t, r) {
        if (!e || ('object' != typeof e && 'function' != typeof e))
          throw new a('`obj` must be an object or a function`');
        if ('string' != typeof t && 'symbol' != typeof t)
          throw new a('`property` must be a string or a symbol`');
        if (arguments.length > 3 && 'boolean' != typeof arguments[3] && null !== arguments[3])
          throw new a('`nonEnumerable`, if provided, must be a boolean or null');
        if (arguments.length > 4 && 'boolean' != typeof arguments[4] && null !== arguments[4])
          throw new a('`nonWritable`, if provided, must be a boolean or null');
        if (arguments.length > 5 && 'boolean' != typeof arguments[5] && null !== arguments[5])
          throw new a('`nonConfigurable`, if provided, must be a boolean or null');
        if (arguments.length > 6 && 'boolean' != typeof arguments[6])
          throw new a('`loose`, if provided, must be a boolean');
        var l = arguments.length > 3 ? arguments[3] : null,
          u = arguments.length > 4 ? arguments[4] : null,
          s = arguments.length > 5 ? arguments[5] : null,
          c = arguments.length > 6 && arguments[6],
          d = !!i && i(e, t);
        if (n)
          n(e, t, {
            configurable: null === s && d ? d.configurable : !s,
            enumerable: null === l && d ? d.enumerable : !l,
            value: r,
            writable: null === u && d ? d.writable : !u,
          });
        else if (!c && (l || u || s))
          throw new o(
            'This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.'
          );
        else e[t] = r;
      };
    },
    17236: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'tbody' }, module: 'HTML' },
            { concept: { name: 'tfoot' }, module: 'HTML' },
            { concept: { name: 'thead' }, module: 'HTML' },
          ],
          requireContextRole: ['grid', 'table', 'treegrid'],
          requiredContextRole: ['grid', 'table', 'treegrid'],
          requiredOwnedElements: [['row']],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    19334: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    19398: (e, t, r) => {
      'use strict';
      var n = r(36074),
        o = r(64215),
        a = function (e, t, r) {
          for (var n, o = e; null != (n = o.next); o = n)
            if (n.key === t) return (o.next = n.next), r || ((n.next = e.next), (e.next = n)), n;
        },
        i = function (e, t) {
          if (e) {
            var r = a(e, t);
            return r && r.value;
          }
        },
        l = function (e, t, r) {
          var n = a(e, t);
          n ? (n.value = r) : (e.next = { key: t, next: e.next, value: r });
        },
        u = function (e, t) {
          if (e) return a(e, t, !0);
        };
      e.exports = function () {
        var e,
          t = {
            assert: function (e) {
              if (!t.has(e)) throw new o('Side channel does not contain ' + n(e));
            },
            delete: function (t) {
              var r = e && e.next,
                n = u(e, t);
              return n && r && r === n && (e = void 0), !!n;
            },
            get: function (t) {
              return i(e, t);
            },
            has: function (t) {
              var r;
              return !!(r = e) && !!a(r, t);
            },
            set: function (t, r) {
              e || (e = { next: void 0 }), l(e, t, r);
            },
          };
        return t;
      };
    },
    19543: e => {
      'use strict';
      e.exports = URIError;
    },
    20284: (e, t, r) => {
      'use strict';
      var n = r(38165);
      e.exports = function () {
        return 'function' == typeof Object.is ? Object.is : n;
      };
    },
    21089: (e, t, r) => {
      'use strict';
      var n = r(48731),
        o = r(66380),
        a = 'object' == typeof StopIteration ? StopIteration : null;
      e.exports = function (e) {
        if (!a) throw new o('this environment lacks StopIteration');
        n.set(e, '[[Done]]', !1);
        var t = {
          next: function () {
            var e = n.get(this, '[[Iterator]]'),
              t = !!n.get(e, '[[Done]]');
            try {
              return { done: t, value: t ? void 0 : e.next() };
            } catch (t) {
              if ((n.set(e, '[[Done]]', !0), t !== a)) throw t;
              return { done: !0, value: void 0 };
            }
          },
        };
        return n.set(t, '[[Iterator]]', e), t;
      };
    },
    21133: (e, t, r) => {
      'use strict';
      e.exports = r(4593);
    },
    21405: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    22364: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'part [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    22811: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'abstract [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    23461: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'footnote [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    23680: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    24379: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-valuemax': null, 'aria-valuemin': null, 'aria-valuenow': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    24428: (e, t, r) => {
      'use strict';
      var n = r(47600),
        o = n('Boolean.prototype.toString'),
        a = n('Object.prototype.toString'),
        i = function (e) {
          try {
            return o(e), !0;
          } catch (e) {
            return !1;
          }
        },
        l = r(79056)();
      e.exports = function (e) {
        return (
          'boolean' == typeof e ||
          (null !== e && 'object' == typeof e && (l ? i(e) : '[object Boolean]' === a(e)))
        );
      };
    },
    24645: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'acknowledgments [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    25082: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-checked': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'checkbox' }], name: 'input' },
              module: 'HTML',
            },
            { concept: { name: 'option' }, module: 'ARIA' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    25220: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-sort': null },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'scope', value: 'row' }], name: 'th' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'scope', value: 'rowgroup' }], name: 'th' },
              module: 'HTML',
            },
          ],
          requireContextRole: ['row', 'rowgroup'],
          requiredContextRole: ['row', 'rowgroup'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'cell'],
            ['roletype', 'structure', 'section', 'cell', 'gridcell'],
            ['roletype', 'widget', 'gridcell'],
            ['roletype', 'structure', 'sectionhead'],
          ],
        });
    },
    25308: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'prologue [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    25579: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    26203: e => {
      'use strict';
      e.exports = [
        'Float16Array',
        'Float32Array',
        'Float64Array',
        'Int8Array',
        'Int16Array',
        'Int32Array',
        'Uint8Array',
        'Uint8ClampedArray',
        'Uint16Array',
        'Uint32Array',
        'BigInt64Array',
        'BigUint64Array',
      ];
    },
    27099: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-level': '2' },
          relatedConcepts: [
            { concept: { name: 'h1' }, module: 'HTML' },
            { concept: { name: 'h2' }, module: 'HTML' },
            { concept: { name: 'h3' }, module: 'HTML' },
            { concept: { name: 'h4' }, module: 'HTML' },
            { concept: { name: 'h5' }, module: 'HTML' },
            { concept: { name: 'h6' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-level': '2' },
          superClass: [['roletype', 'structure', 'sectionhead']],
        });
    },
    27174: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'EPUB biblioentry [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: ['doc-bibliography'],
          requiredContextRole: ['doc-bibliography'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'listitem']],
        });
    },
    27812: e => {
      'use strict';
      var t,
        r = 'function' == typeof Map && Map.prototype ? Map : null,
        n = 'function' == typeof Set && Set.prototype ? Set : null;
      r ||
        (t = function (e) {
          return !1;
        });
      var o = r ? Map.prototype.has : null,
        a = n ? Set.prototype.has : null;
      t ||
        o ||
        (t = function (e) {
          return !1;
        }),
        (e.exports =
          t ||
          function (e) {
            if (!e || 'object' != typeof e) return !1;
            try {
              if ((o.call(e), a))
                try {
                  a.call(e);
                } catch (e) {
                  return !0;
                }
              return e instanceof r;
            } catch (e) {}
            return !1;
          });
    },
    28265: (e, t, r) => {
      'use strict';
      var n = r(6333),
        o = r(10701),
        a = r(45703);
      e.exports = n
        ? function (e) {
            return n(e);
          }
        : o
          ? function (e) {
              if (!e || ('object' != typeof e && 'function' != typeof e))
                throw TypeError('getProto: not an object');
              return o(e);
            }
          : a
            ? function (e) {
                return a(e);
              }
            : null;
    },
    28621: e => {
      'use strict';
      e.exports = 'undefined' != typeof Reflect && Reflect && Reflect.apply;
    },
    28933: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-colcount': null, 'aria-rowcount': null },
          relatedConcepts: [{ concept: { name: 'table' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['row'], ['row', 'rowgroup']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    29452: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    29703: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-multiselectable': null, 'aria-readonly': null },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'role', value: 'grid' }], name: 'table' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['row'], ['row', 'rowgroup']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite'],
            ['roletype', 'structure', 'section', 'table'],
          ],
        });
    },
    30084: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    30831: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    31179: (e, t, r) => {
      'use strict';
      if (r(32973)()) {
        var n = BigInt.prototype.valueOf,
          o = function (e) {
            try {
              return n.call(e), !0;
            } catch (e) {}
            return !1;
          };
        e.exports = function (e) {
          return (
            null != e &&
            'boolean' != typeof e &&
            'string' != typeof e &&
            'number' != typeof e &&
            'symbol' != typeof e &&
            'function' != typeof e &&
            ('bigint' == typeof e || o(e))
          );
        };
      } else
        e.exports = function (e) {
          return !1;
        };
    },
    31192: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = i(r(42738)),
        o = i(r(70863)),
        a = i(r(12407));
      function i(e) {
        return e && e.__esModule ? e : { default: e };
      }
      function l(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                i = !0,
                l = !1;
              try {
                for (
                  o = o.call(e);
                  !(i = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  i = !0
                );
              } catch (e) {
                (l = !0), (n = e);
              } finally {
                try {
                  i || null == o.return || o.return();
                } finally {
                  if (l) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          u(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function u(e, t) {
        if (e) {
          if ('string' == typeof e) return s(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return s(e, t);
        }
      }
      function s(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      for (var c = [], d = a.default.keys(), p = 0; p < d.length; p++) {
        var f = d[p],
          b = a.default.get(f);
        if (b)
          for (var m = [].concat(b.baseConcepts, b.relatedConcepts), y = 0; y < m.length; y++) {
            var v = m[y];
            if ('HTML' === v.module) {
              var h = v.concept;
              h &&
                (function () {
                  var e = JSON.stringify(h),
                    t = c.find(function (t) {
                      return JSON.stringify(t[0]) === e;
                    }),
                    r = void 0;
                  r = t ? t[1] : [];
                  for (var n = !0, o = 0; o < r.length; o++)
                    if (r[o] === f) {
                      n = !1;
                      break;
                    }
                  n && r.push(f), c.push([h, r]);
                })();
            }
          }
      }
      var g = {
        entries: function () {
          return c;
        },
        forEach: function (e) {
          var t,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
            n = (function (e, t) {
              var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
              if (!r) {
                if (Array.isArray(e) || (r = u(e))) {
                  r && (e = r);
                  var n = 0,
                    o = function () {};
                  return {
                    s: o,
                    n: function () {
                      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                    },
                    e: function (e) {
                      throw e;
                    },
                    f: o,
                  };
                }
                throw TypeError(
                  'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                );
              }
              var a,
                i = !0,
                l = !1;
              return {
                s: function () {
                  r = r.call(e);
                },
                n: function () {
                  var e = r.next();
                  return (i = e.done), e;
                },
                e: function (e) {
                  (l = !0), (a = e);
                },
                f: function () {
                  try {
                    i || null == r.return || r.return();
                  } finally {
                    if (l) throw a;
                  }
                },
              };
            })(c);
          try {
            for (n.s(); !(t = n.n()).done; ) {
              var o = l(t.value, 2),
                a = o[0],
                i = o[1];
              e.call(r, i, a, c);
            }
          } catch (e) {
            n.e(e);
          } finally {
            n.f();
          }
        },
        get: function (e) {
          var t = c.find(function (t) {
            return (0, n.default)(e, t[0]);
          });
          return t && t[1];
        },
        has: function (e) {
          return !!g.get(e);
        },
        keys: function () {
          return c.map(function (e) {
            return l(e, 1)[0];
          });
        },
        values: function () {
          return c.map(function (e) {
            return l(e, 2)[1];
          });
        },
      };
      t.default = (0, o.default)(g, g.entries());
    },
    31553: e => {
      'use strict';
      e.exports = Math.abs;
    },
    31630: (e, t, r) => {
      'use strict';
      var n = r(71005),
        o = r(34802),
        a = o(n('String.prototype.indexOf'));
      e.exports = function (e, t) {
        var r = n(e, !!t);
        return 'function' == typeof r && a(e, '.prototype.') > -1 ? o(r) : r;
      };
    },
    31777: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = t.DEFAULT_OPTIONS = void 0),
        (t.format = L),
        (t.plugins = void 0);
      var n = p(r(43320)),
        o = r(58460),
        a = p(r(61310)),
        i = p(r(39742)),
        l = p(r(11810)),
        u = p(r(58894)),
        s = p(r(2798)),
        c = p(r(92123)),
        d = p(r(61304));
      function p(e) {
        return e && e.__esModule ? e : { default: e };
      }
      let f = Object.prototype.toString,
        b = Date.prototype.toISOString,
        m = Error.prototype.toString,
        y = RegExp.prototype.toString,
        v = e => ('function' == typeof e.constructor && e.constructor.name) || 'Object',
        h = e => 'undefined' != typeof window && e === window,
        g = /^Symbol\((.*)\)(.*)$/,
        P = /\n/gi;
      class C extends Error {
        constructor(e, t) {
          super(e), (this.stack = t), (this.name = this.constructor.name);
        }
      }
      function q(e, t) {
        return t ? '[Function ' + (e.name || 'anonymous') + ']' : '[Function]';
      }
      function x(e) {
        return String(e).replace(g, 'Symbol($1)');
      }
      function E(e) {
        return '[' + m.call(e) + ']';
      }
      function w(e, t, r, n) {
        if (!0 === e || !1 === e) return '' + e;
        if (void 0 === e) return 'undefined';
        if (null === e) return 'null';
        let o = typeof e;
        if ('number' === o) return Object.is(e, -0) ? '-0' : String(e);
        if ('bigint' === o) return String(`${e}n`);
        if ('string' === o) return n ? '"' + e.replace(/"|\\/g, '\\$&') + '"' : '"' + e + '"';
        if ('function' === o) return q(e, t);
        if ('symbol' === o) return x(e);
        let a = f.call(e);
        return '[object WeakMap]' === a
          ? 'WeakMap {}'
          : '[object WeakSet]' === a
            ? 'WeakSet {}'
            : '[object Function]' === a || '[object GeneratorFunction]' === a
              ? q(e, t)
              : '[object Symbol]' === a
                ? x(e)
                : '[object Date]' === a
                  ? isNaN(+e)
                    ? 'Date { NaN }'
                    : b.call(e)
                  : '[object Error]' === a
                    ? E(e)
                    : '[object RegExp]' === a
                      ? r
                        ? y.call(e).replace(/[\\^$*+?.()|[\]{}]/g, '\\$&')
                        : y.call(e)
                      : e instanceof Error
                        ? E(e)
                        : null;
      }
      function O(e, t, r, n, a, i) {
        if (-1 !== a.indexOf(e)) return '[Circular]';
        (a = a.slice()).push(e);
        let l = ++n > t.maxDepth,
          u = t.min;
        if (t.callToJSON && !l && e.toJSON && 'function' == typeof e.toJSON && !i)
          return S(e.toJSON(), t, r, n, a, !0);
        let s = f.call(e);
        return '[object Arguments]' === s
          ? l
            ? '[Arguments]'
            : (u ? '' : 'Arguments ') + '[' + (0, o.printListItems)(e, t, r, n, a, S) + ']'
          : '[object Array]' === s ||
              '[object ArrayBuffer]' === s ||
              '[object DataView]' === s ||
              '[object Float32Array]' === s ||
              '[object Float64Array]' === s ||
              '[object Int8Array]' === s ||
              '[object Int16Array]' === s ||
              '[object Int32Array]' === s ||
              '[object Uint8Array]' === s ||
              '[object Uint8ClampedArray]' === s ||
              '[object Uint16Array]' === s ||
              '[object Uint32Array]' === s
            ? l
              ? '[' + e.constructor.name + ']'
              : (u
                  ? ''
                  : t.printBasicPrototype || 'Array' !== e.constructor.name
                    ? e.constructor.name + ' '
                    : '') +
                '[' +
                (0, o.printListItems)(e, t, r, n, a, S) +
                ']'
            : '[object Map]' === s
              ? l
                ? '[Map]'
                : 'Map {' + (0, o.printIteratorEntries)(e.entries(), t, r, n, a, S, ' => ') + '}'
              : '[object Set]' === s
                ? l
                  ? '[Set]'
                  : 'Set {' + (0, o.printIteratorValues)(e.values(), t, r, n, a, S) + '}'
                : l || h(e)
                  ? '[' + v(e) + ']'
                  : (u ? '' : t.printBasicPrototype || 'Object' !== v(e) ? v(e) + ' ' : '') +
                    '{' +
                    (0, o.printObjectProperties)(e, t, r, n, a, S) +
                    '}';
      }
      function R(e, t, r, n, o, a) {
        let i;
        try {
          i =
            null != e.serialize
              ? e.serialize(t, r, n, o, a, S)
              : e.print(
                  t,
                  e => S(e, r, n, o, a),
                  e => {
                    let t = n + r.indent;
                    return t + e.replace(P, '\n' + t);
                  },
                  { edgeSpacing: r.spacingOuter, min: r.min, spacing: r.spacingInner },
                  r.colors
                );
        } catch (e) {
          throw new C(e.message, e.stack);
        }
        if ('string' != typeof i)
          throw Error(
            `pretty-format: Plugin must return type "string" but instead returned "${typeof i}".`
          );
        return i;
      }
      function j(e, t) {
        for (let r = 0; r < e.length; r++)
          try {
            if (e[r].test(t)) return e[r];
          } catch (e) {
            throw new C(e.message, e.stack);
          }
        return null;
      }
      function S(e, t, r, n, o, a) {
        let i = j(t.plugins, e);
        if (null !== i) return R(i, e, t, r, n, o);
        let l = w(e, t.printFunctionName, t.escapeRegex, t.escapeString);
        return null !== l ? l : O(e, t, r, n, o, a);
      }
      let _ = { comment: 'gray', content: 'reset', prop: 'yellow', tag: 'cyan', value: 'green' },
        A = Object.keys(_),
        M = {
          callToJSON: !0,
          compareKeys: void 0,
          escapeRegex: !1,
          escapeString: !0,
          highlight: !1,
          indent: 2,
          maxDepth: 1 / 0,
          min: !1,
          plugins: [],
          printBasicPrototype: !0,
          printFunctionName: !0,
          theme: _,
        };
      t.DEFAULT_OPTIONS = M;
      let T = e =>
          A.reduce((t, r) => {
            let o = e.theme && void 0 !== e.theme[r] ? e.theme[r] : _[r],
              a = o && n.default[o];
            if (a && 'string' == typeof a.close && 'string' == typeof a.open) t[r] = a;
            else
              throw Error(
                `pretty-format: Option "theme" has a key "${r}" whose value "${o}" is undefined in ansi-styles.`
              );
            return t;
          }, Object.create(null)),
        I = () => A.reduce((e, t) => ((e[t] = { close: '', open: '' }), e), Object.create(null)),
        k = e => (e && void 0 !== e.printFunctionName ? e.printFunctionName : M.printFunctionName),
        N = e => (e && void 0 !== e.escapeRegex ? e.escapeRegex : M.escapeRegex),
        F = e => (e && void 0 !== e.escapeString ? e.escapeString : M.escapeString),
        B = e => {
          var t;
          return {
            callToJSON: e && void 0 !== e.callToJSON ? e.callToJSON : M.callToJSON,
            colors: e && e.highlight ? T(e) : I(),
            compareKeys: e && 'function' == typeof e.compareKeys ? e.compareKeys : M.compareKeys,
            escapeRegex: N(e),
            escapeString: F(e),
            indent:
              e && e.min
                ? ''
                : Array((e && void 0 !== e.indent ? e.indent : M.indent) + 1).join(' '),
            maxDepth: e && void 0 !== e.maxDepth ? e.maxDepth : M.maxDepth,
            min: e && void 0 !== e.min ? e.min : M.min,
            plugins: e && void 0 !== e.plugins ? e.plugins : M.plugins,
            printBasicPrototype: null == (t = null == e ? void 0 : e.printBasicPrototype) || t,
            printFunctionName: k(e),
            spacingInner: e && e.min ? ' ' : '\n',
            spacingOuter: e && e.min ? '' : '\n',
          };
        };
      function L(e, t) {
        if (t) {
          if (
            (Object.keys(t).forEach(e => {
              if (!M.hasOwnProperty(e)) throw Error(`pretty-format: Unknown option "${e}".`);
            }),
            t.min && void 0 !== t.indent && 0 !== t.indent)
          )
            throw Error('pretty-format: Options "min" and "indent" cannot be used together.');
          if (void 0 !== t.theme) {
            if (null === t.theme) throw Error('pretty-format: Option "theme" must not be null.');
            if ('object' != typeof t.theme)
              throw Error(
                `pretty-format: Option "theme" must be of type "object" but instead received "${typeof t.theme}".`
              );
          }
          if (t.plugins) {
            let r = j(t.plugins, e);
            if (null !== r) return R(r, e, B(t), '', 0, []);
          }
        }
        let r = w(e, k(t), N(t), F(t));
        return null !== r ? r : O(e, B(t), '', 0, []);
      }
      (t.plugins = {
        AsymmetricMatcher: a.default,
        ConvertAnsi: i.default,
        DOMCollection: l.default,
        DOMElement: u.default,
        Immutable: s.default,
        ReactElement: c.default,
        ReactTestComponent: d.default,
      }),
        (t.default = L);
    },
    32519: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'preface [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    32604: e => {
      'use strict';
      var t,
        r = 'function' == typeof Map && Map.prototype ? Map : null,
        n = 'function' == typeof Set && Set.prototype ? Set : null;
      n ||
        (t = function (e) {
          return !1;
        });
      var o = r ? Map.prototype.has : null,
        a = n ? Set.prototype.has : null;
      t ||
        a ||
        (t = function (e) {
          return !1;
        }),
        (e.exports =
          t ||
          function (e) {
            if (!e || 'object' != typeof e) return !1;
            try {
              if ((a.call(e), o))
                try {
                  o.call(e);
                } catch (e) {
                  return !0;
                }
              return e instanceof n;
            } catch (e) {}
            return !1;
          });
    },
    32641: e => {
      'use strict';
      e.exports = Math.round;
    },
    32777: (e, t, r) => {
      'use strict';
      var n,
        o = r(47600),
        a = r(79056)(),
        i = r(11351),
        l = r(9278);
      if (a) {
        var u = o('RegExp.prototype.exec'),
          s = {},
          c = function () {
            throw s;
          },
          d = { toString: c, valueOf: c };
        'symbol' == typeof Symbol.toPrimitive && (d[Symbol.toPrimitive] = c),
          (n = function (e) {
            if (!e || 'object' != typeof e) return !1;
            var t = l(e, 'lastIndex');
            if (!(t && i(t, 'value'))) return !1;
            try {
              u(e, d);
            } catch (e) {
              return e === s;
            }
          });
      } else {
        var p = o('Object.prototype.toString');
        n = function (e) {
          return (
            !!e && ('object' == typeof e || 'function' == typeof e) && '[object RegExp]' === p(e)
          );
        };
      }
      e.exports = n;
    },
    32973: e => {
      'use strict';
      var t = 'undefined' != typeof BigInt && BigInt;
      e.exports = function () {
        return (
          'function' == typeof t &&
          'function' == typeof BigInt &&
          'bigint' == typeof t(42) &&
          'bigint' == typeof BigInt(42)
        );
      };
    },
    34531: (e, t, r) => {
      'use strict';
      var n = r(47600),
        o = n('Date.prototype.getDay'),
        a = function (e) {
          try {
            return o(e), !0;
          } catch (e) {
            return !1;
          }
        },
        i = n('Object.prototype.toString'),
        l = r(79056)();
      e.exports = function (e) {
        return 'object' == typeof e && null !== e && (l ? a(e) : '[object Date]' === i(e));
      };
    },
    34802: (e, t, r) => {
      'use strict';
      var n = r(69206),
        o = r(97783),
        a = r(6390),
        i = r(58893);
      (e.exports = function (e) {
        var t = a(arguments),
          r = e.length - (arguments.length - 1);
        return n(t, 1 + (r > 0 ? r : 0), !0);
      }),
        o ? o(e.exports, 'apply', { value: i }) : (e.exports.apply = i);
    },
    35127: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'button' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [['roletype', 'widget', 'input', 'checkbox']],
        });
    },
    35794: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'rearnote [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: ['doc-endnotes'],
          requiredContextRole: ['doc-endnotes'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'listitem']],
        });
    },
    36074: (e, t, r) => {
      var n = 'function' == typeof Map && Map.prototype,
        o =
          Object.getOwnPropertyDescriptor && n
            ? Object.getOwnPropertyDescriptor(Map.prototype, 'size')
            : null,
        a = n && o && 'function' == typeof o.get ? o.get : null,
        i = n && Map.prototype.forEach,
        l = 'function' == typeof Set && Set.prototype,
        u =
          Object.getOwnPropertyDescriptor && l
            ? Object.getOwnPropertyDescriptor(Set.prototype, 'size')
            : null,
        s = l && u && 'function' == typeof u.get ? u.get : null,
        c = l && Set.prototype.forEach,
        d = 'function' == typeof WeakMap && WeakMap.prototype ? WeakMap.prototype.has : null,
        p = 'function' == typeof WeakSet && WeakSet.prototype ? WeakSet.prototype.has : null,
        f = 'function' == typeof WeakRef && WeakRef.prototype ? WeakRef.prototype.deref : null,
        b = Boolean.prototype.valueOf,
        m = Object.prototype.toString,
        y = Function.prototype.toString,
        v = String.prototype.match,
        h = String.prototype.slice,
        g = String.prototype.replace,
        P = String.prototype.toUpperCase,
        C = String.prototype.toLowerCase,
        q = RegExp.prototype.test,
        x = Array.prototype.concat,
        E = Array.prototype.join,
        w = Array.prototype.slice,
        O = Math.floor,
        R = 'function' == typeof BigInt ? BigInt.prototype.valueOf : null,
        j = Object.getOwnPropertySymbols,
        S =
          'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
            ? Symbol.prototype.toString
            : null,
        _ = 'function' == typeof Symbol && 'object' == typeof Symbol.iterator,
        A =
          'function' == typeof Symbol &&
          Symbol.toStringTag &&
          (typeof Symbol.toStringTag === _ ? 'object' : 'symbol')
            ? Symbol.toStringTag
            : null,
        M = Object.prototype.propertyIsEnumerable,
        T =
          ('function' == typeof Reflect ? Reflect.getPrototypeOf : Object.getPrototypeOf) ||
          ([].__proto__ === Array.prototype
            ? function (e) {
                return e.__proto__;
              }
            : null);
      function I(e, t) {
        if (e === 1 / 0 || e === -1 / 0 || e != e || (e && e > -1e3 && e < 1e3) || q.call(/e/, t))
          return t;
        var r = /[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;
        if ('number' == typeof e) {
          var n = e < 0 ? -O(-e) : O(e);
          if (n !== e) {
            var o = String(n),
              a = h.call(t, o.length + 1);
            return g.call(o, r, '$&_') + '.' + g.call(g.call(a, /([0-9]{3})/g, '$&_'), /_$/, '');
          }
        }
        return g.call(t, r, '$&_');
      }
      var k = r(22179),
        N = k.custom,
        F = V(N) ? N : null,
        B = { __proto__: null, double: '"', single: "'" },
        L = { __proto__: null, double: /(["\\])/g, single: /(['\\])/g };
      function U(e, t, r) {
        var n = B[r.quoteStyle || t];
        return n + e + n;
      }
      function D(e) {
        return !A || !('object' == typeof e && (A in e || void 0 !== e[A]));
      }
      function H(e) {
        return '[object Array]' === G(e) && D(e);
      }
      function $(e) {
        return '[object RegExp]' === G(e) && D(e);
      }
      function V(e) {
        if (_) return e && 'object' == typeof e && e instanceof Symbol;
        if ('symbol' == typeof e) return !0;
        if (!e || 'object' != typeof e || !S) return !1;
        try {
          return S.call(e), !0;
        } catch (e) {}
        return !1;
      }
      e.exports = function e(t, n, o, l) {
        var u,
          m,
          P,
          q,
          O,
          j = n || {};
        if (z(j, 'quoteStyle') && !z(B, j.quoteStyle))
          throw TypeError('option "quoteStyle" must be "single" or "double"');
        if (
          z(j, 'maxStringLength') &&
          ('number' == typeof j.maxStringLength
            ? j.maxStringLength < 0 && j.maxStringLength !== 1 / 0
            : null !== j.maxStringLength)
        )
          throw TypeError(
            'option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`'
          );
        var N = !z(j, 'customInspect') || j.customInspect;
        if ('boolean' != typeof N && 'symbol' !== N)
          throw TypeError(
            'option "customInspect", if provided, must be `true`, `false`, or `\'symbol\'`'
          );
        if (
          z(j, 'indent') &&
          null !== j.indent &&
          '	' !== j.indent &&
          !(parseInt(j.indent, 10) === j.indent && j.indent > 0)
        )
          throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');
        if (z(j, 'numericSeparator') && 'boolean' != typeof j.numericSeparator)
          throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');
        var W = j.numericSeparator;
        if (void 0 === t) return 'undefined';
        if (null === t) return 'null';
        if ('boolean' == typeof t) return t ? 'true' : 'false';
        if ('string' == typeof t)
          return (function e(t, r) {
            if (t.length > r.maxStringLength) {
              var n = t.length - r.maxStringLength;
              return (
                e(h.call(t, 0, r.maxStringLength), r) +
                ('... ' + n) +
                ' more character' +
                (n > 1 ? 's' : '')
              );
            }
            var o = L[r.quoteStyle || 'single'];
            return (
              (o.lastIndex = 0), U(g.call(g.call(t, o, '\\$1'), /[\x00-\x1f]/g, X), 'single', r)
            );
          })(t, j);
        if ('number' == typeof t) {
          if (0 === t) return 1 / 0 / t > 0 ? '0' : '-0';
          var et = String(t);
          return W ? I(t, et) : et;
        }
        if ('bigint' == typeof t) {
          var er = String(t) + 'n';
          return W ? I(t, er) : er;
        }
        var en = void 0 === j.depth ? 5 : j.depth;
        if ((void 0 === o && (o = 0), o >= en && en > 0 && 'object' == typeof t))
          return H(t) ? '[Array]' : '[Object]';
        var eo = (function (e, t) {
          var r;
          if ('	' === e.indent) r = '	';
          else {
            if ('number' != typeof e.indent || !(e.indent > 0)) return null;
            r = E.call(Array(e.indent + 1), ' ');
          }
          return { base: r, prev: E.call(Array(t + 1), r) };
        })(j, o);
        if (void 0 === l) l = [];
        else if (J(l, t) >= 0) return '[Circular]';
        function ea(t, r, n) {
          if ((r && (l = w.call(l)).push(r), n)) {
            var a = { depth: j.depth };
            return z(j, 'quoteStyle') && (a.quoteStyle = j.quoteStyle), e(t, a, o + 1, l);
          }
          return e(t, j, o + 1, l);
        }
        if ('function' == typeof t && !$(t)) {
          var ei = (function (e) {
              if (e.name) return e.name;
              var t = v.call(y.call(e), /^function\s*([\w$]+)/);
              return t ? t[1] : null;
            })(t),
            el = ee(t, ea);
          return (
            '[Function' +
            (ei ? ': ' + ei : ' (anonymous)') +
            ']' +
            (el.length > 0 ? ' { ' + E.call(el, ', ') + ' }' : '')
          );
        }
        if (V(t)) {
          var eu = _ ? g.call(String(t), /^(Symbol\(.*\))_[^)]*$/, '$1') : S.call(t);
          return 'object' != typeof t || _ ? eu : K(eu);
        }
        if (
          (es = t) &&
          'object' == typeof es &&
          (('undefined' != typeof HTMLElement && es instanceof HTMLElement) ||
            ('string' == typeof es.nodeName && 'function' == typeof es.getAttribute))
        ) {
          for (
            var es, ec, ed = '<' + C.call(String(t.nodeName)), ep = t.attributes || [], ef = 0;
            ef < ep.length;
            ef++
          ) {
            ed +=
              ' ' +
              ep[ef].name +
              '=' +
              U(((ec = ep[ef].value), g.call(String(ec), /"/g, '&quot;')), 'double', j);
          }
          return (
            (ed += '>'),
            t.childNodes && t.childNodes.length && (ed += '...'),
            (ed += '</' + C.call(String(t.nodeName)) + '>')
          );
        }
        if (H(t)) {
          if (0 === t.length) return '[]';
          var eb = ee(t, ea);
          return eo &&
            !(function (e) {
              for (var t = 0; t < e.length; t++) if (J(e[t], '\n') >= 0) return !1;
              return !0;
            })(eb)
            ? '[' + Q(eb, eo) + ']'
            : '[ ' + E.call(eb, ', ') + ' ]';
        }
        if ('[object Error]' === G((u = t)) && D(u)) {
          var em = ee(t, ea);
          return 'cause' in Error.prototype || !('cause' in t) || M.call(t, 'cause')
            ? 0 === em.length
              ? '[' + String(t) + ']'
              : '{ [' + String(t) + '] ' + E.call(em, ', ') + ' }'
            : '{ [' + String(t) + '] ' + E.call(x.call('[cause]: ' + ea(t.cause), em), ', ') + ' }';
        }
        if ('object' == typeof t && N) {
          if (F && 'function' == typeof t[F] && k) return k(t, { depth: en - o });
          else if ('symbol' !== N && 'function' == typeof t.inspect) return t.inspect();
        }
        if (
          (function (e) {
            if (!a || !e || 'object' != typeof e) return !1;
            try {
              a.call(e);
              try {
                s.call(e);
              } catch (e) {
                return !0;
              }
              return e instanceof Map;
            } catch (e) {}
            return !1;
          })(t)
        ) {
          var ey = [];
          return (
            i &&
              i.call(t, function (e, r) {
                ey.push(ea(r, t, !0) + ' => ' + ea(e, t));
              }),
            Z('Map', a.call(t), ey, eo)
          );
        }
        if (
          (function (e) {
            if (!s || !e || 'object' != typeof e) return !1;
            try {
              s.call(e);
              try {
                a.call(e);
              } catch (e) {
                return !0;
              }
              return e instanceof Set;
            } catch (e) {}
            return !1;
          })(t)
        ) {
          var ev = [];
          return (
            c &&
              c.call(t, function (e) {
                ev.push(ea(e, t));
              }),
            Z('Set', s.call(t), ev, eo)
          );
        }
        if (
          (function (e) {
            if (!d || !e || 'object' != typeof e) return !1;
            try {
              d.call(e, d);
              try {
                p.call(e, p);
              } catch (e) {
                return !0;
              }
              return e instanceof WeakMap;
            } catch (e) {}
            return !1;
          })(t)
        )
          return Y('WeakMap');
        if (
          (function (e) {
            if (!p || !e || 'object' != typeof e) return !1;
            try {
              p.call(e, p);
              try {
                d.call(e, d);
              } catch (e) {
                return !0;
              }
              return e instanceof WeakSet;
            } catch (e) {}
            return !1;
          })(t)
        )
          return Y('WeakSet');
        if (
          (function (e) {
            if (!f || !e || 'object' != typeof e) return !1;
            try {
              return f.call(e), !0;
            } catch (e) {}
            return !1;
          })(t)
        )
          return Y('WeakRef');
        if ('[object Number]' === G((m = t)) && D(m)) return K(ea(Number(t)));
        if (
          (function (e) {
            if (!e || 'object' != typeof e || !R) return !1;
            try {
              return R.call(e), !0;
            } catch (e) {}
            return !1;
          })(t)
        )
          return K(ea(R.call(t)));
        if ('[object Boolean]' === G((P = t)) && D(P)) return K(b.call(t));
        if ('[object String]' === G((q = t)) && D(q)) return K(ea(String(t)));
        if ('undefined' != typeof window && t === window) return '{ [object Window] }';
        if (('undefined' != typeof globalThis && t === globalThis) || (void 0 !== r.g && t === r.g))
          return '{ [object globalThis] }';
        if (!('[object Date]' === G((O = t)) && D(O)) && !$(t)) {
          var eh = ee(t, ea),
            eg = T ? T(t) === Object.prototype : t instanceof Object || t.constructor === Object,
            eP = t instanceof Object ? '' : 'null prototype',
            eC = !eg && A && Object(t) === t && A in t ? h.call(G(t), 8, -1) : eP ? 'Object' : '',
            eq =
              (eg || 'function' != typeof t.constructor
                ? ''
                : t.constructor.name
                  ? t.constructor.name + ' '
                  : '') +
              (eC || eP ? '[' + E.call(x.call([], eC || [], eP || []), ': ') + '] ' : '');
          return 0 === eh.length
            ? eq + '{}'
            : eo
              ? eq + '{' + Q(eh, eo) + '}'
              : eq + '{ ' + E.call(eh, ', ') + ' }';
        }
        return String(t);
      };
      var W =
        Object.prototype.hasOwnProperty ||
        function (e) {
          return e in this;
        };
      function z(e, t) {
        return W.call(e, t);
      }
      function G(e) {
        return m.call(e);
      }
      function J(e, t) {
        if (e.indexOf) return e.indexOf(t);
        for (var r = 0, n = e.length; r < n; r++) if (e[r] === t) return r;
        return -1;
      }
      function X(e) {
        var t = e.charCodeAt(0),
          r = { 8: 'b', 9: 't', 10: 'n', 12: 'f', 13: 'r' }[t];
        return r ? '\\' + r : '\\x' + (t < 16 ? '0' : '') + P.call(t.toString(16));
      }
      function K(e) {
        return 'Object(' + e + ')';
      }
      function Y(e) {
        return e + ' { ? }';
      }
      function Z(e, t, r, n) {
        return e + ' (' + t + ') {' + (n ? Q(r, n) : E.call(r, ', ')) + '}';
      }
      function Q(e, t) {
        if (0 === e.length) return '';
        var r = '\n' + t.prev + t.base;
        return r + E.call(e, ',' + r) + '\n' + t.prev;
      }
      function ee(e, t) {
        var r,
          n = H(e),
          o = [];
        if (n) {
          o.length = e.length;
          for (var a = 0; a < e.length; a++) o[a] = z(e, a) ? t(e[a], e) : '';
        }
        var i = 'function' == typeof j ? j(e) : [];
        if (_) {
          r = {};
          for (var l = 0; l < i.length; l++) r['$' + i[l]] = i[l];
        }
        for (var u in e)
          if (z(e, u) && (!n || String(Number(u)) !== u || !(u < e.length)))
            if (_ && r['$' + u] instanceof Symbol) continue;
            else
              q.call(/[^\w$]/, u)
                ? o.push(t(u, e) + ': ' + t(e[u], e))
                : o.push(u + ': ' + t(e[u], e));
        if ('function' == typeof j)
          for (var s = 0; s < i.length; s++)
            M.call(e, i[s]) && o.push('[' + t(i[s]) + ']: ' + t(e[i[s]], e));
        return o;
      }
    },
    36132: e => {
      'use strict';
      e.exports = function () {
        let { onlyFirst: e = !1 } =
          arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        return RegExp(
          '[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))',
          e ? void 0 : 'g'
        );
      };
    },
    36786: (e, t, r) => {
      'use strict';
      var n = r(7835),
        o = r(61072);
      e.exports = function () {
        var e = o();
        return (
          n(
            Object,
            { assign: e },
            {
              assign: function () {
                return Object.assign !== e;
              },
            }
          ),
          e
        );
      };
    },
    37236: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = (function (e) {
        return e && e.__esModule ? e : { default: e };
      })(r(70863));
      function o(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                i = !0,
                l = !1;
              try {
                for (
                  o = o.call(e);
                  !(i = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  i = !0
                );
              } catch (e) {
                (l = !0), (n = e);
              } finally {
                try {
                  i || null == o.return || o.return();
                } finally {
                  if (l) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          a(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function a(e, t) {
        if (e) {
          if ('string' == typeof e) return i(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return i(e, t);
        }
      }
      function i(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      var l = [
          ['a', { reserved: !1 }],
          ['abbr', { reserved: !1 }],
          ['acronym', { reserved: !1 }],
          ['address', { reserved: !1 }],
          ['applet', { reserved: !1 }],
          ['area', { reserved: !1 }],
          ['article', { reserved: !1 }],
          ['aside', { reserved: !1 }],
          ['audio', { reserved: !1 }],
          ['b', { reserved: !1 }],
          ['base', { reserved: !0 }],
          ['bdi', { reserved: !1 }],
          ['bdo', { reserved: !1 }],
          ['big', { reserved: !1 }],
          ['blink', { reserved: !1 }],
          ['blockquote', { reserved: !1 }],
          ['body', { reserved: !1 }],
          ['br', { reserved: !1 }],
          ['button', { reserved: !1 }],
          ['canvas', { reserved: !1 }],
          ['caption', { reserved: !1 }],
          ['center', { reserved: !1 }],
          ['cite', { reserved: !1 }],
          ['code', { reserved: !1 }],
          ['col', { reserved: !0 }],
          ['colgroup', { reserved: !0 }],
          ['content', { reserved: !1 }],
          ['data', { reserved: !1 }],
          ['datalist', { reserved: !1 }],
          ['dd', { reserved: !1 }],
          ['del', { reserved: !1 }],
          ['details', { reserved: !1 }],
          ['dfn', { reserved: !1 }],
          ['dialog', { reserved: !1 }],
          ['dir', { reserved: !1 }],
          ['div', { reserved: !1 }],
          ['dl', { reserved: !1 }],
          ['dt', { reserved: !1 }],
          ['em', { reserved: !1 }],
          ['embed', { reserved: !1 }],
          ['fieldset', { reserved: !1 }],
          ['figcaption', { reserved: !1 }],
          ['figure', { reserved: !1 }],
          ['font', { reserved: !1 }],
          ['footer', { reserved: !1 }],
          ['form', { reserved: !1 }],
          ['frame', { reserved: !1 }],
          ['frameset', { reserved: !1 }],
          ['h1', { reserved: !1 }],
          ['h2', { reserved: !1 }],
          ['h3', { reserved: !1 }],
          ['h4', { reserved: !1 }],
          ['h5', { reserved: !1 }],
          ['h6', { reserved: !1 }],
          ['head', { reserved: !0 }],
          ['header', { reserved: !1 }],
          ['hgroup', { reserved: !1 }],
          ['hr', { reserved: !1 }],
          ['html', { reserved: !0 }],
          ['i', { reserved: !1 }],
          ['iframe', { reserved: !1 }],
          ['img', { reserved: !1 }],
          ['input', { reserved: !1 }],
          ['ins', { reserved: !1 }],
          ['kbd', { reserved: !1 }],
          ['keygen', { reserved: !1 }],
          ['label', { reserved: !1 }],
          ['legend', { reserved: !1 }],
          ['li', { reserved: !1 }],
          ['link', { reserved: !0 }],
          ['main', { reserved: !1 }],
          ['map', { reserved: !1 }],
          ['mark', { reserved: !1 }],
          ['marquee', { reserved: !1 }],
          ['menu', { reserved: !1 }],
          ['menuitem', { reserved: !1 }],
          ['meta', { reserved: !0 }],
          ['meter', { reserved: !1 }],
          ['nav', { reserved: !1 }],
          ['noembed', { reserved: !0 }],
          ['noscript', { reserved: !0 }],
          ['object', { reserved: !1 }],
          ['ol', { reserved: !1 }],
          ['optgroup', { reserved: !1 }],
          ['option', { reserved: !1 }],
          ['output', { reserved: !1 }],
          ['p', { reserved: !1 }],
          ['param', { reserved: !0 }],
          ['picture', { reserved: !0 }],
          ['pre', { reserved: !1 }],
          ['progress', { reserved: !1 }],
          ['q', { reserved: !1 }],
          ['rp', { reserved: !1 }],
          ['rt', { reserved: !1 }],
          ['rtc', { reserved: !1 }],
          ['ruby', { reserved: !1 }],
          ['s', { reserved: !1 }],
          ['samp', { reserved: !1 }],
          ['script', { reserved: !0 }],
          ['section', { reserved: !1 }],
          ['select', { reserved: !1 }],
          ['small', { reserved: !1 }],
          ['source', { reserved: !0 }],
          ['spacer', { reserved: !1 }],
          ['span', { reserved: !1 }],
          ['strike', { reserved: !1 }],
          ['strong', { reserved: !1 }],
          ['style', { reserved: !0 }],
          ['sub', { reserved: !1 }],
          ['summary', { reserved: !1 }],
          ['sup', { reserved: !1 }],
          ['table', { reserved: !1 }],
          ['tbody', { reserved: !1 }],
          ['td', { reserved: !1 }],
          ['textarea', { reserved: !1 }],
          ['tfoot', { reserved: !1 }],
          ['th', { reserved: !1 }],
          ['thead', { reserved: !1 }],
          ['time', { reserved: !1 }],
          ['title', { reserved: !0 }],
          ['tr', { reserved: !1 }],
          ['track', { reserved: !0 }],
          ['tt', { reserved: !1 }],
          ['u', { reserved: !1 }],
          ['ul', { reserved: !1 }],
          ['var', { reserved: !1 }],
          ['video', { reserved: !1 }],
          ['wbr', { reserved: !1 }],
          ['xmp', { reserved: !1 }],
        ],
        u = {
          entries: function () {
            return l;
          },
          forEach: function (e) {
            var t,
              r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
              n = (function (e, t) {
                var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
                if (!r) {
                  if (Array.isArray(e) || (r = a(e))) {
                    r && (e = r);
                    var n = 0,
                      o = function () {};
                    return {
                      s: o,
                      n: function () {
                        return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                      },
                      e: function (e) {
                        throw e;
                      },
                      f: o,
                    };
                  }
                  throw TypeError(
                    'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                  );
                }
                var i,
                  l = !0,
                  u = !1;
                return {
                  s: function () {
                    r = r.call(e);
                  },
                  n: function () {
                    var e = r.next();
                    return (l = e.done), e;
                  },
                  e: function (e) {
                    (u = !0), (i = e);
                  },
                  f: function () {
                    try {
                      l || null == r.return || r.return();
                    } finally {
                      if (u) throw i;
                    }
                  },
                };
              })(l);
            try {
              for (n.s(); !(t = n.n()).done; ) {
                var i = o(t.value, 2),
                  u = i[0],
                  s = i[1];
                e.call(r, s, u, l);
              }
            } catch (e) {
              n.e(e);
            } finally {
              n.f();
            }
          },
          get: function (e) {
            var t = l.find(function (t) {
              return t[0] === e;
            });
            return t && t[1];
          },
          has: function (e) {
            return !!u.get(e);
          },
          keys: function () {
            return l.map(function (e) {
              return o(e, 1)[0];
            });
          },
          values: function () {
            return l.map(function (e) {
              return o(e, 2)[1];
            });
          },
        };
      t.default = (0, n.default)(u, u.entries());
    },
    37850: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'menuitem' }, module: 'ARIA' }],
          requireContextRole: ['group', 'menu', 'menubar'],
          requiredContextRole: ['group', 'menu', 'menubar'],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [
            ['roletype', 'widget', 'input', 'checkbox', 'menuitemcheckbox'],
            ['roletype', 'widget', 'command', 'menuitem', 'menuitemcheckbox'],
            ['roletype', 'widget', 'input', 'radio'],
          ],
        });
    },
    38165: e => {
      'use strict';
      var t = function (e) {
        return e != e;
      };
      e.exports = function (e, r) {
        return 0 === e && 0 === r ? 1 / e == 1 / r : !!(e === r || (t(e) && t(r)));
      };
    },
    38304: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'cover [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'img']],
        });
    },
    38507: e => {
      'use strict';
      e.exports = Math.pow;
    },
    39233: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-colindex': null,
            'aria-expanded': null,
            'aria-level': null,
            'aria-posinset': null,
            'aria-rowindex': null,
            'aria-selected': null,
            'aria-setsize': null,
          },
          relatedConcepts: [{ concept: { name: 'tr' }, module: 'HTML' }],
          requireContextRole: ['grid', 'rowgroup', 'table', 'treegrid'],
          requiredContextRole: ['grid', 'rowgroup', 'table', 'treegrid'],
          requiredOwnedElements: [['cell'], ['columnheader'], ['gridcell'], ['rowheader']],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'group'],
            ['roletype', 'widget'],
          ],
        });
    },
    39742: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.test = t.serialize = t.default = void 0);
      var n = a(r(36132)),
        o = a(r(43320));
      function a(e) {
        return e && e.__esModule ? e : { default: e };
      }
      let i = e =>
          e.replace((0, n.default)(), e => {
            switch (e) {
              case o.default.red.close:
              case o.default.green.close:
              case o.default.cyan.close:
              case o.default.gray.close:
              case o.default.white.close:
              case o.default.yellow.close:
              case o.default.bgRed.close:
              case o.default.bgGreen.close:
              case o.default.bgYellow.close:
              case o.default.inverse.close:
              case o.default.dim.close:
              case o.default.bold.close:
              case o.default.reset.open:
              case o.default.reset.close:
                return '</>';
              case o.default.red.open:
                return '<red>';
              case o.default.green.open:
                return '<green>';
              case o.default.cyan.open:
                return '<cyan>';
              case o.default.gray.open:
                return '<gray>';
              case o.default.white.open:
                return '<white>';
              case o.default.yellow.open:
                return '<yellow>';
              case o.default.bgRed.open:
                return '<bgRed>';
              case o.default.bgGreen.open:
                return '<bgGreen>';
              case o.default.bgYellow.open:
                return '<bgYellow>';
              case o.default.inverse.open:
                return '<inverse>';
              case o.default.dim.open:
                return '<dim>';
              case o.default.bold.open:
                return '<bold>';
              default:
                return '';
            }
          }),
        l = e => 'string' == typeof e && !!e.match((0, n.default)());
      t.test = l;
      let u = (e, t, r, n, o, a) => a(i(e), t, r, n, o);
      (t.serialize = u), (t.default = { serialize: u, test: l });
    },
    39754: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'chapter [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    40689: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-activedescendant': null,
            'aria-autocomplete': null,
            'aria-errormessage': null,
            'aria-haspopup': null,
            'aria-invalid': null,
            'aria-multiline': null,
            'aria-placeholder': null,
            'aria-readonly': null,
            'aria-required': null,
          },
          relatedConcepts: [
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'type' },
                  { constraints: ['undefined'], name: 'list' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'email' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'tel' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'text' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'url' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            { concept: { name: 'input' }, module: 'XForms' },
            { concept: { name: 'textarea' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    41190: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'pullquote [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['none']],
        });
    },
    42738: (e, t, r) => {
      'use strict';
      var n = r(61781),
        o = r(31630),
        a = r(58507),
        i = r(71005),
        l = r(6784),
        u = r(42815),
        s = r(9369),
        c = r(50510),
        d = r(67602),
        p = r(68783),
        f = r(34531),
        b = r(32777),
        m = r(58672),
        y = r(75166),
        v = r(49774),
        h = r(15230),
        g = r(49237),
        P = r(69566),
        C = o('SharedArrayBuffer.prototype.byteLength', !0),
        q = o('Date.prototype.getTime'),
        x = Object.getPrototypeOf,
        E = o('Object.prototype.toString'),
        w = i('%Set%', !0),
        O = o('Map.prototype.has', !0),
        R = o('Map.prototype.get', !0),
        j = o('Map.prototype.size', !0),
        S = o('Set.prototype.add', !0),
        _ = o('Set.prototype.delete', !0),
        A = o('Set.prototype.has', !0),
        M = o('Set.prototype.size', !0);
      function T(e, t, r, n) {
        for (var o, a = l(e); (o = a.next()) && !o.done; )
          if (N(t, o.value, r, n)) return _(e, o.value), !0;
        return !1;
      }
      function I(e) {
        return void 0 === e
          ? null
          : 'object' != typeof e
            ? 'symbol' != typeof e && (('string' != typeof e && 'number' != typeof e) || +e == +e)
            : void 0;
      }
      function k(e, t, r, n, o, a) {
        for (var i, u, s = l(e); (i = s.next()) && !i.done; )
          if (N(r, (u = i.value), o, a) && N(n, R(t, u), o, a)) return _(e, u), !0;
        return !1;
      }
      function N(e, t, r, o) {
        var i,
          u = r || {};
        if (u.strict ? s(e, t) : e === t) return !0;
        if (v(e) !== v(t)) return !1;
        if (!e || !t || ('object' != typeof e && 'object' != typeof t))
          return u.strict ? s(e, t) : e == t;
        var _ = o.has(e),
          B = o.has(t);
        if (_ && B) {
          if (o.get(e) === o.get(t)) return !0;
        } else i = {};
        return (
          _ || o.set(e, i),
          B || o.set(t, i),
          (function (e, t, r, o) {
            if (
              typeof e != typeof t ||
              null == e ||
              null == t ||
              E(e) !== E(t) ||
              c(e) !== c(t) ||
              d(e) !== d(t)
            )
              return !1;
            var i,
              u,
              s = e instanceof Error,
              v = t instanceof Error;
            if (s !== v || ((s || v) && (e.name !== t.name || e.message !== t.message))) return !1;
            var _ = b(e),
              B = b(t);
            if (_ !== B || ((_ || B) && (e.source !== t.source || a(e) !== a(t)))) return !1;
            var L = f(e),
              U = f(t);
            if (L !== U || ((L || U) && q(e) !== q(t)) || (r.strict && x && x(e) !== x(t)))
              return !1;
            var D = g(e),
              H = g(t);
            if (D !== H) return !1;
            if (D || H) {
              if (e.length !== t.length) return !1;
              for (i = 0; i < e.length; i++) if (e[i] !== t[i]) return !1;
              return !0;
            }
            var $ = F(e),
              V = F(t);
            if ($ !== V) return !1;
            if ($ || V) {
              if (e.length !== t.length) return !1;
              for (i = 0; i < e.length; i++) if (e[i] !== t[i]) return !1;
              return !0;
            }
            var W = p(e),
              z = p(t);
            if (W !== z) return !1;
            if (W || z)
              return (
                P(e) === P(t) &&
                'function' == typeof Uint8Array &&
                N(new Uint8Array(e), new Uint8Array(t), r, o)
              );
            var G = m(e),
              J = m(t);
            if (G !== J) return !1;
            if (G || J)
              return (
                C(e) === C(t) &&
                'function' == typeof Uint8Array &&
                N(new Uint8Array(e), new Uint8Array(t), r, o)
              );
            if (typeof e != typeof t) return !1;
            var X = y(e),
              K = y(t);
            if (X.length !== K.length) return !1;
            for (X.sort(), K.sort(), i = X.length - 1; i >= 0; i--) if (X[i] != K[i]) return !1;
            for (i = X.length - 1; i >= 0; i--) if (!N(e[(u = X[i])], t[u], r, o)) return !1;
            var Y = h(e),
              Z = h(t);
            return (
              Y === Z &&
              ('Set' === Y || 'Set' === Z
                ? (function (e, t, r, n) {
                    if (M(e) !== M(t)) return !1;
                    for (var o, a, i, u = l(e), s = l(t); (o = u.next()) && !o.done; )
                      if (o.value && 'object' == typeof o.value) i || (i = new w()), S(i, o.value);
                      else if (!A(t, o.value)) {
                        if (
                          r.strict ||
                          !(function (e, t, r) {
                            var n = I(r);
                            return null != n ? n : A(t, n) && !A(e, n);
                          })(e, t, o.value)
                        )
                          return !1;
                        i || (i = new w()), S(i, o.value);
                      }
                    if (i) {
                      for (; (a = s.next()) && !a.done; )
                        if (a.value && 'object' == typeof a.value) {
                          if (!T(i, a.value, r.strict, n)) return !1;
                        } else if (!r.strict && !A(e, a.value) && !T(i, a.value, r.strict, n))
                          return !1;
                      return 0 === M(i);
                    }
                    return !0;
                  })(e, t, r, o)
                : 'Map' !== Y ||
                  (function (e, t, r, o) {
                    if (j(e) !== j(t)) return !1;
                    for (var a, i, u, s, c, d, p = l(e), f = l(t); (a = p.next()) && !a.done; )
                      if (((s = a.value[0]), (c = a.value[1]), s && 'object' == typeof s))
                        u || (u = new w()), S(u, s);
                      else if ((void 0 === (d = R(t, s)) && !O(t, s)) || !N(c, d, r, o)) {
                        if (
                          r.strict ||
                          !(function (e, t, r, o, a, i) {
                            var l = I(r);
                            if (null != l) return l;
                            var u = R(t, l),
                              s = n({}, a, { strict: !1 });
                            return (
                              (void 0 !== u || !!O(t, l)) &&
                              !!N(o, u, s, i) &&
                              !O(e, l) &&
                              N(o, u, s, i)
                            );
                          })(e, t, s, c, r, o)
                        )
                          return !1;
                        u || (u = new w()), S(u, s);
                      }
                    if (u) {
                      for (; (i = f.next()) && !i.done; )
                        if (((s = i.value[0]), (d = i.value[1]), s && 'object' == typeof s)) {
                          if (!k(u, e, s, d, r, o)) return !1;
                        } else if (
                          !r.strict &&
                          (!e.has(s) || !N(R(e, s), d, r, o)) &&
                          !k(u, e, s, d, n({}, r, { strict: !1 }), o)
                        )
                          return !1;
                      return 0 === M(u);
                    }
                    return !0;
                  })(e, t, r, o))
            );
          })(e, t, u, o)
        );
      }
      function F(e) {
        return (
          !!e &&
          'object' == typeof e &&
          'number' == typeof e.length &&
          'function' == typeof e.copy &&
          'function' == typeof e.slice &&
          (!(e.length > 0) || 'number' == typeof e[0]) &&
          !!(e.constructor && e.constructor.isBuffer && e.constructor.isBuffer(e))
        );
      }
      e.exports = function (e, t, r) {
        return N(e, t, r, u());
      };
    },
    42813: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-atomic': 'true', 'aria-live': 'polite' },
          relatedConcepts: [{ concept: { name: 'output' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    42815: (e, t, r) => {
      'use strict';
      var n = r(64215),
        o = r(36074),
        a = r(19398),
        i = r(12751),
        l = r(7806) || i || a;
      e.exports = function () {
        var e,
          t = {
            assert: function (e) {
              if (!t.has(e)) throw new n('Side channel does not contain ' + o(e));
            },
            delete: function (t) {
              return !!e && e.delete(t);
            },
            get: function (t) {
              return e && e.get(t);
            },
            has: function (t) {
              return !!e && e.has(t);
            },
            set: function (t, r) {
              e || (e = l()), e.set(t, r);
            },
          };
        return t;
      };
    },
    43026: (e, t, r) => {
      'use strict';
      var n = r(47600),
        o = n('String.prototype.valueOf'),
        a = function (e) {
          try {
            return o(e), !0;
          } catch (e) {
            return !1;
          }
        },
        i = n('Object.prototype.toString'),
        l = r(79056)();
      e.exports = function (e) {
        return (
          'string' == typeof e ||
          (!!e && 'object' == typeof e && (l ? a(e) : '[object String]' === i(e)))
        );
      };
    },
    43320: (e, t, r) => {
      'use strict';
      e = r.nmd(e);
      let n =
          (e = 0) =>
          t =>
            `\u001B[${38 + e};5;${t}m`,
        o =
          (e = 0) =>
          (t, r, n) =>
            `\u001B[${38 + e};2;${t};${r};${n}m`;
      Object.defineProperty(e, 'exports', {
        enumerable: !0,
        get: function () {
          let e = new Map(),
            t = {
              modifier: {
                reset: [0, 0],
                bold: [1, 22],
                dim: [2, 22],
                italic: [3, 23],
                underline: [4, 24],
                overline: [53, 55],
                inverse: [7, 27],
                hidden: [8, 28],
                strikethrough: [9, 29],
              },
              color: {
                black: [30, 39],
                red: [31, 39],
                green: [32, 39],
                yellow: [33, 39],
                blue: [34, 39],
                magenta: [35, 39],
                cyan: [36, 39],
                white: [37, 39],
                blackBright: [90, 39],
                redBright: [91, 39],
                greenBright: [92, 39],
                yellowBright: [93, 39],
                blueBright: [94, 39],
                magentaBright: [95, 39],
                cyanBright: [96, 39],
                whiteBright: [97, 39],
              },
              bgColor: {
                bgBlack: [40, 49],
                bgRed: [41, 49],
                bgGreen: [42, 49],
                bgYellow: [43, 49],
                bgBlue: [44, 49],
                bgMagenta: [45, 49],
                bgCyan: [46, 49],
                bgWhite: [47, 49],
                bgBlackBright: [100, 49],
                bgRedBright: [101, 49],
                bgGreenBright: [102, 49],
                bgYellowBright: [103, 49],
                bgBlueBright: [104, 49],
                bgMagentaBright: [105, 49],
                bgCyanBright: [106, 49],
                bgWhiteBright: [107, 49],
              },
            };
          for (let [r, n] of ((t.color.gray = t.color.blackBright),
          (t.bgColor.bgGray = t.bgColor.bgBlackBright),
          (t.color.grey = t.color.blackBright),
          (t.bgColor.bgGrey = t.bgColor.bgBlackBright),
          Object.entries(t))) {
            for (let [r, o] of Object.entries(n))
              (t[r] = { open: `\u001B[${o[0]}m`, close: `\u001B[${o[1]}m` }),
                (n[r] = t[r]),
                e.set(o[0], o[1]);
            Object.defineProperty(t, r, { value: n, enumerable: !1 });
          }
          return (
            Object.defineProperty(t, 'codes', { value: e, enumerable: !1 }),
            (t.color.close = '\x1b[39m'),
            (t.bgColor.close = '\x1b[49m'),
            (t.color.ansi256 = n()),
            (t.color.ansi16m = o()),
            (t.bgColor.ansi256 = n(10)),
            (t.bgColor.ansi16m = o(10)),
            Object.defineProperties(t, {
              rgbToAnsi256: {
                value: (e, t, r) =>
                  e === t && t === r
                    ? e < 8
                      ? 16
                      : e > 248
                        ? 231
                        : Math.round(((e - 8) / 247) * 24) + 232
                    : 16 +
                      36 * Math.round((e / 255) * 5) +
                      6 * Math.round((t / 255) * 5) +
                      Math.round((r / 255) * 5),
                enumerable: !1,
              },
              hexToRgb: {
                value: e => {
                  let t = /(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));
                  if (!t) return [0, 0, 0];
                  let { colorString: r } = t.groups;
                  3 === r.length &&
                    (r = r
                      .split('')
                      .map(e => e + e)
                      .join(''));
                  let n = Number.parseInt(r, 16);
                  return [(n >> 16) & 255, (n >> 8) & 255, 255 & n];
                },
                enumerable: !1,
              },
              hexToAnsi256: { value: e => t.rgbToAnsi256(...t.hexToRgb(e)), enumerable: !1 },
            }),
            t
          );
        },
      });
    },
    43341: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    44103: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = (function (e) {
        return e && e.__esModule ? e : { default: e };
      })(r(70863));
      function o(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                i = !0,
                l = !1;
              try {
                for (
                  o = o.call(e);
                  !(i = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  i = !0
                );
              } catch (e) {
                (l = !0), (n = e);
              } finally {
                try {
                  i || null == o.return || o.return();
                } finally {
                  if (l) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          a(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function a(e, t) {
        if (e) {
          if ('string' == typeof e) return i(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return i(e, t);
        }
      }
      function i(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      var l = [
          ['aria-activedescendant', { type: 'id' }],
          ['aria-atomic', { type: 'boolean' }],
          ['aria-autocomplete', { type: 'token', values: ['inline', 'list', 'both', 'none'] }],
          ['aria-busy', { type: 'boolean' }],
          ['aria-checked', { type: 'tristate' }],
          ['aria-colcount', { type: 'integer' }],
          ['aria-colindex', { type: 'integer' }],
          ['aria-colspan', { type: 'integer' }],
          ['aria-controls', { type: 'idlist' }],
          [
            'aria-current',
            { type: 'token', values: ['page', 'step', 'location', 'date', 'time', !0, !1] },
          ],
          ['aria-describedby', { type: 'idlist' }],
          ['aria-details', { type: 'id' }],
          ['aria-disabled', { type: 'boolean' }],
          [
            'aria-dropeffect',
            { type: 'tokenlist', values: ['copy', 'execute', 'link', 'move', 'none', 'popup'] },
          ],
          ['aria-errormessage', { type: 'id' }],
          ['aria-expanded', { type: 'boolean', allowundefined: !0 }],
          ['aria-flowto', { type: 'idlist' }],
          ['aria-grabbed', { type: 'boolean', allowundefined: !0 }],
          [
            'aria-haspopup',
            { type: 'token', values: [!1, !0, 'menu', 'listbox', 'tree', 'grid', 'dialog'] },
          ],
          ['aria-hidden', { type: 'boolean', allowundefined: !0 }],
          ['aria-invalid', { type: 'token', values: ['grammar', !1, 'spelling', !0] }],
          ['aria-keyshortcuts', { type: 'string' }],
          ['aria-label', { type: 'string' }],
          ['aria-labelledby', { type: 'idlist' }],
          ['aria-level', { type: 'integer' }],
          ['aria-live', { type: 'token', values: ['assertive', 'off', 'polite'] }],
          ['aria-modal', { type: 'boolean' }],
          ['aria-multiline', { type: 'boolean' }],
          ['aria-multiselectable', { type: 'boolean' }],
          ['aria-orientation', { type: 'token', values: ['vertical', 'undefined', 'horizontal'] }],
          ['aria-owns', { type: 'idlist' }],
          ['aria-placeholder', { type: 'string' }],
          ['aria-posinset', { type: 'integer' }],
          ['aria-pressed', { type: 'tristate' }],
          ['aria-readonly', { type: 'boolean' }],
          [
            'aria-relevant',
            { type: 'tokenlist', values: ['additions', 'all', 'removals', 'text'] },
          ],
          ['aria-required', { type: 'boolean' }],
          ['aria-roledescription', { type: 'string' }],
          ['aria-rowcount', { type: 'integer' }],
          ['aria-rowindex', { type: 'integer' }],
          ['aria-rowspan', { type: 'integer' }],
          ['aria-selected', { type: 'boolean', allowundefined: !0 }],
          ['aria-setsize', { type: 'integer' }],
          ['aria-sort', { type: 'token', values: ['ascending', 'descending', 'none', 'other'] }],
          ['aria-valuemax', { type: 'number' }],
          ['aria-valuemin', { type: 'number' }],
          ['aria-valuenow', { type: 'number' }],
          ['aria-valuetext', { type: 'string' }],
        ],
        u = {
          entries: function () {
            return l;
          },
          forEach: function (e) {
            var t,
              r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
              n = (function (e, t) {
                var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
                if (!r) {
                  if (Array.isArray(e) || (r = a(e))) {
                    r && (e = r);
                    var n = 0,
                      o = function () {};
                    return {
                      s: o,
                      n: function () {
                        return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                      },
                      e: function (e) {
                        throw e;
                      },
                      f: o,
                    };
                  }
                  throw TypeError(
                    'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                  );
                }
                var i,
                  l = !0,
                  u = !1;
                return {
                  s: function () {
                    r = r.call(e);
                  },
                  n: function () {
                    var e = r.next();
                    return (l = e.done), e;
                  },
                  e: function (e) {
                    (u = !0), (i = e);
                  },
                  f: function () {
                    try {
                      l || null == r.return || r.return();
                    } finally {
                      if (u) throw i;
                    }
                  },
                };
              })(l);
            try {
              for (n.s(); !(t = n.n()).done; ) {
                var i = o(t.value, 2),
                  u = i[0],
                  s = i[1];
                e.call(r, s, u, l);
              }
            } catch (e) {
              n.e(e);
            } finally {
              n.f();
            }
          },
          get: function (e) {
            var t = l.find(function (t) {
              return t[0] === e;
            });
            return t && t[1];
          },
          has: function (e) {
            return !!u.get(e);
          },
          keys: function () {
            return l.map(function (e) {
              return o(e, 1)[0];
            });
          },
          values: function () {
            return l.map(function (e) {
              return o(e, 2)[1];
            });
          },
        };
      t.default = (0, n.default)(u, u.entries());
    },
    44150: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    44168: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'appendix [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    44169: e => {
      'use strict';
      var t,
        r,
        n = Function.prototype.toString,
        o = 'object' == typeof Reflect && null !== Reflect && Reflect.apply;
      if ('function' == typeof o && 'function' == typeof Object.defineProperty)
        try {
          (t = Object.defineProperty({}, 'length', {
            get: function () {
              throw r;
            },
          })),
            (r = {}),
            o(
              function () {
                throw 42;
              },
              null,
              t
            );
        } catch (e) {
          e !== r && (o = null);
        }
      else o = null;
      var a = /^\s*class\b/,
        i = function (e) {
          try {
            var t = n.call(e);
            return a.test(t);
          } catch (e) {
            return !1;
          }
        },
        l = function (e) {
          try {
            if (i(e)) return !1;
            return n.call(e), !0;
          } catch (e) {
            return !1;
          }
        },
        u = Object.prototype.toString,
        s = 'function' == typeof Symbol && !!Symbol.toStringTag,
        c = !(0 in [,]),
        d = function () {
          return !1;
        };
      if ('object' == typeof document) {
        var p = document.all;
        u.call(p) === u.call(document.all) &&
          (d = function (e) {
            if ((c || !e) && (void 0 === e || 'object' == typeof e))
              try {
                var t = u.call(e);
                return (
                  ('[object HTMLAllCollection]' === t ||
                    '[object HTML document.all class]' === t ||
                    '[object HTMLCollection]' === t ||
                    '[object Object]' === t) &&
                  null == e('')
                );
              } catch (e) {}
            return !1;
          });
      }
      e.exports = o
        ? function (e) {
            if (d(e)) return !0;
            if (!e || ('function' != typeof e && 'object' != typeof e)) return !1;
            try {
              o(e, null, t);
            } catch (e) {
              if (e !== r) return !1;
            }
            return !i(e) && l(e);
          }
        : function (e) {
            if (d(e)) return !0;
            if (!e || ('function' != typeof e && 'object' != typeof e)) return !1;
            if (s) return l(e);
            if (i(e)) return !1;
            var t = u.call(e);
            return (
              ('[object Function]' === t ||
                '[object GeneratorFunction]' === t ||
                !!/^\[object HTML/.test(t)) &&
              l(e)
            );
          };
    },
    44902: (e, t, r) => {
      'use strict';
      var n = r(59251),
        o = r(7835).supportsDescriptors,
        a = Object.getOwnPropertyDescriptor;
      e.exports = function () {
        if (o && 'gim' === /a/gim.flags) {
          var e = a(RegExp.prototype, 'flags');
          if (
            e &&
            'function' == typeof e.get &&
            'dotAll' in RegExp.prototype &&
            'hasIndices' in RegExp.prototype
          ) {
            var t = '',
              r = {};
            if (
              (Object.defineProperty(r, 'hasIndices', {
                get: function () {
                  t += 'd';
                },
              }),
              Object.defineProperty(r, 'sticky', {
                get: function () {
                  t += 'y';
                },
              }),
              e.get.call(r),
              'dy' === t)
            )
              return e.get;
          }
        }
        return n;
      };
    },
    45125: e => {
      'use strict';
      e.exports = Error;
    },
    45703: (e, t, r) => {
      'use strict';
      var n,
        o = r(6390),
        a = r(9278);
      try {
        n = [].__proto__ === Array.prototype;
      } catch (e) {
        if (!e || 'object' != typeof e || !('code' in e) || 'ERR_PROTO_ACCESS' !== e.code) throw e;
      }
      var i = !!n && a && a(Object.prototype, '__proto__'),
        l = Object,
        u = l.getPrototypeOf;
      e.exports =
        i && 'function' == typeof i.get
          ? o([i.get])
          : 'function' == typeof u &&
            function (e) {
              return u(null == e ? e : l(e));
            };
    },
    45760: e => {
      'use strict';
      e.exports = Object;
    },
    46139: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-valuetext': null,
            'aria-valuenow': '0',
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'number' }], name: 'input' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite'],
            ['roletype', 'widget', 'input'],
            ['roletype', 'structure', 'range'],
          ],
        });
    },
    46236: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [
            { concept: { name: 'span' }, module: 'HTML' },
            { concept: { name: 'div' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    46586: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {
            'aria-atomic': null,
            'aria-busy': null,
            'aria-controls': null,
            'aria-current': null,
            'aria-describedby': null,
            'aria-details': null,
            'aria-dropeffect': null,
            'aria-flowto': null,
            'aria-grabbed': null,
            'aria-hidden': null,
            'aria-keyshortcuts': null,
            'aria-label': null,
            'aria-labelledby': null,
            'aria-live': null,
            'aria-owns': null,
            'aria-relevant': null,
            'aria-roledescription': null,
          },
          relatedConcepts: [
            { concept: { name: 'rel' }, module: 'HTML' },
            { concept: { name: 'role' }, module: 'XHTML' },
            { concept: { name: 'type' }, module: 'Dublin Core' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [],
        });
    },
    46798: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-level': null,
            'aria-multiselectable': null,
            'aria-orientation': 'horizontal',
          },
          relatedConcepts: [{ module: 'DAISY', concept: { name: 'guide' } }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['tab']],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'composite']],
        });
    },
    46805: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-valuetext': null },
          relatedConcepts: [
            { concept: { name: 'progress' }, module: 'HTML' },
            { concept: { name: 'status' }, module: 'ARIA' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'range'],
            ['roletype', 'widget'],
          ],
        });
    },
    47147: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    47600: (e, t, r) => {
      'use strict';
      var n = r(71005),
        o = r(6390),
        a = o([n('%String.prototype.indexOf%')]);
      e.exports = function (e, t) {
        var r = n(e, !!t);
        return 'function' == typeof r && a(e, '.prototype.') > -1 ? o([r]) : r;
      };
    },
    47799: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'menu' }, module: 'HTML' },
            { concept: { name: 'ol' }, module: 'HTML' },
            { concept: { name: 'ul' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['listitem']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    48017: (e, t, r) => {
      'use strict';
      var n = r(75166),
        o = r(67541)(),
        a = r(47600),
        i = r(45760),
        l = a('Array.prototype.push'),
        u = a('Object.prototype.propertyIsEnumerable'),
        s = o ? i.getOwnPropertySymbols : null;
      e.exports = function (e, t) {
        if (null == e) throw TypeError('target must be an object');
        var r = i(e);
        if (1 == arguments.length) return r;
        for (var a = 1; a < arguments.length; ++a) {
          var c = i(arguments[a]),
            d = n(c),
            p = o && (i.getOwnPropertySymbols || s);
          if (p)
            for (var f = p(c), b = 0; b < f.length; ++b) {
              var m = f[b];
              u(c, m) && l(d, m);
            }
          for (var y = 0; y < d.length; ++y) {
            var v = d[y];
            if (u(c, v)) {
              var h = c[v];
              r[v] = h;
            }
          }
        }
        return r;
      };
    },
    48191: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'colophon [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    48731: (e, t, r) => {
      'use strict';
      var n = r(11351),
        o = r(42815)(),
        a = r(64215),
        i = {
          assert: function (e, t) {
            if (!e || ('object' != typeof e && 'function' != typeof e))
              throw new a('`O` is not an object');
            if ('string' != typeof t) throw new a('`slot` must be a string');
            if ((o.assert(e), !i.has(e, t))) throw new a('`' + t + '` is not present on `O`');
          },
          get: function (e, t) {
            if (!e || ('object' != typeof e && 'function' != typeof e))
              throw new a('`O` is not an object');
            if ('string' != typeof t) throw new a('`slot` must be a string');
            var r = o.get(e);
            return r && r['$' + t];
          },
          has: function (e, t) {
            if (!e || ('object' != typeof e && 'function' != typeof e))
              throw new a('`O` is not an object');
            if ('string' != typeof t) throw new a('`slot` must be a string');
            var r = o.get(e);
            return !!r && n(r, '$' + t);
          },
          set: function (e, t, r) {
            if (!e || ('object' != typeof e && 'function' != typeof e))
              throw new a('`O` is not an object');
            if ('string' != typeof t) throw new a('`slot` must be a string');
            var n = o.get(e);
            n || ((n = {}), o.set(e, n)), (n['$' + t] = r);
          },
        };
      Object.freeze && Object.freeze(i), (e.exports = i);
    },
    48932: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = i(r(82099)),
        o = i(r(60657)),
        a = i(r(12594));
      function i(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['graphics-document', n.default],
        ['graphics-object', o.default],
        ['graphics-symbol', a.default],
      ];
    },
    48968: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = function (e) {
          return e.replace(/</g, '&lt;').replace(/>/g, '&gt;');
        });
    },
    49237: (e, t, r) => {
      'use strict';
      var n = r(70617),
        o = r(88089),
        a = r(34802),
        i = r(47600),
        l = r(9278),
        u = r(28265),
        s = i('Object.prototype.toString'),
        c = r(79056)(),
        d = 'undefined' == typeof globalThis ? r.g : globalThis,
        p = o(),
        f = i('String.prototype.slice'),
        b =
          i('Array.prototype.indexOf', !0) ||
          function (e, t) {
            for (var r = 0; r < e.length; r += 1) if (e[r] === t) return r;
            return -1;
          },
        m = { __proto__: null };
      c && l && u
        ? n(p, function (e) {
            var t = new d[e]();
            if (Symbol.toStringTag in t && u) {
              var r = u(t),
                n = l(r, Symbol.toStringTag);
              !n && r && (n = l(u(r), Symbol.toStringTag)), (m['$' + e] = a(n.get));
            }
          })
        : n(p, function (e) {
            var t = new d[e](),
              r = t.slice || t.set;
            r && (m['$' + e] = a(r));
          });
      var y = function (e) {
          var t = !1;
          return (
            n(m, function (r, n) {
              if (!t)
                try {
                  '$' + r(e) === n && (t = f(n, 1));
                } catch (e) {}
            }),
            t
          );
        },
        v = function (e) {
          var t = !1;
          return (
            n(m, function (r, n) {
              if (!t)
                try {
                  r(e), (t = f(n, 1));
                } catch (e) {}
            }),
            t
          );
        };
      e.exports = function (e) {
        if (!e || 'object' != typeof e) return !1;
        if (!c) {
          var t = f(s(e), 8, -1);
          return b(p, t) > -1 ? t : 'Object' === t && v(e);
        }
        return l ? y(e) : null;
      };
    },
    49691: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype']],
        });
    },
    49774: (e, t, r) => {
      'use strict';
      var n = r(43026),
        o = r(88e3),
        a = r(24428),
        i = r(16200),
        l = r(31179);
      e.exports = function (e) {
        return null == e || ('object' != typeof e && 'function' != typeof e)
          ? null
          : n(e)
            ? 'String'
            : o(e)
              ? 'Number'
              : a(e)
                ? 'Boolean'
                : i(e)
                  ? 'Symbol'
                  : l(e)
                    ? 'BigInt'
                    : void 0;
      };
    },
    50510: (e, t, r) => {
      'use strict';
      var n = r(79056)(),
        o = r(47600)('Object.prototype.toString'),
        a = function (e) {
          return (
            (!n || !e || 'object' != typeof e || !(Symbol.toStringTag in e)) &&
            '[object Arguments]' === o(e)
          );
        },
        i = function (e) {
          return (
            !!a(e) ||
            (null !== e &&
              'object' == typeof e &&
              'length' in e &&
              'number' == typeof e.length &&
              e.length >= 0 &&
              '[object Array]' !== o(e) &&
              'callee' in e &&
              '[object Function]' === o(e.callee))
          );
        },
        l = (function () {
          return a(arguments);
        })();
      (a.isLegacyArguments = i), (e.exports = l ? a : i);
    },
    51569: e => {
      'use strict';
      e.exports = EvalError;
    },
    51650: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite'],
            ['roletype', 'structure', 'section', 'group'],
          ],
        });
    },
    52594: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': 'horizontal' },
          relatedConcepts: [{ concept: { name: 'menubar' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'group']],
        });
    },
    52691: (e, t, r) => {
      'use strict';
      var n = r(47600),
        o = r(32777),
        a = n('RegExp.prototype.exec'),
        i = r(64215);
      e.exports = function (e) {
        if (!o(e)) throw new i('`regex` must be a RegExp');
        return function (t) {
          return null !== a(e, t);
        };
      };
    },
    54305: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'epigraph [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    54767: e => {
      'use strict';
      var t = function () {
          return 'string' == typeof function () {}.name;
        },
        r = Object.getOwnPropertyDescriptor;
      if (r)
        try {
          r([], 'length');
        } catch (e) {
          r = null;
        }
      t.functionsHaveConfigurableNames = function () {
        if (!t() || !r) return !1;
        var e = r(function () {}, 'name');
        return !!e && !!e.configurable;
      };
      var n = Function.prototype.bind;
      (t.boundFunctionsHaveNames = function () {
        return t() && 'function' == typeof n && '' !== function () {}.bind().name;
      }),
        (e.exports = t);
    },
    54815: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-posinset': null, 'aria-setsize': null },
          relatedConcepts: [{ concept: { name: 'article' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'document']],
        });
    },
    56711: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = function () {
          var e = this,
            t = 0,
            r = {
              '@@iterator': function () {
                return r;
              },
              next: function () {
                if (!(t < e.length)) return { done: !0 };
                var r = e[t];
                return (t += 1), { done: !1, value: r };
              },
            };
          return r;
        });
    },
    56733: (e, t, r) => {
      'use strict';
      r.d(t, { XX: () => w, nj: () => u.nj, rC: () => m });
      var n,
        o = r(26611),
        a = r(21462),
        i = r(47993),
        l = r(54988),
        u = r(61869),
        s = r(77051);
      let c = o.act;
      function d() {
        if ('undefined' != typeof globalThis) return globalThis;
        if ('undefined' != typeof self) return self;
        if ('undefined' != typeof window) return window;
        if (void 0 !== r.g) return r.g;
        throw Error('unable to locate global object');
      }
      function p(e) {
        d().IS_REACT_ACT_ENVIRONMENT = e;
      }
      function f() {
        return d().IS_REACT_ACT_ENVIRONMENT;
      }
      let b = e => {
          let t = f();
          p(!0);
          try {
            let r = !1,
              n = c(() => {
                let t = e();
                return (
                  null !== t && 'object' == typeof t && 'function' == typeof t.then && (r = !0), t
                );
              });
            if (r)
              return {
                then: (e, r) => {
                  n.then(
                    r => {
                      p(t), e(r);
                    },
                    e => {
                      p(t), r(e);
                    }
                  );
                },
              };
            return p(t), n;
          } catch (e) {
            throw (p(t), e);
          }
        },
        m = function () {
          return (0, u.rC)(...arguments);
        };
      Object.keys(u.rC).forEach(e => {
        m[e] = function () {
          return u.rC[e](...arguments);
        };
      });
      let y = m.mouseEnter,
        v = m.mouseLeave;
      (m.mouseEnter = function () {
        return y(...arguments), m.mouseOver(...arguments);
      }),
        (m.mouseLeave = function () {
          return v(...arguments), m.mouseOut(...arguments);
        });
      let h = m.pointerEnter,
        g = m.pointerLeave;
      (m.pointerEnter = function () {
        return h(...arguments), m.pointerOver(...arguments);
      }),
        (m.pointerLeave = function () {
          return g(...arguments), m.pointerOut(...arguments);
        });
      let P = m.select;
      m.select = (e, t) => {
        P(e, t), e.focus(), m.keyUp(e, t);
      };
      let C = m.blur,
        q = m.focus;
      (m.blur = function () {
        return m.focusOut(...arguments), C(...arguments);
      }),
        (m.focus = function () {
          return m.focusIn(...arguments), q(...arguments);
        }),
        (0, u.jK)({
          unstable_advanceTimersWrapper: e => b(e),
          asyncWrapper: async e => {
            let t = f();
            p(!1);
            try {
              return await e();
            } finally {
              p(t);
            }
          },
          eventWrapper: e => {
            let t;
            return (
              b(() => {
                t = e();
              }),
              t
            );
          },
        });
      let x = new Set(),
        E = [];
      function w(e, t) {
        let r,
          {
            container: n,
            baseElement: o = n,
            legacyRoot: s = !1,
            queries: c,
            hydrate: d = !1,
            wrapper: p,
          } = void 0 === t ? {} : t;
        return (
          o || (o = document.body),
          n || (n = o.appendChild(document.createElement('div'))),
          x.has(n)
            ? E.forEach(e => {
                e.container === n && (r = e.root);
              })
            : ((r = (
                s
                  ? function (e) {
                      return {
                        hydrate(t) {
                          i.hydrate(t, e);
                        },
                        render(t) {
                          i.render(t, e);
                        },
                        unmount() {
                          i.unmountComponentAtNode(e);
                        },
                      };
                    }
                  : function (e, t) {
                      let r,
                        { hydrate: n, ui: o, wrapper: i } = t;
                      return (
                        n
                          ? b(() => {
                              r = l.hydrateRoot(e, i ? a.createElement(i, null, o) : o);
                            })
                          : (r = l.createRoot(e)),
                        {
                          hydrate() {
                            if (!n)
                              throw Error(
                                'Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.'
                              );
                          },
                          render(e) {
                            r.render(e);
                          },
                          unmount() {
                            r.unmount();
                          },
                        }
                      );
                    }
              )(n, { hydrate: d, ui: e, wrapper: p })),
              E.push({ container: n, root: r }),
              x.add(n)),
          (function e(t, r) {
            let { baseElement: n, container: o, hydrate: i, queries: l, root: s, wrapper: c } = r,
              d = e => (c ? a.createElement(c, null, e) : e);
            return (
              b(() => {
                i ? s.hydrate(d(t), o) : s.render(d(t), o);
              }),
              {
                container: o,
                baseElement: n,
                debug: function (e, t, r) {
                  return (
                    void 0 === e && (e = n),
                    Array.isArray(e)
                      ? e.forEach(e => console.log((0, u.fE)(e, t, r)))
                      : console.log((0, u.fE)(e, t, r))
                  );
                },
                unmount: () => {
                  b(() => {
                    s.unmount();
                  });
                },
                rerender: t => {
                  e(d(t), { container: o, baseElement: n, root: s });
                },
                asFragment: () => {
                  if ('function' == typeof document.createRange)
                    return document.createRange().createContextualFragment(o.innerHTML);
                  {
                    let e = document.createElement('template');
                    return (e.innerHTML = o.innerHTML), e.content;
                  }
                },
                ...(0, u.E5)(n, l),
              }
            );
          })(e, { container: n, baseElement: o, queries: c, hydrate: d, wrapper: p, root: r })
        );
      }
      function O() {
        E.forEach(e => {
          let { root: t, container: r } = e;
          b(() => {
            t.unmount();
          }),
            r.parentNode === document.body && document.body.removeChild(r);
        }),
          (E.length = 0),
          x.clear();
      }
      if (
        (void 0 === s || !(null != (n = s.env) && n.RTL_SKIP_AUTO_CLEANUP)) &&
        ('function' == typeof afterEach
          ? afterEach(() => {
              O();
            })
          : 'function' == typeof teardown &&
            teardown(() => {
              O();
            }),
        'function' == typeof beforeAll && 'function' == typeof afterAll)
      ) {
        let e = f();
        beforeAll(() => {
          (e = f()), p(!0);
        }),
          afterAll(() => {
            p(e);
          });
      }
    },
    56843: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'alert' }, module: 'XForms' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'alert'],
            ['roletype', 'window', 'dialog'],
          ],
        });
    },
    56934: (e, t, r) => {
      'use strict';
      t.Ot = t.wZ = t._s = void 0;
      var n = u(r(44103)),
        o = u(r(37236)),
        a = u(r(12407)),
        i = u(r(31192)),
        l = u(r(9496));
      function u(e) {
        return e && e.__esModule ? e : { default: e };
      }
      n.default, o.default, (t.Ot = a.default), (t._s = i.default), (t.wZ = l.default);
    },
    57632: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'credit [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    57644: e => {
      'use strict';
      e.exports = Object.getOwnPropertyDescriptor;
    },
    57767: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    58460: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.printIteratorEntries = function (e, t, r, n, o, a, i = ': ') {
          let l = '',
            u = e.next();
          if (!u.done) {
            l += t.spacingOuter;
            let s = r + t.indent;
            for (; !u.done; ) {
              let r = a(u.value[0], t, s, n, o),
                c = a(u.value[1], t, s, n, o);
              (l += s + r + i + c),
                (u = e.next()).done ? t.min || (l += ',') : (l += ',' + t.spacingInner);
            }
            l += t.spacingOuter + r;
          }
          return l;
        }),
        (t.printIteratorValues = function (e, t, r, n, o, a) {
          let i = '',
            l = e.next();
          if (!l.done) {
            i += t.spacingOuter;
            let u = r + t.indent;
            for (; !l.done; )
              (i += u + a(l.value, t, u, n, o)),
                (l = e.next()).done ? t.min || (i += ',') : (i += ',' + t.spacingInner);
            i += t.spacingOuter + r;
          }
          return i;
        }),
        (t.printListItems = function (e, t, r, n, o, a) {
          let i = '';
          if (e.length) {
            i += t.spacingOuter;
            let l = r + t.indent;
            for (let r = 0; r < e.length; r++)
              (i += l),
                r in e && (i += a(e[r], t, l, n, o)),
                r < e.length - 1 ? (i += ',' + t.spacingInner) : t.min || (i += ',');
            i += t.spacingOuter + r;
          }
          return i;
        }),
        (t.printObjectProperties = function (e, t, n, o, a, i) {
          let l = '',
            u = r(e, t.compareKeys);
          if (u.length) {
            l += t.spacingOuter;
            let r = n + t.indent;
            for (let n = 0; n < u.length; n++) {
              let s = u[n],
                c = i(s, t, r, o, a),
                d = i(e[s], t, r, o, a);
              (l += r + c + ': ' + d),
                n < u.length - 1 ? (l += ',' + t.spacingInner) : t.min || (l += ',');
            }
            l += t.spacingOuter + n;
          }
          return l;
        });
      let r = (e, t) => {
        let r = Object.keys(e).sort(t);
        return (
          Object.getOwnPropertySymbols &&
            Object.getOwnPropertySymbols(e).forEach(t => {
              Object.getOwnPropertyDescriptor(e, t).enumerable && r.push(t);
            }),
          r
        );
      };
    },
    58507: (e, t, r) => {
      'use strict';
      var n = r(7835),
        o = r(34802),
        a = r(59251),
        i = r(44902),
        l = r(58804),
        u = o(i());
      n(u, { getPolyfill: i, implementation: a, shim: l }), (e.exports = u);
    },
    58672: (e, t, r) => {
      'use strict';
      var n = r(47600)('SharedArrayBuffer.prototype.byteLength', !0);
      e.exports = n
        ? function (e) {
            if (!e || 'object' != typeof e) return !1;
            try {
              return n(e), !0;
            } catch (e) {
              return !1;
            }
          }
        : function (e) {
            return !1;
          };
    },
    58687: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['row'], ['row', 'rowgroup']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'grid'],
            ['roletype', 'structure', 'section', 'table', 'grid'],
            ['roletype', 'widget', 'composite', 'select', 'tree'],
            ['roletype', 'structure', 'section', 'group', 'select', 'tree'],
          ],
        });
    },
    58804: (e, t, r) => {
      'use strict';
      var n = r(7835).supportsDescriptors,
        o = r(44902),
        a = r(9278),
        i = Object.defineProperty,
        l = r(45125),
        u = r(28265),
        s = /a/;
      e.exports = function () {
        if (!n || !u)
          throw new l(
            'RegExp.prototype.flags requires a true ES5 environment that supports property descriptors'
          );
        var e = o(),
          t = u(s),
          r = a(t, 'flags');
        return (r && r.get === e) || i(t, 'flags', { configurable: !0, enumerable: !1, get: e }), e;
      };
    },
    58893: (e, t, r) => {
      'use strict';
      var n = r(8241),
        o = r(73542),
        a = r(63784);
      e.exports = function () {
        return a(n, o, arguments);
      };
    },
    58894: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.test = t.serialize = t.default = void 0);
      var n = r(70302);
      let o = /^((HTML|SVG)\w*)?Element$/,
        a = e => {
          try {
            return 'function' == typeof e.hasAttribute && e.hasAttribute('is');
          } catch {
            return !1;
          }
        },
        i = e => {
          let t = e.constructor.name,
            { nodeType: r, tagName: n } = e,
            i = ('string' == typeof n && n.includes('-')) || a(e);
          return (
            (1 === r && (o.test(t) || i)) ||
            (3 === r && 'Text' === t) ||
            (8 === r && 'Comment' === t) ||
            (11 === r && 'DocumentFragment' === t)
          );
        },
        l = e => {
          var t;
          return (null == e || null == (t = e.constructor) ? void 0 : t.name) && i(e);
        };
      function u(e) {
        return 11 === e.nodeType;
      }
      t.test = l;
      let s = (e, t, r, o, a, i) => {
        if (3 === e.nodeType) return (0, n.printText)(e.data, t);
        if (8 === e.nodeType) return (0, n.printComment)(e.data, t);
        let l = u(e) ? 'DocumentFragment' : e.tagName.toLowerCase();
        return ++o > t.maxDepth
          ? (0, n.printElementAsLeaf)(l, t)
          : (0, n.printElement)(
              l,
              (0, n.printProps)(
                u(e)
                  ? []
                  : Array.from(e.attributes)
                      .map(e => e.name)
                      .sort(),
                u(e)
                  ? {}
                  : Array.from(e.attributes).reduce((e, t) => ((e[t.name] = t.value), e), {}),
                t,
                r + t.indent,
                o,
                a,
                i
              ),
              (0, n.printChildren)(
                Array.prototype.slice.call(e.childNodes || e.children),
                t,
                r + t.indent,
                o,
                a,
                i
              ),
              t,
              r
            );
      };
      (t.serialize = s), (t.default = { serialize: s, test: l });
    },
    59251: (e, t, r) => {
      'use strict';
      var n = r(85365),
        o = r(64215),
        a = Object;
      e.exports = n(
        function () {
          if (this == null || this !== a(this))
            throw new o('RegExp.prototype.flags getter called on non-object');
          var e = '';
          return (
            this.hasIndices && (e += 'd'),
            this.global && (e += 'g'),
            this.ignoreCase && (e += 'i'),
            this.multiline && (e += 'm'),
            this.dotAll && (e += 's'),
            this.unicode && (e += 'u'),
            this.unicodeSets && (e += 'v'),
            this.sticky && (e += 'y'),
            e
          );
        },
        'get flags',
        !0
      );
    },
    59683: e => {
      'use strict';
      e.exports = Math.floor;
    },
    59829: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { attributes: [{ constraints: ['set'], name: 'aria-label' }], name: 'form' },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-labelledby' }],
                name: 'form',
              },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ constraints: ['set'], name: 'name' }], name: 'form' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    59913: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'glossary [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['definition'], ['term']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    60611: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'dedication [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    60657: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [
            { module: 'GRAPHICS', concept: { name: 'graphics-document' } },
            { module: 'ARIA', concept: { name: 'group' } },
            { module: 'ARIA', concept: { name: 'img' } },
            { module: 'GRAPHICS', concept: { name: 'graphics-symbol' } },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'group']],
        });
    },
    60801: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    61003: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-colindex': null,
            'aria-colspan': null,
            'aria-rowindex': null,
            'aria-rowspan': null,
          },
          relatedConcepts: [
            { concept: { constraints: ['descendant of table'], name: 'td' }, module: 'HTML' },
          ],
          requireContextRole: ['row'],
          requiredContextRole: ['row'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    61014: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'Device Independence Delivery Unit' } },
            { concept: { name: 'body' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    61072: (e, t, r) => {
      'use strict';
      var n = r(48017),
        o = function () {
          if (!Object.assign) return !1;
          for (var e = 'abcdefghijklmnopqrst', t = e.split(''), r = {}, n = 0; n < t.length; ++n)
            r[t[n]] = t[n];
          var o = Object.assign({}, r),
            a = '';
          for (var i in o) a += i;
          return e !== a;
        },
        a = function () {
          if (!Object.assign || !Object.preventExtensions) return !1;
          var e = Object.preventExtensions({ 1: 2 });
          try {
            Object.assign(e, 'xy');
          } catch (t) {
            return 'y' === e[1];
          }
          return !1;
        };
      e.exports = function () {
        return !Object.assign || o() || a() ? n : Object.assign;
      };
    },
    61293: e => {
      'use strict';
      var t = Object.prototype.toString,
        r = Math.max,
        n = function (e, t) {
          for (var r = [], n = 0; n < e.length; n += 1) r[n] = e[n];
          for (var o = 0; o < t.length; o += 1) r[o + e.length] = t[o];
          return r;
        },
        o = function (e, t) {
          for (var r = [], n = t || 0, o = 0; n < e.length; n += 1, o += 1) r[o] = e[n];
          return r;
        },
        a = function (e, t) {
          for (var r = '', n = 0; n < e.length; n += 1) (r += e[n]), n + 1 < e.length && (r += t);
          return r;
        };
      e.exports = function (e) {
        var i,
          l = this;
        if ('function' != typeof l || '[object Function]' !== t.apply(l))
          throw TypeError('Function.prototype.bind called on incompatible ' + l);
        for (var u = o(arguments, 1), s = r(0, l.length - u.length), c = [], d = 0; d < s; d++)
          c[d] = '$' + d;
        if (
          ((i = Function(
            'binder',
            'return function (' + a(c, ',') + '){ return binder.apply(this,arguments); }'
          )(function () {
            if (this instanceof i) {
              var t = l.apply(this, n(u, arguments));
              return Object(t) === t ? t : this;
            }
            return l.apply(e, n(u, arguments));
          })),
          l.prototype)
        ) {
          var p = function () {};
          (p.prototype = l.prototype), (i.prototype = new p()), (p.prototype = null);
        }
        return i;
      };
    },
    61304: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.test = t.serialize = t.default = void 0);
      var n = r(70302),
        o =
          'undefined' != typeof globalThis
            ? globalThis
            : void 0 !== o
              ? o
              : 'undefined' != typeof self
                ? self
                : 'undefined' != typeof window
                  ? window
                  : Function('return this')(),
        a = o['jest-symbol-do-not-touch'] || o.Symbol;
      let i = 'function' == typeof a && a.for ? a.for('react.test.json') : 0xea71357,
        l = e => {
          let { props: t } = e;
          return t
            ? Object.keys(t)
                .filter(e => void 0 !== t[e])
                .sort()
            : [];
        },
        u = (e, t, r, o, a, i) =>
          ++o > t.maxDepth
            ? (0, n.printElementAsLeaf)(e.type, t)
            : (0, n.printElement)(
                e.type,
                e.props ? (0, n.printProps)(l(e), e.props, t, r + t.indent, o, a, i) : '',
                e.children ? (0, n.printChildren)(e.children, t, r + t.indent, o, a, i) : '',
                t,
                r
              );
      t.serialize = u;
      let s = e => e && e.$$typeof === i;
      (t.test = s), (t.default = { serialize: u, test: s });
    },
    61310: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.test = t.serialize = t.default = void 0);
      var n = r(58460),
        o =
          'undefined' != typeof globalThis
            ? globalThis
            : void 0 !== o
              ? o
              : 'undefined' != typeof self
                ? self
                : 'undefined' != typeof window
                  ? window
                  : Function('return this')(),
        a = o['jest-symbol-do-not-touch'] || o.Symbol;
      let i = 'function' == typeof a && a.for ? a.for('jest.asymmetricMatcher') : 1267621,
        l = (e, t, r, o, a, i) => {
          let l = e.toString();
          return 'ArrayContaining' === l || 'ArrayNotContaining' === l
            ? ++o > t.maxDepth
              ? '[' + l + ']'
              : l + ' [' + (0, n.printListItems)(e.sample, t, r, o, a, i) + ']'
            : 'ObjectContaining' === l || 'ObjectNotContaining' === l
              ? ++o > t.maxDepth
                ? '[' + l + ']'
                : l + ' {' + (0, n.printObjectProperties)(e.sample, t, r, o, a, i) + '}'
              : 'StringMatching' === l ||
                  'StringNotMatching' === l ||
                  'StringContaining' === l ||
                  'StringNotContaining' === l
                ? l + ' ' + i(e.sample, t, r, o, a)
                : e.toAsymmetricMatcher();
        };
      t.serialize = l;
      let u = e => e && e.$$typeof === i;
      (t.test = u), (t.default = { serialize: l, test: u });
    },
    61781: (e, t, r) => {
      'use strict';
      var n = r(7835),
        o = r(34802),
        a = r(48017),
        i = r(61072),
        l = r(36786),
        u = o.apply(i()),
        s = function (e, t) {
          return u(Object, arguments);
        };
      n(s, { getPolyfill: i, implementation: a, shim: l }), (e.exports = s);
    },
    61869: (e, t, r) => {
      'use strict';
      r.d(t, { E5: () => tc, fE: () => S, jK: () => M, nj: () => tv, rC: () => tb });
      var n = r(31777),
        o = r(14945),
        a = r(56934),
        i = r(95490),
        l = r.n(i);
      e = r.hmd(e);
      var u = r(77051);
      function s(e) {
        return e.replace(/</g, '&lt;').replace(/>/g, '&gt;');
      }
      let c = (e, t, r, n, o, a, i) => {
          let l = n + r.indent,
            u = r.colors;
          return e
            .map(e => {
              let s = t[e],
                c = i(s, r, l, o, a);
              return (
                'string' != typeof s &&
                  (-1 !== c.indexOf('\n') && (c = r.spacingOuter + l + c + r.spacingOuter + n),
                  (c = '{' + c + '}')),
                r.spacingInner +
                  n +
                  u.prop.open +
                  e +
                  u.prop.close +
                  '=' +
                  u.value.open +
                  c +
                  u.value.close
              );
            })
            .join('');
        },
        d = (e, t, r, n, o, a) =>
          e
            .map(e => {
              let i = 'string' == typeof e ? p(e, t) : a(e, t, r, n, o);
              return '' === i && 'object' == typeof e && null !== e && 3 !== e.nodeType
                ? ''
                : t.spacingOuter + r + i;
            })
            .join(''),
        p = (e, t) => {
          let r = t.colors.content;
          return r.open + s(e) + r.close;
        },
        f = (e, t) => {
          let r = t.colors.comment;
          return r.open + '\x3c!--' + s(e) + '--\x3e' + r.close;
        },
        b = (e, t, r, n, o) => {
          let a = n.colors.tag;
          return (
            a.open +
            '<' +
            e +
            (t && a.close + t + n.spacingOuter + o + a.open) +
            (r
              ? '>' + a.close + r + n.spacingOuter + o + a.open + '</' + e
              : (t && !n.min ? '' : ' ') + '/') +
            '>' +
            a.close
          );
        },
        m = (e, t) => {
          let r = t.colors.tag;
          return r.open + '<' + e + r.close + ' …' + r.open + ' />' + r.close;
        },
        y = /^((HTML|SVG)\w*)?Element$/,
        v = e => {
          let t = e.constructor.name,
            { nodeType: r, tagName: n } = e,
            o =
              ('string' == typeof n && n.includes('-')) ||
              ('function' == typeof e.hasAttribute && e.hasAttribute('is'));
          return (
            (1 === r && (y.test(t) || o)) ||
            (3 === r && 'Text' === t) ||
            (8 === r && 'Comment' === t) ||
            (11 === r && 'DocumentFragment' === t)
          );
        };
      function h(e) {
        return 11 === e.nodeType;
      }
      let g = null,
        P = null,
        C = null;
      try {
        let t = e && e.require;
        (P = t.call(e, 'fs').readFileSync),
          (C = t.call(e, '@babel/code-frame').codeFrameColumns),
          (g = t.call(e, 'chalk'));
      } catch {}
      function q() {
        return (
          'undefined' != typeof jest &&
          null !== jest &&
          (!0 === setTimeout._isMockFunction ||
            Object.prototype.hasOwnProperty.call(setTimeout, 'clock'))
        );
      }
      function x() {
        if ('undefined' == typeof window) throw Error('Could not find default container');
        return window.document;
      }
      function E(e) {
        if (e.defaultView) return e.defaultView;
        if (e.ownerDocument && e.ownerDocument.defaultView) return e.ownerDocument.defaultView;
        if (e.window) return e.window;
        if (e.ownerDocument && null === e.ownerDocument.defaultView)
          throw Error('It looks like the window object is not available for the provided node.');
        if (e.then instanceof Function)
          throw Error(
            'It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?'
          );
        else if (Array.isArray(e))
          throw Error(
            'It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?'
          );
        else if ('function' == typeof e.debug && 'function' == typeof e.logTestingPlaygroundURL)
          throw Error(
            'It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?'
          );
        else throw Error('The given node is not an Element, the node type is: ' + typeof e + '.');
      }
      function w(e) {
        if (!e || 'function' != typeof e.querySelector || 'function' != typeof e.querySelectorAll) {
          var t;
          throw TypeError(
            'Expected container to be an Element, a Document or a DocumentFragment but got ' +
              ('object' == typeof (t = e) ? (null === t ? 'null' : t.constructor.name) : typeof t) +
              '.'
          );
        }
      }
      let O = () => {
          let e;
          try {
            var t;
            e = JSON.parse(null == u || null == (t = u.env) ? void 0 : t.COLORS);
          } catch (e) {}
          return 'boolean' == typeof e
            ? e
            : void 0 !== u && void 0 !== u.versions && void 0 !== u.versions.node;
        },
        { DOMCollection: R } = n.plugins;
      function j(e) {
        return 8 !== e.nodeType && (1 !== e.nodeType || !e.matches(A.defaultIgnore));
      }
      function S(e, t, r) {
        if (
          (void 0 === r && (r = {}),
          e || (e = x().body),
          'number' != typeof t && (t = (void 0 !== u && u.env.DEBUG_PRINT_LIMIT) || 7e3),
          0 === t)
        )
          return '';
        e.documentElement && (e = e.documentElement);
        let o = typeof e;
        if (('object' === o ? (o = e.constructor.name) : (e = {}), !('outerHTML' in e)))
          throw TypeError('Expected an element or document but got ' + o);
        let { filterNode: a = j, ...i } = r,
          l = n.format(e, {
            plugins: [
              {
                test: e => {
                  var t;
                  return (null == e || null == (t = e.constructor) ? void 0 : t.name) && v(e);
                },
                serialize: (e, t, r, n, o, i) => {
                  if (3 === e.nodeType) return p(e.data, t);
                  if (8 === e.nodeType) return f(e.data, t);
                  let l = h(e) ? 'DocumentFragment' : e.tagName.toLowerCase();
                  return ++n > t.maxDepth
                    ? m(l, t)
                    : b(
                        l,
                        c(
                          h(e)
                            ? []
                            : Array.from(e.attributes)
                                .map(e => e.name)
                                .sort(),
                          h(e)
                            ? {}
                            : Array.from(e.attributes).reduce(
                                (e, t) => ((e[t.name] = t.value), e),
                                {}
                              ),
                          t,
                          r + t.indent,
                          n,
                          o,
                          i
                        ),
                        d(
                          Array.prototype.slice.call(e.childNodes || e.children).filter(a),
                          t,
                          r + t.indent,
                          n,
                          o,
                          i
                        ),
                        t,
                        r
                      );
                },
              },
              R,
            ],
            printFunctionName: !1,
            highlight: O(),
            ...i,
          });
        return void 0 !== t && e.outerHTML.length > t ? l.slice(0, t) + '...' : l;
      }
      let _ = function () {
          let e =
            P && C
              ? (function (e) {
                  let t = e.indexOf('(') + 1,
                    r = e.indexOf(')'),
                    n = e.slice(t, r),
                    o = n.split(':'),
                    [a, i, l] = [o[0], parseInt(o[1], 10), parseInt(o[2], 10)],
                    u = '';
                  try {
                    u = P(a, 'utf-8');
                  } catch {
                    return '';
                  }
                  let s = C(
                    u,
                    { start: { line: i, column: l } },
                    { highlightCode: !0, linesBelow: 0 }
                  );
                  return g.dim(n) + '\n' + s + '\n';
                })(
                  Error()
                    .stack.split('\n')
                    .slice(1)
                    .find(e => !e.includes('node_modules/'))
                )
              : '';
          e ? console.log(S(...arguments) + '\n\n' + e) : console.log(S(...arguments));
        },
        A = {
          testIdAttribute: 'data-testid',
          asyncUtilTimeout: 1e3,
          asyncWrapper: e => e(),
          unstable_advanceTimersWrapper: e => e(),
          eventWrapper: e => e(),
          defaultHidden: !1,
          defaultIgnore: 'script, style',
          showOriginalStackTrace: !1,
          throwSuggestions: !1,
          getElementError(e, t) {
            let r = S(t),
              n = Error(
                [e, 'Ignored nodes: comments, ' + A.defaultIgnore + '\n' + r]
                  .filter(Boolean)
                  .join('\n\n')
              );
            return (n.name = 'TestingLibraryElementError'), n;
          },
          _disableExpensiveErrorDiagnostics: !1,
          computedStyleSupportsPseudoElements: !1,
        };
      function M(e) {
        'function' == typeof e && (e = e(A)), (A = { ...A, ...e });
      }
      let T = ['button', 'meter', 'output', 'progress', 'select', 'textarea', 'input'];
      function I(e) {
        let t;
        return 'label' === e.tagName.toLowerCase()
          ? (function e(t) {
              return T.includes(t.nodeName.toLowerCase())
                ? ''
                : 3 === t.nodeType
                  ? t.textContent
                  : Array.from(t.childNodes)
                      .map(t => e(t))
                      .join('');
            })(e)
          : e.value || e.textContent;
      }
      function k(e) {
        var t, r;
        if (void 0 !== e.labels) {
          return null != (t = e.labels) ? t : [];
        }
        return ((r = e),
        /BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(r.tagName) ||
          ('INPUT' === r.tagName && 'hidden' !== r.getAttribute('type')))
          ? Array.from(e.ownerDocument.querySelectorAll('label')).filter(t => t.control === e)
          : [];
      }
      function N(e, t, r) {
        let { selector: n = '*' } = void 0 === r ? {} : r,
          o = t.getAttribute('aria-labelledby'),
          a = o ? o.split(' ') : [];
        return a.length
          ? a.map(t => {
              let r = e.querySelector('[id="' + t + '"]');
              return r ? { content: I(r), formControl: null } : { content: '', formControl: null };
            })
          : Array.from(k(t)).map(e => ({
              content: I(e),
              formControl: Array.from(
                e.querySelectorAll('button, input, meter, output, progress, select, textarea')
              ).filter(e => e.matches(n))[0],
            }));
      }
      function F(e) {
        if (null == e)
          throw Error(
            'It looks like ' +
              e +
              ' was passed instead of a matcher. Did you do something like getByText(' +
              e +
              ')?'
          );
      }
      function B(e, t, r, n) {
        if ('string' != typeof e) return !1;
        F(r);
        let o = n(e);
        return 'string' == typeof r || 'number' == typeof r
          ? o.toLowerCase().includes(r.toString().toLowerCase())
          : 'function' == typeof r
            ? r(o, t)
            : H(r, o);
      }
      function L(e, t, r, n) {
        if ('string' != typeof e) return !1;
        F(r);
        let o = n(e);
        return r instanceof Function ? r(o, t) : r instanceof RegExp ? H(r, o) : o === String(r);
      }
      function U(e) {
        let { trim: t = !0, collapseWhitespace: r = !0 } = void 0 === e ? {} : e;
        return e => {
          let n = e;
          return (n = t ? n.trim() : n), (n = r ? n.replace(/\s+/g, ' ') : n);
        };
      }
      function D(e) {
        let { trim: t, collapseWhitespace: r, normalizer: n } = e;
        if (!n) return U({ trim: t, collapseWhitespace: r });
        if (void 0 !== t || void 0 !== r)
          throw Error(
            'trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer'
          );
        return n;
      }
      function H(e, t) {
        let r = e.test(t);
        return (
          e.global &&
            0 !== e.lastIndex &&
            (console.warn(
              'To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp.'
            ),
            (e.lastIndex = 0)),
          r
        );
      }
      function $(e) {
        return e.matches('input[type=submit], input[type=button], input[type=reset]')
          ? e.value
          : Array.from(e.childNodes)
              .filter(e => 3 === e.nodeType && !!e.textContent)
              .map(e => e.textContent)
              .join('');
      }
      let V = (function (e) {
        let t = [];
        for (let [r, n] of e.entries())
          t = [
            ...t,
            {
              match: (function (e) {
                let { attributes: t = [] } = e,
                  r = t.findIndex(e => e.value && 'type' === e.name && 'text' === e.value);
                r >= 0 && (t = [...t.slice(0, r), ...t.slice(r + 1)]);
                let n = (function (e) {
                  let { name: t, attributes: r } = e;
                  return (
                    '' +
                    t +
                    r
                      .map(e => {
                        let { name: t, value: r, constraints: n = [] } = e;
                        return -1 !== n.indexOf('undefined')
                          ? ':not([' + t + '])'
                          : r
                            ? '[' + t + '="' + r + '"]'
                            : '[' + t + ']';
                      })
                      .join('')
                  );
                })({ ...e, attributes: t });
                return e => (!(r >= 0) || 'text' === e.type) && e.matches(n);
              })(r),
              roles: Array.from(n),
              specificity: (function (e) {
                let { attributes: t = [] } = e;
                return t.length;
              })(r),
            },
          ];
        return t.sort(function (e, t) {
          let { specificity: r } = e,
            { specificity: n } = t;
          return n - r;
        });
      })(a._s);
      function W(e) {
        return (
          !0 === e.hidden ||
          'true' === e.getAttribute('aria-hidden') ||
          'none' === e.ownerDocument.defaultView.getComputedStyle(e).display
        );
      }
      function z(e, t) {
        void 0 === t && (t = {});
        let { isSubtreeInaccessible: r = W } = t;
        if ('hidden' === e.ownerDocument.defaultView.getComputedStyle(e).visibility) return !0;
        let n = e;
        for (; n; ) {
          if (r(n)) return !0;
          n = n.parentElement;
        }
        return !1;
      }
      function G(e) {
        for (let { match: t, roles: r } of V) if (t(e)) return [...r];
        return [];
      }
      function J(e, t) {
        let r = e.getAttribute(t);
        return 'true' === r || ('false' !== r && void 0);
      }
      let X = U();
      function K(e) {
        return RegExp(e.toLowerCase().replace(/[.*+\-?^${}()|[\]\\]/g, '\\$&'), 'i');
      }
      function Y(e, t, r, n) {
        let { variant: o, name: a } = n,
          i = '',
          l = {},
          u = [['Role', 'TestId'].includes(e) ? r : K(r)];
        a && (l.name = K(a)),
          'Role' === e &&
            z(t) &&
            ((l.hidden = !0),
            (i =
              'Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    ')),
          Object.keys(l).length > 0 && u.push(l);
        let s = o + 'By' + e;
        return {
          queryName: e,
          queryMethod: s,
          queryArgs: u,
          variant: o,
          warning: i,
          toString() {
            i && console.warn(i);
            let [e, t] = u;
            return (
              s +
              '(' +
              (e = 'string' == typeof e ? "'" + e + "'" : e) +
              (t = t
                ? ', { ' +
                  Object.entries(t)
                    .map(e => {
                      let [t, r] = e;
                      return t + ': ' + r;
                    })
                    .join(', ') +
                  ' }'
                : '') +
              ')'
            );
          },
        };
      }
      function Z(e, t, r) {
        return r && (!t || t.toLowerCase() === e.toLowerCase());
      }
      function Q(e, t, r) {
        var n, a;
        if ((void 0 === t && (t = 'get'), e.matches(A.defaultIgnore))) return;
        let i = null != (n = e.getAttribute('role')) ? n : null == (a = G(e)) ? void 0 : a[0];
        if ('generic' !== i && Z('Role', r, i))
          return Y('Role', e, i, {
            variant: t,
            name: (0, o.D0)(e, {
              computedStyleSupportsPseudoElements: A.computedStyleSupportsPseudoElements,
            }),
          });
        let l = N(document, e)
          .map(e => e.content)
          .join(' ');
        if (Z('LabelText', r, l)) return Y('LabelText', e, l, { variant: t });
        let u = e.getAttribute('placeholder');
        if (Z('PlaceholderText', r, u)) return Y('PlaceholderText', e, u, { variant: t });
        let s = X($(e));
        if (Z('Text', r, s)) return Y('Text', e, s, { variant: t });
        if (Z('DisplayValue', r, e.value)) return Y('DisplayValue', e, X(e.value), { variant: t });
        let c = e.getAttribute('alt');
        if (Z('AltText', r, c)) return Y('AltText', e, c, { variant: t });
        let d = e.getAttribute('title');
        if (Z('Title', r, d)) return Y('Title', e, d, { variant: t });
        let p = e.getAttribute(A.testIdAttribute);
        if (Z('TestId', r, p)) return Y('TestId', e, p, { variant: t });
      }
      function ee(e, t) {
        e.stack = t.stack.replace(t.message, e.message);
      }
      function et(e, t) {
        return A.getElementError(e, t);
      }
      function er(e, t, r, n) {
        let {
            exact: o = !0,
            collapseWhitespace: a,
            trim: i,
            normalizer: l,
          } = void 0 === n ? {} : n,
          u = o ? L : B,
          s = D({ collapseWhitespace: a, trim: i, normalizer: l });
        return Array.from(t.querySelectorAll('[' + e + ']')).filter(t =>
          u(t.getAttribute(e), t, r, s)
        );
      }
      function en(e, t) {
        return function (r) {
          for (var n, o = arguments.length, a = Array(o > 1 ? o - 1 : 0), i = 1; i < o; i++)
            a[i - 1] = arguments[i];
          let l = e(r, ...a);
          if (l.length > 1) {
            let e = l.map(e => et(null, e).message).join('\n\n');
            throw (
              ((n = t(r, ...a) + '\n\nHere are the matching elements:\n\n' + e),
              et(
                n +
                  '\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).',
                r
              ))
            );
          }
          return l[0] || null;
        };
      }
      function eo(e, t) {
        return A.getElementError(
          'A better query is available, try this:\n' + e.toString() + '\n',
          t
        );
      }
      function ea(e) {
        return (t, r, n, o) =>
          (function (e, t) {
            let r = Error('STACK_TRACE_MESSAGE');
            return A.asyncWrapper(() =>
              (function (e, t) {
                let {
                  container: r = x(),
                  timeout: n = A.asyncUtilTimeout,
                  showOriginalStackTrace: o = A.showOriginalStackTrace,
                  stackTraceError: a,
                  interval: i = 50,
                  onTimeout: l = e => ((e.message = A.getElementError(e.message, r).message), e),
                  mutationObserverOptions: u = {
                    subtree: !0,
                    childList: !0,
                    attributes: !0,
                    characterData: !0,
                  },
                } = t;
                if ('function' != typeof e)
                  throw TypeError('Received `callback` arg must be a function');
                return new Promise(async (t, s) => {
                  let c,
                    d,
                    p,
                    f = !1,
                    b = 'idle',
                    m = setTimeout(function () {
                      let e;
                      c
                        ? ((e = c), o || 'TestingLibraryElementError' !== e.name || ee(e, a))
                        : ((e = Error('Timed out in waitFor.')), o || ee(e, a)),
                        v(l(e), null);
                    }, n),
                    y = q();
                  if (y) {
                    let { unstable_advanceTimersWrapper: e } = A;
                    for (g(); !f; ) {
                      if (!q()) {
                        let e = Error(
                          "Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830"
                        );
                        o || ee(e, a), s(e);
                        return;
                      }
                      if (
                        (e(() => {
                          jest.advanceTimersByTime(i);
                        }),
                        g(),
                        f)
                      )
                        break;
                      await e(async () => {
                        await new Promise(e => {
                          setTimeout(e, 0), jest.advanceTimersByTime(0);
                        });
                      });
                    }
                  } else {
                    try {
                      w(r);
                    } catch (e) {
                      s(e);
                      return;
                    }
                    d = setInterval(h, i);
                    let { MutationObserver: e } = E(r);
                    (p = new e(h)).observe(r, u), g();
                  }
                  function v(e, r) {
                    (f = !0),
                      clearTimeout(m),
                      y || (clearInterval(d), p.disconnect()),
                      e ? s(e) : t(r);
                  }
                  function h() {
                    if (!q()) return g();
                    {
                      let e = Error(
                        "Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830"
                      );
                      return o || ee(e, a), s(e);
                    }
                  }
                  function g() {
                    if ('pending' !== b)
                      try {
                        let t = (function (e) {
                          try {
                            return (A._disableExpensiveErrorDiagnostics = !0), e();
                          } finally {
                            A._disableExpensiveErrorDiagnostics = !1;
                          }
                        })(e);
                        'function' == typeof (null == t ? void 0 : t.then)
                          ? ((b = 'pending'),
                            t.then(
                              e => {
                                (b = 'resolved'), v(null, e);
                              },
                              e => {
                                (b = 'rejected'), (c = e);
                              }
                            ))
                          : v(null, t);
                      } catch (e) {
                        c = e;
                      }
                  }
                });
              })(e, { stackTraceError: r, ...t })
            );
          })(() => e(t, r, n), { container: t, ...o });
      }
      let ei = (e, t, r) =>
          function (n) {
            for (var o = arguments.length, a = Array(o > 1 ? o - 1 : 0), i = 1; i < o; i++)
              a[i - 1] = arguments[i];
            let l = e(n, ...a),
              [{ suggest: u = A.throwSuggestions } = {}] = a.slice(-1);
            if (l && u) {
              let e = Q(l, r);
              if (e && !t.endsWith(e.queryName)) throw eo(e.toString(), n);
            }
            return l;
          },
        el = (e, t, r) =>
          function (n) {
            for (var o = arguments.length, a = Array(o > 1 ? o - 1 : 0), i = 1; i < o; i++)
              a[i - 1] = arguments[i];
            let l = e(n, ...a),
              [{ suggest: u = A.throwSuggestions } = {}] = a.slice(-1);
            if (l.length && u) {
              let e = [
                ...new Set(
                  l.map(e => {
                    var t;
                    return null == (t = Q(e, r)) ? void 0 : t.toString();
                  })
                ),
              ];
              if (1 === e.length && !t.endsWith(Q(l[0], r).queryName)) throw eo(e[0], n);
            }
            return l;
          };
      function eu(e, t, r) {
        var n, o;
        let a = ei(en(e, t), e.name, 'query'),
          i =
            ((n = e),
            (o = r),
            function (e) {
              for (var t = arguments.length, r = Array(t > 1 ? t - 1 : 0), a = 1; a < t; a++)
                r[a - 1] = arguments[a];
              let i = n(e, ...r);
              if (!i.length) throw A.getElementError(o(e, ...r), e);
              return i;
            }),
          l = en(i, t),
          u = ei(l, e.name, 'get'),
          s = el(i, e.name.replace('query', 'get'), 'getAll');
        return [a, s, u, ea(el(i, e.name, 'findAll')), ea(ei(l, e.name, 'find'))];
      }
      let es = function (e, t, r) {
          let {
              exact: n = !0,
              trim: o,
              collapseWhitespace: a,
              normalizer: i,
            } = void 0 === r ? {} : r,
            l = n ? L : B,
            u = D({ collapseWhitespace: a, trim: o, normalizer: i });
          return Array.from(e.querySelectorAll('label,input'))
            .map(e => ({ node: e, textToMatch: I(e) }))
            .filter(e => {
              let { textToMatch: t } = e;
              return null !== t;
            })
            .filter(e => {
              let { node: r, textToMatch: n } = e;
              return l(n, r, t, u);
            })
            .map(e => {
              let { node: t } = e;
              return t;
            });
        },
        ec = function (e, t, r) {
          let {
            selector: n = '*',
            exact: o = !0,
            collapseWhitespace: a,
            trim: i,
            normalizer: l,
          } = void 0 === r ? {} : r;
          w(e);
          let u = o ? L : B,
            s = D({ collapseWhitespace: a, trim: i, normalizer: l });
          return Array.from(
            new Set(
              Array.from(e.querySelectorAll('*'))
                .filter(e => k(e).length || e.hasAttribute('aria-labelledby'))
                .reduce((r, o) => {
                  let a = N(e, o, { selector: n });
                  a.filter(e => !!e.formControl).forEach(e => {
                    u(e.content, e.formControl, t, s) && e.formControl && r.push(e.formControl);
                  });
                  let i = a.filter(e => !!e.content).map(e => e.content);
                  return (
                    u(i.join(' '), o, t, s) && r.push(o),
                    i.length > 1 &&
                      i.forEach((e, n) => {
                        u(e, o, t, s) && r.push(o);
                        let a = [...i];
                        a.splice(n, 1), a.length > 1 && u(a.join(' '), o, t, s) && r.push(o);
                      }),
                    r
                  );
                }, [])
                .concat(er('aria-label', e, t, { exact: o, normalizer: s }))
            )
          ).filter(e => e.matches(n));
        },
        ed = function (e, t) {
          for (var r = arguments.length, n = Array(r > 2 ? r - 2 : 0), o = 2; o < r; o++)
            n[o - 2] = arguments[o];
          let a = ec(e, t, ...n);
          if (!a.length) {
            let r = es(e, t, ...n);
            if (r.length) {
              let n = r
                .map(t =>
                  (function (e, t) {
                    let r = t.getAttribute('for');
                    if (!r) return null;
                    let n = e.querySelector('[id="' + r + '"]');
                    return n ? n.tagName.toLowerCase() : null;
                  })(e, t)
                )
                .filter(e => !!e);
              if (n.length)
                throw A.getElementError(
                  n
                    .map(
                      e =>
                        'Found a label with the text of: ' +
                        t +
                        ', however the element associated with this label (<' +
                        e +
                        ' />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <' +
                        e +
                        ' />, you can use aria-label or aria-labelledby instead.'
                    )
                    .join('\n\n'),
                  e
                );
              throw A.getElementError(
                'Found a label with the text of: ' +
                  t +
                  ', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',
                e
              );
            }
            throw A.getElementError('Unable to find a label with the text of: ' + t, e);
          }
          return a;
        },
        ep = (e, t) => 'Found multiple elements with the text of: ' + t,
        ef = ei(en(ec, ep), ec.name, 'query'),
        eb = en(ed, ep),
        em = ea(el(ed, ed.name, 'findAll')),
        ey = ea(ei(eb, ed.name, 'find')),
        ev = el(ed, ed.name, 'getAll'),
        eh = ei(eb, ed.name, 'get'),
        eg = el(ec, ec.name, 'queryAll'),
        eP = function () {
          for (var e = arguments.length, t = Array(e), r = 0; r < e; r++) t[r] = arguments[r];
          return w(t[0]), er('placeholder', ...t);
        },
        eC = el(eP, eP.name, 'queryAll'),
        [eq, ex, eE, ew, eO] = eu(
          eP,
          (e, t) => 'Found multiple elements with the placeholder text of: ' + t,
          (e, t) => 'Unable to find an element with the placeholder text of: ' + t
        ),
        eR = function (e, t, r) {
          let {
            selector: n = '*',
            exact: o = !0,
            collapseWhitespace: a,
            trim: i,
            ignore: l = A.defaultIgnore,
            normalizer: u,
          } = void 0 === r ? {} : r;
          w(e);
          let s = o ? L : B,
            c = D({ collapseWhitespace: a, trim: i, normalizer: u }),
            d = [];
          return (
            'function' == typeof e.matches && e.matches(n) && (d = [e]),
            [...d, ...Array.from(e.querySelectorAll(n))]
              .filter(e => !l || !e.matches(l))
              .filter(e => s($(e), e, t, c))
          );
        },
        ej = el(eR, eR.name, 'queryAll'),
        [eS, e_, eA, eM, eT] = eu(
          eR,
          (e, t) => 'Found multiple elements with the text: ' + t,
          function (e, t, r) {
            void 0 === r && (r = {});
            let { collapseWhitespace: n, trim: o, normalizer: a, selector: i } = r,
              l = D({ collapseWhitespace: n, trim: o, normalizer: a })(t.toString());
            return (
              'Unable to find an element with the text: ' +
              (l !== t.toString() ? l + " (normalized from '" + t + "')" : t) +
              ((null != i ? i : '*') !== '*' ? ", which matches selector '" + i + "'" : '') +
              '. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.'
            );
          }
        ),
        eI = function (e, t, r) {
          let {
            exact: n = !0,
            collapseWhitespace: o,
            trim: a,
            normalizer: i,
          } = void 0 === r ? {} : r;
          w(e);
          let l = n ? L : B,
            u = D({ collapseWhitespace: o, trim: a, normalizer: i });
          return Array.from(e.querySelectorAll('input,textarea,select')).filter(e =>
            'SELECT' === e.tagName
              ? Array.from(e.options)
                  .filter(e => e.selected)
                  .some(e => l($(e), e, t, u))
              : l(e.value, e, t, u)
          );
        },
        ek = el(eI, eI.name, 'queryAll'),
        [eN, eF, eB, eL, eU] = eu(
          eI,
          (e, t) => 'Found multiple elements with the display value: ' + t + '.',
          (e, t) => 'Unable to find an element with the display value: ' + t + '.'
        ),
        eD = /^(img|input|area|.+-.+)$/i,
        eH = function (e, t, r) {
          return void 0 === r && (r = {}), w(e), er('alt', e, t, r).filter(e => eD.test(e.tagName));
        },
        e$ = el(eH, eH.name, 'queryAll'),
        [eV, eW, ez, eG, eJ] = eu(
          eH,
          (e, t) => 'Found multiple elements with the alt text: ' + t,
          (e, t) => 'Unable to find an element with the alt text: ' + t
        ),
        eX = e => {
          var t;
          return (
            'title' === e.tagName.toLowerCase() &&
            (null == (t = e.parentElement) ? void 0 : t.tagName.toLowerCase()) === 'svg'
          );
        },
        eK = function (e, t, r) {
          let {
            exact: n = !0,
            collapseWhitespace: o,
            trim: a,
            normalizer: i,
          } = void 0 === r ? {} : r;
          w(e);
          let l = n ? L : B,
            u = D({ collapseWhitespace: o, trim: a, normalizer: i });
          return Array.from(e.querySelectorAll('[title], svg > title')).filter(
            e => l(e.getAttribute('title'), e, t, u) || (eX(e) && l($(e), e, t, u))
          );
        },
        eY = el(eK, eK.name, 'queryAll'),
        [eZ, eQ, e0, e1, e6] = eu(
          eK,
          (e, t) => 'Found multiple elements with the title: ' + t + '.',
          (e, t) => 'Unable to find an element with the title: ' + t + '.'
        );
      function e2(e, t, r) {
        var n, i, l, u, s, c, d, p, f;
        let {
          exact: b = !0,
          collapseWhitespace: m,
          hidden: y = A.defaultHidden,
          name: v,
          description: h,
          trim: g,
          normalizer: P,
          queryFallbacks: C = !1,
          selected: q,
          checked: x,
          pressed: E,
          current: O,
          level: R,
          expanded: j,
        } = void 0 === r ? {} : r;
        w(e);
        let S = b ? L : B,
          _ = D({ collapseWhitespace: m, trim: g, normalizer: P });
        if (
          void 0 !== q &&
          (null == (n = a.Ot.get(t)) ? void 0 : n.props['aria-selected']) === void 0
        )
          throw Error('"aria-selected" is not supported on role "' + t + '".');
        if (
          void 0 !== x &&
          (null == (i = a.Ot.get(t)) ? void 0 : i.props['aria-checked']) === void 0
        )
          throw Error('"aria-checked" is not supported on role "' + t + '".');
        if (
          void 0 !== E &&
          (null == (l = a.Ot.get(t)) ? void 0 : l.props['aria-pressed']) === void 0
        )
          throw Error('"aria-pressed" is not supported on role "' + t + '".');
        if (
          void 0 !== O &&
          (null == (u = a.Ot.get(t)) ? void 0 : u.props['aria-current']) === void 0
        )
          throw Error('"aria-current" is not supported on role "' + t + '".');
        if (void 0 !== R && 'heading' !== t)
          throw Error('Role "' + t + '" cannot have "level" property.');
        if (
          void 0 !== j &&
          (null == (s = a.Ot.get(t)) ? void 0 : s.props['aria-expanded']) === void 0
        )
          throw Error('"aria-expanded" is not supported on role "' + t + '".');
        let M = new WeakMap();
        function T(e) {
          return M.has(e) || M.set(e, W(e)), M.get(e);
        }
        return Array.from(
          e.querySelectorAll(
            ((c = t),
            (d = b),
            (p = P ? _ : void 0),
            'string' != typeof c
              ? '*'
              : [d && !p ? '*[role~="' + c + '"]' : '*[role]']
                  .concat(
                    Array.from(
                      new Set(
                        Array.from(null != (f = a.wZ.get(c)) ? f : new Set()).map(e => {
                          let { name: t } = e;
                          return t;
                        })
                      )
                    )
                  )
                  .join(','))
          )
        )
          .filter(e => {
            if (e.hasAttribute('role')) {
              let r = e.getAttribute('role');
              if (C)
                return r
                  .split(' ')
                  .filter(Boolean)
                  .some(r => S(r, e, t, _));
              if (P) return S(r, e, t, _);
              let [n] = r.split(' ');
              return S(n, e, t, _);
            }
            return G(e).some(r => S(r, e, t, _));
          })
          .filter(e => {
            var t, r;
            return void 0 !== q
              ? q === ('OPTION' === e.tagName ? e.selected : J(e, 'aria-selected'))
              : void 0 !== x
                ? x ===
                  (function (e) {
                    if (!('indeterminate' in e) || !e.indeterminate)
                      return 'checked' in e ? e.checked : J(e, 'aria-checked');
                  })(e)
                : void 0 !== E
                  ? E === J(e, 'aria-pressed')
                  : void 0 !== O
                    ? O ===
                      (null !=
                        (t =
                          null != (r = J(e, 'aria-current'))
                            ? r
                            : e.getAttribute('aria-current')) && t)
                    : void 0 !== j
                      ? j === J(e, 'aria-expanded')
                      : void 0 === R ||
                        R ===
                          ((e.getAttribute('aria-level') && Number(e.getAttribute('aria-level'))) ||
                            { H1: 1, H2: 2, H3: 3, H4: 4, H5: 5, H6: 6 }[e.tagName]);
          })
          .filter(
            e =>
              void 0 === v ||
              L(
                (0, o.D0)(e, {
                  computedStyleSupportsPseudoElements: A.computedStyleSupportsPseudoElements,
                }),
                e,
                v,
                e => e
              )
          )
          .filter(
            e =>
              void 0 === h ||
              L(
                (0, o._1)(e, {
                  computedStyleSupportsPseudoElements: A.computedStyleSupportsPseudoElements,
                }),
                e,
                h,
                e => e
              )
          )
          .filter(e => !1 !== y || !1 === z(e, { isSubtreeInaccessible: T }));
      }
      let e3 = e => {
          let t = '';
          return void 0 === e
            ? ''
            : 'string' == typeof e
              ? ' and name "' + e + '"'
              : ' and name `' + e + '`';
        },
        e4 = el(e2, e2.name, 'queryAll'),
        [e7, e5, e8, e9, te] = eu(
          e2,
          function (e, t, r) {
            let { name: n } = void 0 === r ? {} : r;
            return 'Found multiple elements with the role "' + t + '"' + e3(n);
          },
          function (e, t, r) {
            let n,
              { hidden: a = A.defaultHidden, name: i, description: l } = void 0 === r ? {} : r;
            if (A._disableExpensiveErrorDiagnostics)
              return 'Unable to find role="' + t + '"' + e3(i);
            let u = '';
            Array.from(e.children).forEach(e => {
              u += (function (e, t) {
                let { hidden: r, includeDescription: n } = t;
                return Object.entries(
                  (function (e, t) {
                    let { hidden: r = !1 } = void 0 === t ? {} : t;
                    return (function e(t) {
                      return [t, ...Array.from(t.children).reduce((t, r) => [...t, ...e(r)], [])];
                    })(e)
                      .filter(e => !1 !== r || !1 === z(e))
                      .reduce((e, t) => {
                        let r = [];
                        return (
                          t.hasAttribute('role')
                            ? t.getAttribute('role').split(' ').slice(0, 1)
                            : G(t)
                        ).reduce(
                          (e, r) =>
                            Array.isArray(e[r]) ? { ...e, [r]: [...e[r], t] } : { ...e, [r]: [t] },
                          e
                        );
                      }, {});
                  })(e, { hidden: r })
                )
                  .filter(e => {
                    let [t] = e;
                    return 'generic' !== t;
                  })
                  .map(e => {
                    let [t, r] = e,
                      a = '-'.repeat(50);
                    return (
                      t +
                      ':\n\n' +
                      r
                        .map(e => {
                          let t =
                              'Name "' +
                              (0, o.D0)(e, {
                                computedStyleSupportsPseudoElements:
                                  A.computedStyleSupportsPseudoElements,
                              }) +
                              '":\n',
                            r = S(e.cloneNode(!1));
                          return n
                            ? '' +
                                t +
                                ('Description "' +
                                  (0, o._1)(e, {
                                    computedStyleSupportsPseudoElements:
                                      A.computedStyleSupportsPseudoElements,
                                  })) +
                                '":\n' +
                                r
                            : '' + t + r;
                        })
                        .join('\n\n') +
                      '\n\n' +
                      a
                    );
                  })
                  .join('\n');
              })(e, { hidden: a, includeDescription: void 0 !== l });
            }),
              (n =
                0 === u.length
                  ? !1 === a
                    ? 'There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole'
                    : 'There are no available roles.'
                  : (
                      '\nHere are the ' +
                      (!1 === a ? 'accessible' : 'available') +
                      ' roles:\n\n  ' +
                      u.replace(/\n/g, '\n  ').replace(/\n\s\s\n/g, '\n\n') +
                      '\n'
                    ).trim());
            let s = '',
              c = '';
            return (
              '\nUnable to find an ' +
              (!1 === a ? 'accessible ' : '') +
              'element with the role "' +
              t +
              '"' +
              (void 0 === i
                ? ''
                : 'string' == typeof i
                  ? ' and name "' + i + '"'
                  : ' and name `' + i + '`') +
              (void 0 === l
                ? ''
                : 'string' == typeof l
                  ? ' and description "' + l + '"'
                  : ' and description `' + l + '`') +
              '\n\n' +
              n
            ).trim();
          }
        ),
        tt = () => A.testIdAttribute,
        tr = function () {
          for (var e = arguments.length, t = Array(e), r = 0; r < e; r++) t[r] = arguments[r];
          return w(t[0]), er(tt(), ...t);
        },
        tn = el(tr, tr.name, 'queryAll'),
        [to, ta, ti, tl, tu] = eu(
          tr,
          (e, t) => 'Found multiple elements by: [' + tt() + '="' + t + '"]',
          (e, t) => 'Unable to find an element by: [' + tt() + '="' + t + '"]'
        );
      var ts = Object.freeze({
        __proto__: null,
        queryAllByLabelText: eg,
        queryByLabelText: ef,
        getAllByLabelText: ev,
        getByLabelText: eh,
        findAllByLabelText: em,
        findByLabelText: ey,
        queryByPlaceholderText: eq,
        queryAllByPlaceholderText: eC,
        getByPlaceholderText: eE,
        getAllByPlaceholderText: ex,
        findAllByPlaceholderText: ew,
        findByPlaceholderText: eO,
        queryByText: eS,
        queryAllByText: ej,
        getByText: eA,
        getAllByText: e_,
        findAllByText: eM,
        findByText: eT,
        queryByDisplayValue: eN,
        queryAllByDisplayValue: ek,
        getByDisplayValue: eB,
        getAllByDisplayValue: eF,
        findAllByDisplayValue: eL,
        findByDisplayValue: eU,
        queryByAltText: eV,
        queryAllByAltText: e$,
        getByAltText: ez,
        getAllByAltText: eW,
        findAllByAltText: eG,
        findByAltText: eJ,
        queryByTitle: eZ,
        queryAllByTitle: eY,
        getByTitle: e0,
        getAllByTitle: eQ,
        findAllByTitle: e1,
        findByTitle: e6,
        queryByRole: e7,
        queryAllByRole: e4,
        getAllByRole: e5,
        getByRole: e8,
        findAllByRole: e9,
        findByRole: te,
        queryByTestId: to,
        queryAllByTestId: tn,
        getByTestId: ti,
        getAllByTestId: ta,
        findAllByTestId: tl,
        findByTestId: tu,
      });
      function tc(e, t, r) {
        return (
          void 0 === t && (t = ts),
          void 0 === r && (r = {}),
          Object.keys(t).reduce((r, n) => {
            let o = t[n];
            return (r[n] = o.bind(null, e)), r;
          }, r)
        );
      }
      let td = e => !e || (Array.isArray(e) && !e.length),
        tp = {
          copy: {
            EventType: 'ClipboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          cut: {
            EventType: 'ClipboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          paste: {
            EventType: 'ClipboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          compositionEnd: {
            EventType: 'CompositionEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          compositionStart: {
            EventType: 'CompositionEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          compositionUpdate: {
            EventType: 'CompositionEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          keyDown: {
            EventType: 'KeyboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, charCode: 0, composed: !0 },
          },
          keyPress: {
            EventType: 'KeyboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, charCode: 0, composed: !0 },
          },
          keyUp: {
            EventType: 'KeyboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, charCode: 0, composed: !0 },
          },
          focus: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          blur: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          focusIn: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          focusOut: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          change: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !1 } },
          input: {
            EventType: 'InputEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          invalid: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !0 } },
          submit: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !0 } },
          reset: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !0 } },
          click: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, button: 0, composed: !0 },
          },
          contextMenu: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dblClick: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          drag: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dragEnd: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          dragEnter: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dragExit: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          dragLeave: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          dragOver: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dragStart: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          drop: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseDown: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseEnter: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          mouseLeave: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          mouseMove: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseOut: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseOver: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseUp: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          select: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !1 } },
          touchCancel: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          touchEnd: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          touchMove: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          touchStart: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          resize: { EventType: 'UIEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          scroll: { EventType: 'UIEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          wheel: {
            EventType: 'WheelEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          abort: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          canPlay: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          canPlayThrough: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          durationChange: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          emptied: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          encrypted: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          ended: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          loadedData: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          loadedMetadata: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          loadStart: { EventType: 'ProgressEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          pause: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          play: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          playing: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          progress: { EventType: 'ProgressEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          rateChange: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          seeked: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          seeking: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          stalled: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          suspend: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          timeUpdate: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          volumeChange: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          waiting: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          load: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          error: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          animationStart: {
            EventType: 'AnimationEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          animationEnd: {
            EventType: 'AnimationEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          animationIteration: {
            EventType: 'AnimationEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          transitionCancel: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          transitionEnd: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !0 },
          },
          transitionRun: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          transitionStart: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          pointerOver: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerEnter: { EventType: 'PointerEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          pointerDown: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerMove: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerUp: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerCancel: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          pointerOut: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerLeave: { EventType: 'PointerEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          gotPointerCapture: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          lostPointerCapture: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          popState: { EventType: 'PopStateEvent', defaultInit: { bubbles: !0, cancelable: !1 } },
          offline: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          online: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
        },
        tf = { doubleClick: 'dblClick' };
      function tb(e, t) {
        return A.eventWrapper(() => {
          if (!t) throw Error('Unable to fire an event - please provide an event object.');
          if (!e)
            throw Error('Unable to fire a "' + t.type + '" event - please provide a DOM element.');
          return e.dispatchEvent(t);
        });
      }
      function tm(e, t, r, n) {
        let o,
          { EventType: a = 'Event', defaultInit: i = {} } = void 0 === n ? {} : n;
        if (!t) throw Error('Unable to fire a "' + e + '" event - please provide a DOM element.');
        let l = { ...i, ...r },
          { target: { value: u, files: s, ...c } = {} } = l;
        void 0 !== u &&
          (function (e, t) {
            let { set: r } = Object.getOwnPropertyDescriptor(e, 'value') || {},
              { set: n } = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(e), 'value') || {};
            if (n && r !== n) n.call(e, t);
            else if (r) r.call(e, t);
            else throw Error('The given element does not have a value setter');
          })(t, u),
          void 0 !== s &&
            Object.defineProperty(t, 'files', {
              configurable: !0,
              enumerable: !0,
              writable: !0,
              value: s,
            }),
          Object.assign(t, c);
        let d = E(t),
          p = d[a] || d.Event;
        if ('function' == typeof p) o = new p(e, l);
        else {
          o = d.document.createEvent(a);
          let { bubbles: t, cancelable: r, detail: n, ...i } = l;
          o.initEvent(e, t, r, n),
            Object.keys(i).forEach(e => {
              o[e] = i[e];
            });
        }
        return (
          ['dataTransfer', 'clipboardData'].forEach(e => {
            let t = l[e];
            'object' == typeof t &&
              ('function' == typeof d.DataTransfer
                ? Object.defineProperty(o, e, {
                    value: Object.getOwnPropertyNames(t).reduce(
                      (e, r) => (Object.defineProperty(e, r, { value: t[r] }), e),
                      new d.DataTransfer()
                    ),
                  })
                : Object.defineProperty(o, e, { value: t }));
          }),
          o
        );
      }
      Object.keys(tp).forEach(e => {
        let { EventType: t, defaultInit: r } = tp[e],
          n = e.toLowerCase();
        (tm[e] = (e, o) => tm(n, e, o, { EventType: t, defaultInit: r })),
          (tb[e] = (t, r) => tb(t, tm[e](t, r)));
      }),
        Object.keys(tf).forEach(e => {
          let t = tf[e];
          tb[e] = function () {
            return tb[t](...arguments);
          };
        });
      let ty = {
          debug: (e, t, r) => (Array.isArray(e) ? e.forEach(e => _(e, t, r)) : _(e, t, r)),
          logTestingPlaygroundURL: function (e) {
            var t;
            if ((void 0 === e && (e = x().body), !e || !('innerHTML' in e)))
              return void console.log("The element you're providing isn't a valid DOM element.");
            if (!e.innerHTML)
              return void console.log("The provided element doesn't have any children.");
            let r =
              'https://testing-playground.com/#markup=' +
              ((t = e.innerHTML),
              l().compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g, '\n')));
            return console.log('Open this URL in your browser\n\n' + r), r;
          },
        },
        tv =
          'undefined' != typeof document && document.body
            ? tc(document.body, ts, ty)
            : Object.keys(ts).reduce(
                (e, t) => (
                  (e[t] = () => {
                    throw TypeError(
                      'For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error'
                    );
                  }),
                  e
                ),
                ty
              );
    },
    62296: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-expanded': null, 'aria-haspopup': null },
          relatedConcepts: [],
          requireContextRole: ['group', 'tree'],
          requiredContextRole: ['group', 'tree'],
          requiredOwnedElements: [],
          requiredProps: { 'aria-selected': null },
          superClass: [
            ['roletype', 'structure', 'section', 'listitem'],
            ['roletype', 'widget', 'input', 'option'],
          ],
        });
    },
    62366: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-haspopup': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-valuetext': null,
            'aria-orientation': 'horizontal',
            'aria-valuemax': '100',
            'aria-valuemin': '0',
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'range' }], name: 'input' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-valuenow': null },
          superClass: [
            ['roletype', 'widget', 'input'],
            ['roletype', 'structure', 'range'],
          ],
        });
    },
    62426: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    63253: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'dfn' }, module: 'HTML' },
            { concept: { name: 'dt' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    63371: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-label' }],
                name: 'section',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-labelledby' }],
                name: 'section',
              },
              module: 'HTML',
            },
            { concept: { name: 'Device Independence Glossart perceivable unit' } },
            { concept: { name: 'frame' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    63535: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-posinset': null,
            'aria-setsize': null,
          },
          relatedConcepts: [
            { concept: { name: 'MENU_ITEM' }, module: 'JAPI' },
            { concept: { name: 'listitem' }, module: 'ARIA' },
            { concept: { name: 'menuitem' }, module: 'HTML' },
            { concept: { name: 'option' }, module: 'ARIA' },
          ],
          requireContextRole: ['group', 'menu', 'menubar'],
          requiredContextRole: ['group', 'menu', 'menubar'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command']],
        });
    },
    63689: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'toc [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']],
        });
    },
    63784: (e, t, r) => {
      'use strict';
      var n = r(8241),
        o = r(73542),
        a = r(92914);
      e.exports = r(28621) || n.call(a, o);
    },
    63958: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-modal': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype']],
        });
    },
    63971: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: ['figure', 'grid', 'table'],
          requiredContextRole: ['figure', 'grid', 'table'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    64215: e => {
      'use strict';
      e.exports = TypeError;
    },
    64363: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'index [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']],
        });
    },
    65829: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'math' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    66380: e => {
      'use strict';
      e.exports = SyntaxError;
    },
    66447: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-pressed': null,
          },
          relatedConcepts: [
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'aria-pressed' },
                  { name: 'type', value: 'checkbox' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'aria-expanded', value: 'false' }], name: 'summary' },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [{ name: 'aria-expanded', value: 'true' }],
                constraints: [
                  'direct descendant of details element with the open attribute defined',
                ],
                name: 'summary',
              },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'type', value: 'button' }], name: 'input' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'type', value: 'image' }], name: 'input' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'type', value: 'reset' }], name: 'input' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'type', value: 'submit' }], name: 'input' },
              module: 'HTML',
            },
            { concept: { name: 'button' }, module: 'HTML' },
            { concept: { name: 'trigger' }, module: 'XForms' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command']],
        });
    },
    67194: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'glossref [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    67350: e => {
      'use strict';
      var t = Object.prototype.toString;
      e.exports = function (e) {
        var r = t.call(e),
          n = '[object Arguments]' === r;
        return (
          n ||
            (n =
              '[object Array]' !== r &&
              null !== e &&
              'object' == typeof e &&
              'number' == typeof e.length &&
              e.length >= 0 &&
              '[object Function]' === t.call(e.callee)),
          n
        );
      };
    },
    67541: e => {
      'use strict';
      e.exports = function () {
        if ('function' != typeof Symbol || 'function' != typeof Object.getOwnPropertySymbols)
          return !1;
        if ('symbol' == typeof Symbol.iterator) return !0;
        var e = {},
          t = Symbol('test'),
          r = Object(t);
        if (
          'string' == typeof t ||
          '[object Symbol]' !== Object.prototype.toString.call(t) ||
          '[object Symbol]' !== Object.prototype.toString.call(r)
        )
          return !1;
        for (var n in ((e[t] = 42), e)) return !1;
        if (
          ('function' == typeof Object.keys && 0 !== Object.keys(e).length) ||
          ('function' == typeof Object.getOwnPropertyNames &&
            0 !== Object.getOwnPropertyNames(e).length)
        )
          return !1;
        var o = Object.getOwnPropertySymbols(e);
        if (1 !== o.length || o[0] !== t || !Object.prototype.propertyIsEnumerable.call(e, t))
          return !1;
        if ('function' == typeof Object.getOwnPropertyDescriptor) {
          var a = Object.getOwnPropertyDescriptor(e, t);
          if (42 !== a.value || !0 !== a.enumerable) return !1;
        }
        return !0;
      };
    },
    67602: e => {
      var t = {}.toString;
      e.exports =
        Array.isArray ||
        function (e) {
          return '[object Array]' == t.call(e);
        };
    },
    68268: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-level': null, 'aria-posinset': null, 'aria-setsize': null },
          relatedConcepts: [
            {
              concept: { constraints: ['direct descendant of ol, ul or menu'], name: 'li' },
              module: 'HTML',
            },
            { concept: { name: 'item' }, module: 'XForms' },
          ],
          requireContextRole: ['directory', 'list'],
          requiredContextRole: ['directory', 'list'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    68783: (e, t, r) => {
      'use strict';
      var n = r(34802),
        o = r(47600),
        a = r(71005)('%ArrayBuffer%', !0),
        i = o('ArrayBuffer.prototype.byteLength', !0),
        l = o('Object.prototype.toString'),
        u = !!a && !i && new a(0).slice,
        s = !!u && n(u);
      e.exports =
        i || s
          ? function (e) {
              if (!e || 'object' != typeof e) return !1;
              try {
                return i ? i(e) : s(e, 0), !0;
              } catch (e) {
                return !1;
              }
            }
          : a
            ? function (e) {
                return '[object ArrayBuffer]' === l(e);
              }
            : function (e) {
                return !1;
              };
    },
    69206: (e, t, r) => {
      'use strict';
      var n = r(71005),
        o = r(16975),
        a = r(78486)(),
        i = r(9278),
        l = r(64215),
        u = n('%Math.floor%');
      e.exports = function (e, t) {
        if ('function' != typeof e) throw new l('`fn` is not a function');
        if ('number' != typeof t || t < 0 || t > 0xffffffff || u(t) !== t)
          throw new l('`length` must be a positive 32-bit integer');
        var r = arguments.length > 2 && !!arguments[2],
          n = !0,
          s = !0;
        if ('length' in e && i) {
          var c = i(e, 'length');
          c && !c.configurable && (n = !1), c && !c.writable && (s = !1);
        }
        return (n || s || !r) && (a ? o(e, 'length', t, !0, !0) : o(e, 'length', t)), e;
      };
    },
    69324: (e, t, r) => {
      'use strict';
      var n;
      if (!Object.keys) {
        var o = Object.prototype.hasOwnProperty,
          a = Object.prototype.toString,
          i = r(67350),
          l = Object.prototype.propertyIsEnumerable,
          u = !l.call({ toString: null }, 'toString'),
          s = l.call(function () {}, 'prototype'),
          c = [
            'toString',
            'toLocaleString',
            'valueOf',
            'hasOwnProperty',
            'isPrototypeOf',
            'propertyIsEnumerable',
            'constructor',
          ],
          d = function (e) {
            var t = e.constructor;
            return t && t.prototype === e;
          },
          p = {
            $applicationCache: !0,
            $console: !0,
            $external: !0,
            $frame: !0,
            $frameElement: !0,
            $frames: !0,
            $innerHeight: !0,
            $innerWidth: !0,
            $onmozfullscreenchange: !0,
            $onmozfullscreenerror: !0,
            $outerHeight: !0,
            $outerWidth: !0,
            $pageXOffset: !0,
            $pageYOffset: !0,
            $parent: !0,
            $scrollLeft: !0,
            $scrollTop: !0,
            $scrollX: !0,
            $scrollY: !0,
            $self: !0,
            $webkitIndexedDB: !0,
            $webkitStorageInfo: !0,
            $window: !0,
          },
          f = (function () {
            if ('undefined' == typeof window) return !1;
            for (var e in window)
              try {
                if (
                  !p['$' + e] &&
                  o.call(window, e) &&
                  null !== window[e] &&
                  'object' == typeof window[e]
                )
                  try {
                    d(window[e]);
                  } catch (e) {
                    return !0;
                  }
              } catch (e) {
                return !0;
              }
            return !1;
          })(),
          b = function (e) {
            if ('undefined' == typeof window || !f) return d(e);
            try {
              return d(e);
            } catch (e) {
              return !1;
            }
          };
        n = function (e) {
          var t = null !== e && 'object' == typeof e,
            r = '[object Function]' === a.call(e),
            n = i(e),
            l = t && '[object String]' === a.call(e),
            d = [];
          if (!t && !r && !n) throw TypeError('Object.keys called on a non-object');
          var p = s && r;
          if (l && e.length > 0 && !o.call(e, 0))
            for (var f = 0; f < e.length; ++f) d.push(String(f));
          if (n && e.length > 0) for (var m = 0; m < e.length; ++m) d.push(String(m));
          else for (var y in e) !(p && 'prototype' === y) && o.call(e, y) && d.push(String(y));
          if (u)
            for (var v = b(e), h = 0; h < c.length; ++h)
              !(v && 'constructor' === c[h]) && o.call(e, c[h]) && d.push(c[h]);
          return d;
        };
      }
      e.exports = n;
    },
    69566: (e, t, r) => {
      'use strict';
      var n = r(47600)('ArrayBuffer.prototype.byteLength', !0),
        o = r(68783);
      e.exports = function (e) {
        return o(e) ? (n ? n(e) : e.byteLength) : NaN;
      };
    },
    70302: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.printText =
          t.printProps =
          t.printElementAsLeaf =
          t.printElement =
          t.printComment =
          t.printChildren =
            void 0);
      var n = (function (e) {
        return e && e.__esModule ? e : { default: e };
      })(r(48968));
      (t.printProps = (e, t, r, n, o, a, i) => {
        let l = n + r.indent,
          u = r.colors;
        return e
          .map(e => {
            let s = t[e],
              c = i(s, r, l, o, a);
            return (
              'string' != typeof s &&
                (-1 !== c.indexOf('\n') && (c = r.spacingOuter + l + c + r.spacingOuter + n),
                (c = '{' + c + '}')),
              r.spacingInner +
                n +
                u.prop.open +
                e +
                u.prop.close +
                '=' +
                u.value.open +
                c +
                u.value.close
            );
          })
          .join('');
      }),
        (t.printChildren = (e, t, r, n, a, i) =>
          e
            .map(e => t.spacingOuter + r + ('string' == typeof e ? o(e, t) : i(e, t, r, n, a)))
            .join(''));
      let o = (e, t) => {
        let r = t.colors.content;
        return r.open + (0, n.default)(e) + r.close;
      };
      (t.printText = o),
        (t.printComment = (e, t) => {
          let r = t.colors.comment;
          return r.open + '\x3c!--' + (0, n.default)(e) + '--\x3e' + r.close;
        }),
        (t.printElement = (e, t, r, n, o) => {
          let a = n.colors.tag;
          return (
            a.open +
            '<' +
            e +
            (t && a.close + t + n.spacingOuter + o + a.open) +
            (r
              ? '>' + a.close + r + n.spacingOuter + o + a.open + '</' + e
              : (t && !n.min ? '' : ' ') + '/') +
            '>' +
            a.close
          );
        }),
        (t.printElementAsLeaf = (e, t) => {
          let r = t.colors.tag;
          return r.open + '<' + e + r.close + ' …' + r.open + ' />' + r.close;
        });
    },
    70345: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'afterword [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    70445: (e, t, r) => {
      'use strict';
      var n = r(71005),
        o = r(47600),
        a = n('%WeakSet%', !0),
        i = o('WeakSet.prototype.has', !0);
      if (i) {
        var l = o('WeakMap.prototype.has', !0);
        e.exports = function (e) {
          if (!e || 'object' != typeof e) return !1;
          try {
            if ((i(e, i), l))
              try {
                l(e, l);
              } catch (e) {
                return !0;
              }
            return e instanceof a;
          } catch (e) {}
          return !1;
        };
      } else
        e.exports = function (e) {
          return !1;
        };
    },
    70617: (e, t, r) => {
      'use strict';
      var n = r(44169),
        o = Object.prototype.toString,
        a = Object.prototype.hasOwnProperty,
        i = function (e, t, r) {
          for (var n = 0, o = e.length; n < o; n++)
            a.call(e, n) && (null == r ? t(e[n], n, e) : t.call(r, e[n], n, e));
        },
        l = function (e, t, r) {
          for (var n = 0, o = e.length; n < o; n++)
            null == r ? t(e.charAt(n), n, e) : t.call(r, e.charAt(n), n, e);
        },
        u = function (e, t, r) {
          for (var n in e) a.call(e, n) && (null == r ? t(e[n], n, e) : t.call(r, e[n], n, e));
        };
      e.exports = function (e, t, r) {
        var a;
        if (!n(t)) throw TypeError('iterator must be a function');
        (arguments.length >= 3 && (a = r), '[object Array]' === o.call(e))
          ? i(e, t, a)
          : 'string' == typeof e
            ? l(e, t, a)
            : u(e, t, a);
      };
    },
    70863: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = function (e, t) {
          return (
            'function' == typeof Symbol &&
              'symbol' === o(Symbol.iterator) &&
              Object.defineProperty(e, Symbol.iterator, { value: n.default.bind(t) }),
            e
          );
        });
      var n = (function (e) {
        return e && e.__esModule ? e : { default: e };
      })(r(56711));
      function o(e) {
        return (o =
          'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  'function' == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? 'symbol'
                  : typeof e;
              })(e);
      }
    },
    71005: (e, t, r) => {
      'use strict';
      var n,
        o = r(45760),
        a = r(45125),
        i = r(51569),
        l = r(78508),
        u = r(2340),
        s = r(66380),
        c = r(64215),
        d = r(19543),
        p = r(31553),
        f = r(59683),
        b = r(12239),
        m = r(88221),
        y = r(38507),
        v = r(32641),
        h = r(1586),
        g = Function,
        P = function (e) {
          try {
            return g('"use strict"; return (' + e + ').constructor;')();
          } catch (e) {}
        },
        C = r(9278),
        q = r(97783),
        x = function () {
          throw new c();
        },
        E = C
          ? (function () {
              try {
                return arguments.callee, x;
              } catch (e) {
                try {
                  return C(arguments, 'callee').get;
                } catch (e) {
                  return x;
                }
              }
            })()
          : x,
        w = r(2427)(),
        O = r(28265),
        R = r(10701),
        j = r(6333),
        S = r(73542),
        _ = r(92914),
        A = {},
        M = 'undefined' != typeof Uint8Array && O ? O(Uint8Array) : n,
        T = {
          __proto__: null,
          '%AggregateError%': 'undefined' == typeof AggregateError ? n : AggregateError,
          '%Array%': Array,
          '%ArrayBuffer%': 'undefined' == typeof ArrayBuffer ? n : ArrayBuffer,
          '%ArrayIteratorPrototype%': w && O ? O([][Symbol.iterator]()) : n,
          '%AsyncFromSyncIteratorPrototype%': n,
          '%AsyncFunction%': A,
          '%AsyncGenerator%': A,
          '%AsyncGeneratorFunction%': A,
          '%AsyncIteratorPrototype%': A,
          '%Atomics%': 'undefined' == typeof Atomics ? n : Atomics,
          '%BigInt%': 'undefined' == typeof BigInt ? n : BigInt,
          '%BigInt64Array%': 'undefined' == typeof BigInt64Array ? n : BigInt64Array,
          '%BigUint64Array%': 'undefined' == typeof BigUint64Array ? n : BigUint64Array,
          '%Boolean%': Boolean,
          '%DataView%': 'undefined' == typeof DataView ? n : DataView,
          '%Date%': Date,
          '%decodeURI%': decodeURI,
          '%decodeURIComponent%': decodeURIComponent,
          '%encodeURI%': encodeURI,
          '%encodeURIComponent%': encodeURIComponent,
          '%Error%': a,
          '%eval%': eval,
          '%EvalError%': i,
          '%Float16Array%': 'undefined' == typeof Float16Array ? n : Float16Array,
          '%Float32Array%': 'undefined' == typeof Float32Array ? n : Float32Array,
          '%Float64Array%': 'undefined' == typeof Float64Array ? n : Float64Array,
          '%FinalizationRegistry%':
            'undefined' == typeof FinalizationRegistry ? n : FinalizationRegistry,
          '%Function%': g,
          '%GeneratorFunction%': A,
          '%Int8Array%': 'undefined' == typeof Int8Array ? n : Int8Array,
          '%Int16Array%': 'undefined' == typeof Int16Array ? n : Int16Array,
          '%Int32Array%': 'undefined' == typeof Int32Array ? n : Int32Array,
          '%isFinite%': isFinite,
          '%isNaN%': isNaN,
          '%IteratorPrototype%': w && O ? O(O([][Symbol.iterator]())) : n,
          '%JSON%': 'object' == typeof JSON ? JSON : n,
          '%Map%': 'undefined' == typeof Map ? n : Map,
          '%MapIteratorPrototype%':
            'undefined' != typeof Map && w && O ? O(new Map()[Symbol.iterator]()) : n,
          '%Math%': Math,
          '%Number%': Number,
          '%Object%': o,
          '%Object.getOwnPropertyDescriptor%': C,
          '%parseFloat%': parseFloat,
          '%parseInt%': parseInt,
          '%Promise%': 'undefined' == typeof Promise ? n : Promise,
          '%Proxy%': 'undefined' == typeof Proxy ? n : Proxy,
          '%RangeError%': l,
          '%ReferenceError%': u,
          '%Reflect%': 'undefined' == typeof Reflect ? n : Reflect,
          '%RegExp%': RegExp,
          '%Set%': 'undefined' == typeof Set ? n : Set,
          '%SetIteratorPrototype%':
            'undefined' != typeof Set && w && O ? O(new Set()[Symbol.iterator]()) : n,
          '%SharedArrayBuffer%': 'undefined' == typeof SharedArrayBuffer ? n : SharedArrayBuffer,
          '%String%': String,
          '%StringIteratorPrototype%': w && O ? O(''[Symbol.iterator]()) : n,
          '%Symbol%': w ? Symbol : n,
          '%SyntaxError%': s,
          '%ThrowTypeError%': E,
          '%TypedArray%': M,
          '%TypeError%': c,
          '%Uint8Array%': 'undefined' == typeof Uint8Array ? n : Uint8Array,
          '%Uint8ClampedArray%': 'undefined' == typeof Uint8ClampedArray ? n : Uint8ClampedArray,
          '%Uint16Array%': 'undefined' == typeof Uint16Array ? n : Uint16Array,
          '%Uint32Array%': 'undefined' == typeof Uint32Array ? n : Uint32Array,
          '%URIError%': d,
          '%WeakMap%': 'undefined' == typeof WeakMap ? n : WeakMap,
          '%WeakRef%': 'undefined' == typeof WeakRef ? n : WeakRef,
          '%WeakSet%': 'undefined' == typeof WeakSet ? n : WeakSet,
          '%Function.prototype.call%': _,
          '%Function.prototype.apply%': S,
          '%Object.defineProperty%': q,
          '%Object.getPrototypeOf%': R,
          '%Math.abs%': p,
          '%Math.floor%': f,
          '%Math.max%': b,
          '%Math.min%': m,
          '%Math.pow%': y,
          '%Math.round%': v,
          '%Math.sign%': h,
          '%Reflect.getPrototypeOf%': j,
        };
      if (O)
        try {
          null.error;
        } catch (e) {
          var I = O(O(e));
          T['%Error.prototype%'] = I;
        }
      var k = function e(t) {
          var r;
          if ('%AsyncFunction%' === t) r = P('async function () {}');
          else if ('%GeneratorFunction%' === t) r = P('function* () {}');
          else if ('%AsyncGeneratorFunction%' === t) r = P('async function* () {}');
          else if ('%AsyncGenerator%' === t) {
            var n = e('%AsyncGeneratorFunction%');
            n && (r = n.prototype);
          } else if ('%AsyncIteratorPrototype%' === t) {
            var o = e('%AsyncGenerator%');
            o && O && (r = O(o.prototype));
          }
          return (T[t] = r), r;
        },
        N = {
          __proto__: null,
          '%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],
          '%ArrayPrototype%': ['Array', 'prototype'],
          '%ArrayProto_entries%': ['Array', 'prototype', 'entries'],
          '%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],
          '%ArrayProto_keys%': ['Array', 'prototype', 'keys'],
          '%ArrayProto_values%': ['Array', 'prototype', 'values'],
          '%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],
          '%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],
          '%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],
          '%BooleanPrototype%': ['Boolean', 'prototype'],
          '%DataViewPrototype%': ['DataView', 'prototype'],
          '%DatePrototype%': ['Date', 'prototype'],
          '%ErrorPrototype%': ['Error', 'prototype'],
          '%EvalErrorPrototype%': ['EvalError', 'prototype'],
          '%Float32ArrayPrototype%': ['Float32Array', 'prototype'],
          '%Float64ArrayPrototype%': ['Float64Array', 'prototype'],
          '%FunctionPrototype%': ['Function', 'prototype'],
          '%Generator%': ['GeneratorFunction', 'prototype'],
          '%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],
          '%Int8ArrayPrototype%': ['Int8Array', 'prototype'],
          '%Int16ArrayPrototype%': ['Int16Array', 'prototype'],
          '%Int32ArrayPrototype%': ['Int32Array', 'prototype'],
          '%JSONParse%': ['JSON', 'parse'],
          '%JSONStringify%': ['JSON', 'stringify'],
          '%MapPrototype%': ['Map', 'prototype'],
          '%NumberPrototype%': ['Number', 'prototype'],
          '%ObjectPrototype%': ['Object', 'prototype'],
          '%ObjProto_toString%': ['Object', 'prototype', 'toString'],
          '%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],
          '%PromisePrototype%': ['Promise', 'prototype'],
          '%PromiseProto_then%': ['Promise', 'prototype', 'then'],
          '%Promise_all%': ['Promise', 'all'],
          '%Promise_reject%': ['Promise', 'reject'],
          '%Promise_resolve%': ['Promise', 'resolve'],
          '%RangeErrorPrototype%': ['RangeError', 'prototype'],
          '%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],
          '%RegExpPrototype%': ['RegExp', 'prototype'],
          '%SetPrototype%': ['Set', 'prototype'],
          '%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],
          '%StringPrototype%': ['String', 'prototype'],
          '%SymbolPrototype%': ['Symbol', 'prototype'],
          '%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],
          '%TypedArrayPrototype%': ['TypedArray', 'prototype'],
          '%TypeErrorPrototype%': ['TypeError', 'prototype'],
          '%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],
          '%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],
          '%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],
          '%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],
          '%URIErrorPrototype%': ['URIError', 'prototype'],
          '%WeakMapPrototype%': ['WeakMap', 'prototype'],
          '%WeakSetPrototype%': ['WeakSet', 'prototype'],
        },
        F = r(8241),
        B = r(11351),
        L = F.call(_, Array.prototype.concat),
        U = F.call(S, Array.prototype.splice),
        D = F.call(_, String.prototype.replace),
        H = F.call(_, String.prototype.slice),
        $ = F.call(_, RegExp.prototype.exec),
        V =
          /[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,
        W = /\\(\\)?/g,
        z = function (e) {
          var t = H(e, 0, 1),
            r = H(e, -1);
          if ('%' === t && '%' !== r) throw new s('invalid intrinsic syntax, expected closing `%`');
          if ('%' === r && '%' !== t) throw new s('invalid intrinsic syntax, expected opening `%`');
          var n = [];
          return (
            D(e, V, function (e, t, r, o) {
              n[n.length] = r ? D(o, W, '$1') : t || e;
            }),
            n
          );
        },
        G = function (e, t) {
          var r,
            n = e;
          if ((B(N, n) && (n = '%' + (r = N[n])[0] + '%'), B(T, n))) {
            var o = T[n];
            if ((o === A && (o = k(n)), void 0 === o && !t))
              throw new c(
                'intrinsic ' + e + ' exists, but is not available. Please file an issue!'
              );
            return { alias: r, name: n, value: o };
          }
          throw new s('intrinsic ' + e + ' does not exist!');
        };
      e.exports = function (e, t) {
        if ('string' != typeof e || 0 === e.length)
          throw new c('intrinsic name must be a non-empty string');
        if (arguments.length > 1 && 'boolean' != typeof t)
          throw new c('"allowMissing" argument must be a boolean');
        if (null === $(/^%?[^%]*%?$/, e))
          throw new s(
            '`%` may not be present anywhere but at the beginning and end of the intrinsic name'
          );
        var r = z(e),
          n = r.length > 0 ? r[0] : '',
          o = G('%' + n + '%', t),
          a = o.name,
          i = o.value,
          l = !1,
          u = o.alias;
        u && ((n = u[0]), U(r, L([0, 1], u)));
        for (var d = 1, p = !0; d < r.length; d += 1) {
          var f = r[d],
            b = H(f, 0, 1),
            m = H(f, -1);
          if (
            ('"' === b || "'" === b || '`' === b || '"' === m || "'" === m || '`' === m) &&
            b !== m
          )
            throw new s('property names with quotes must have matching quotes');
          if ((('constructor' !== f && p) || (l = !0), (n += '.' + f), B(T, (a = '%' + n + '%'))))
            i = T[a];
          else if (null != i) {
            if (!(f in i)) {
              if (!t)
                throw new c(
                  'base intrinsic for ' + e + ' exists, but the property is not available.'
                );
              return;
            }
            if (C && d + 1 >= r.length) {
              var y = C(i, f);
              i = (p = !!y) && 'get' in y && !('originalValue' in y.get) ? y.get : i[f];
            } else (p = B(i, f)), (i = i[f]);
            p && !l && (T[a] = i);
          }
        }
        return i;
      };
    },
    71807: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-valuetext': null,
            'aria-orientation': 'vertical',
            'aria-valuemax': '100',
            'aria-valuemin': '0',
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-controls': null, 'aria-valuenow': null },
          superClass: [
            ['roletype', 'structure', 'range'],
            ['roletype', 'widget'],
          ],
        });
    },
    71876: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-valuetext': null, 'aria-valuemax': '100', 'aria-valuemin': '0' },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-valuenow': null },
          superClass: [['roletype', 'structure', 'range']],
        });
    },
    72231: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-live': 'polite' },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    72498: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    72800: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-activedescendant': null,
            'aria-autocomplete': null,
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-expanded': 'false',
            'aria-haspopup': 'listbox',
          },
          relatedConcepts: [
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'email' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'search' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'tel' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'text' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'url' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'url' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'multiple' },
                  { constraints: ['undefined'], name: 'size' },
                ],
                name: 'select',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'multiple' },
                  { name: 'size', value: 1 },
                ],
                name: 'select',
              },
              module: 'HTML',
            },
            { concept: { name: 'select' }, module: 'XForms' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-controls': null, 'aria-expanded': 'false' },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    73121: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'conclusion [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    73542: e => {
      'use strict';
      e.exports = Function.prototype.apply;
    },
    74495: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'figure' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    74706: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'help [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'note']],
        });
    },
    74880: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-invalid': null,
            'aria-multiselectable': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-orientation': 'vertical',
          },
          relatedConcepts: [
            {
              concept: {
                attributes: [{ constraints: ['>1'], name: 'size' }, { name: 'multiple' }],
                name: 'select',
              },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ constraints: ['>1'], name: 'size' }], name: 'select' },
              module: 'HTML',
            },
            { concept: { attributes: [{ name: 'multiple' }], name: 'select' }, module: 'HTML' },
            { concept: { name: 'datalist' }, module: 'HTML' },
            { concept: { name: 'list' }, module: 'ARIA' },
            { concept: { name: 'select' }, module: 'XForms' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['option', 'group'], ['option']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    75166: (e, t, r) => {
      'use strict';
      var n = Array.prototype.slice,
        o = r(67350),
        a = Object.keys,
        i = a
          ? function (e) {
              return a(e);
            }
          : r(69324),
        l = Object.keys;
      (i.shim = function () {
        return (
          Object.keys
            ? !(function () {
                var e = Object.keys(arguments);
                return e && e.length === arguments.length;
              })(1, 2) &&
              (Object.keys = function (e) {
                return o(e) ? l(n.call(e)) : l(e);
              })
            : (Object.keys = i),
          Object.keys || i
        );
      }),
        (e.exports = i);
    },
    76055: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-selected': null,
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'role', value: 'gridcell' }], name: 'td' },
              module: 'HTML',
            },
          ],
          requireContextRole: ['row'],
          requiredContextRole: ['row'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'cell'],
            ['roletype', 'widget'],
          ],
        });
    },
    76802: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ module: 'DAISY Guide' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'list']],
        });
    },
    76821: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'qna [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    77335: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'menuitem' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget']],
        });
    },
    77379: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['article']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'list']],
        });
    },
    78165: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = m(r(77335)),
        o = m(r(8377)),
        a = m(r(16134)),
        i = m(r(62426)),
        l = m(r(24379)),
        u = m(r(46586)),
        s = m(r(13117)),
        c = m(r(47147)),
        d = m(r(51650)),
        p = m(r(49691)),
        f = m(r(87062)),
        b = m(r(63958));
      function m(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['command', n.default],
        ['composite', o.default],
        ['input', a.default],
        ['landmark', i.default],
        ['range', l.default],
        ['roletype', u.default],
        ['section', s.default],
        ['sectionhead', c.default],
        ['select', d.default],
        ['structure', p.default],
        ['widget', f.default],
        ['window', b.default],
      ];
    },
    78486: (e, t, r) => {
      'use strict';
      var n = r(97783),
        o = function () {
          return !!n;
        };
      (o.hasArrayLengthDefineBug = function () {
        if (!n) return null;
        try {
          return 1 !== n([], 'length', { value: 1 }).length;
        } catch (e) {
          return !0;
        }
      }),
        (e.exports = o);
    },
    78508: e => {
      'use strict';
      e.exports = RangeError;
    },
    78657: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'introduction [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    79056: (e, t, r) => {
      'use strict';
      var n = r(67541);
      e.exports = function () {
        return n() && !!Symbol.toStringTag;
      };
    },
    79566: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'page-list [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']],
        });
    },
    79930: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'main' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    81733: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'credits [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    82084: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'errata [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    82099: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [
            { module: 'GRAPHICS', concept: { name: 'graphics-object' } },
            { module: 'ARIA', concept: { name: 'img' } },
            { module: 'ARIA', concept: { name: 'article' } },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'document']],
        });
    },
    82957: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'bibliography [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['doc-biblioentry']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    83629: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'epilogue [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    83846: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-orientation': 'horizontal',
            'aria-valuemax': '100',
            'aria-valuemin': '0',
            'aria-valuenow': null,
            'aria-valuetext': null,
          },
          relatedConcepts: [{ concept: { name: 'hr' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    83874: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'content'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'referrer [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    84446: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-checked': null,
            'aria-posinset': null,
            'aria-setsize': null,
            'aria-selected': 'false',
          },
          relatedConcepts: [
            { concept: { name: 'item' }, module: 'XForms' },
            { concept: { name: 'listitem' }, module: 'ARIA' },
            { concept: { name: 'option' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-selected': 'false' },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    84759: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'rearnotes [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['doc-endnote']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    85365: (e, t, r) => {
      'use strict';
      var n = r(16975),
        o = r(78486)(),
        a = r(54767).functionsHaveConfigurableNames(),
        i = r(64215);
      e.exports = function (e, t) {
        if ('function' != typeof e) throw new i('`fn` is not a function');
        var r = arguments.length > 2 && !!arguments[2];
        return (!r || a) && (o ? n(e, 'name', t, !0, !0) : n(e, 'name', t)), e;
      };
    },
    87062: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype']],
        });
    },
    87077: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    87337: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [],
        });
    },
    88e3: (e, t, r) => {
      'use strict';
      var n = r(47600),
        o = n('Number.prototype.toString'),
        a = function (e) {
          try {
            return o(e), !0;
          } catch (e) {
            return !1;
          }
        },
        i = n('Object.prototype.toString'),
        l = r(79056)();
      e.exports = function (e) {
        return (
          'number' == typeof e ||
          (!!e && 'object' == typeof e && (l ? a(e) : '[object Number]' === i(e)))
        );
      };
    },
    88089: (e, t, r) => {
      'use strict';
      var n = r(26203),
        o = 'undefined' == typeof globalThis ? r.g : globalThis;
      e.exports = function () {
        for (var e = [], t = 0; t < n.length; t++)
          'function' == typeof o[n[t]] && (e[e.length] = n[t]);
        return e;
      };
    },
    88221: e => {
      'use strict';
      e.exports = Math.min;
    },
    89005: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    89206: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { attributes: [{ constraints: ['set'], name: 'alt' }], name: 'img' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ constraints: ['undefined'], name: 'alt' }], name: 'img' },
              module: 'HTML',
            },
            { concept: { name: 'imggroup' }, module: 'DTB' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    90043: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'foreword [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    92123: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.test = t.serialize = t.default = void 0);
      var n = (function (e, t) {
          if (e && e.__esModule) return e;
          if (null === e || ('object' != typeof e && 'function' != typeof e)) return { default: e };
          var r = a(t);
          if (r && r.has(e)) return r.get(e);
          var n = {},
            o = Object.defineProperty && Object.getOwnPropertyDescriptor;
          for (var i in e)
            if ('default' !== i && Object.prototype.hasOwnProperty.call(e, i)) {
              var l = o ? Object.getOwnPropertyDescriptor(e, i) : null;
              l && (l.get || l.set) ? Object.defineProperty(n, i, l) : (n[i] = e[i]);
            }
          return (n.default = e), r && r.set(e, n), n;
        })(r(21133)),
        o = r(70302);
      function a(e) {
        if ('function' != typeof WeakMap) return null;
        var t = new WeakMap(),
          r = new WeakMap();
        return (a = function (e) {
          return e ? r : t;
        })(e);
      }
      let i = (e, t = []) => (
          Array.isArray(e)
            ? e.forEach(e => {
                i(e, t);
              })
            : null != e && !1 !== e && t.push(e),
          t
        ),
        l = e => {
          let t = e.type;
          if ('string' == typeof t) return t;
          if ('function' == typeof t) return t.displayName || t.name || 'Unknown';
          if (n.isFragment(e)) return 'React.Fragment';
          if (n.isSuspense(e)) return 'React.Suspense';
          if ('object' == typeof t && null !== t) {
            if (n.isContextProvider(e)) return 'Context.Provider';
            if (n.isContextConsumer(e)) return 'Context.Consumer';
            if (n.isForwardRef(e)) {
              if (t.displayName) return t.displayName;
              let e = t.render.displayName || t.render.name || '';
              return '' !== e ? 'ForwardRef(' + e + ')' : 'ForwardRef';
            }
            if (n.isMemo(e)) {
              let e = t.displayName || t.type.displayName || t.type.name || '';
              return '' !== e ? 'Memo(' + e + ')' : 'Memo';
            }
          }
          return 'UNDEFINED';
        },
        u = e => {
          let { props: t } = e;
          return Object.keys(t)
            .filter(e => 'children' !== e && void 0 !== t[e])
            .sort();
        },
        s = (e, t, r, n, a, s) =>
          ++n > t.maxDepth
            ? (0, o.printElementAsLeaf)(l(e), t)
            : (0, o.printElement)(
                l(e),
                (0, o.printProps)(u(e), e.props, t, r + t.indent, n, a, s),
                (0, o.printChildren)(i(e.props.children), t, r + t.indent, n, a, s),
                t,
                r
              );
      t.serialize = s;
      let c = e => null != e && n.isElement(e);
      (t.test = c), (t.default = { serialize: s, test: c });
    },
    92356: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { constraints: ['direct descendant of document'], name: 'footer' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    92914: e => {
      'use strict';
      e.exports = Function.prototype.call;
    },
    93025: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'nav' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    93632: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-sort': null },
          relatedConcepts: [
            {
              attributes: [{ name: 'scope', value: 'col' }],
              concept: { name: 'th' },
              module: 'HTML',
            },
          ],
          requireContextRole: ['row'],
          requiredContextRole: ['row'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'cell'],
            ['roletype', 'structure', 'section', 'cell', 'gridcell'],
            ['roletype', 'widget', 'gridcell'],
            ['roletype', 'structure', 'sectionhead'],
          ],
        });
    },
    95006: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = eA(r(7871)),
        o = eA(r(56843)),
        a = eA(r(99035)),
        i = eA(r(54815)),
        l = eA(r(6653)),
        u = eA(r(95696)),
        s = eA(r(66447)),
        c = eA(r(63971)),
        d = eA(r(61003)),
        p = eA(r(25082)),
        f = eA(r(19334)),
        b = eA(r(93632)),
        m = eA(r(72800)),
        y = eA(r(16415)),
        v = eA(r(92356)),
        h = eA(r(10842)),
        g = eA(r(43341)),
        P = eA(r(98431)),
        C = eA(r(76802)),
        q = eA(r(61014)),
        x = eA(r(89005)),
        E = eA(r(77379)),
        w = eA(r(74495)),
        O = eA(r(59829)),
        R = eA(r(46236)),
        j = eA(r(29703)),
        S = eA(r(76055)),
        _ = eA(r(13398)),
        A = eA(r(27099)),
        M = eA(r(89206)),
        T = eA(r(23680)),
        I = eA(r(16751)),
        k = eA(r(47799)),
        N = eA(r(74880)),
        F = eA(r(68268)),
        B = eA(r(72231)),
        L = eA(r(79930)),
        U = eA(r(60801)),
        D = eA(r(65829)),
        H = eA(r(2116)),
        $ = eA(r(7281)),
        V = eA(r(63535)),
        W = eA(r(1318)),
        z = eA(r(37850)),
        G = eA(r(71876)),
        J = eA(r(93025)),
        X = eA(r(87337)),
        K = eA(r(25579)),
        Y = eA(r(84446)),
        Z = eA(r(30831)),
        Q = eA(r(57767)),
        ee = eA(r(46805)),
        et = eA(r(4278)),
        er = eA(r(1605)),
        en = eA(r(63371)),
        eo = eA(r(39233)),
        ea = eA(r(17236)),
        ei = eA(r(25220)),
        el = eA(r(71807)),
        eu = eA(r(95399)),
        es = eA(r(4528)),
        ec = eA(r(83846)),
        ed = eA(r(62366)),
        ep = eA(r(46139)),
        ef = eA(r(42813)),
        eb = eA(r(13172)),
        em = eA(r(29452)),
        ey = eA(r(87077)),
        ev = eA(r(35127)),
        eh = eA(r(1206)),
        eg = eA(r(28933)),
        eP = eA(r(46798)),
        eC = eA(r(30084)),
        eq = eA(r(63253)),
        ex = eA(r(40689)),
        eE = eA(r(44150)),
        ew = eA(r(96732)),
        eO = eA(r(52594)),
        eR = eA(r(72498)),
        ej = eA(r(13643)),
        eS = eA(r(58687)),
        e_ = eA(r(62296));
      function eA(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['alert', n.default],
        ['alertdialog', o.default],
        ['application', a.default],
        ['article', i.default],
        ['banner', l.default],
        ['blockquote', u.default],
        ['button', s.default],
        ['caption', c.default],
        ['cell', d.default],
        ['checkbox', p.default],
        ['code', f.default],
        ['columnheader', b.default],
        ['combobox', m.default],
        ['complementary', y.default],
        ['contentinfo', v.default],
        ['definition', h.default],
        ['deletion', g.default],
        ['dialog', P.default],
        ['directory', C.default],
        ['document', q.default],
        ['emphasis', x.default],
        ['feed', E.default],
        ['figure', w.default],
        ['form', O.default],
        ['generic', R.default],
        ['grid', j.default],
        ['gridcell', S.default],
        ['group', _.default],
        ['heading', A.default],
        ['img', M.default],
        ['insertion', T.default],
        ['link', I.default],
        ['list', k.default],
        ['listbox', N.default],
        ['listitem', F.default],
        ['log', B.default],
        ['main', L.default],
        ['marquee', U.default],
        ['math', D.default],
        ['menu', H.default],
        ['menubar', $.default],
        ['menuitem', V.default],
        ['menuitemcheckbox', W.default],
        ['menuitemradio', z.default],
        ['meter', G.default],
        ['navigation', J.default],
        ['none', X.default],
        ['note', K.default],
        ['option', Y.default],
        ['paragraph', Z.default],
        ['presentation', Q.default],
        ['progressbar', ee.default],
        ['radio', et.default],
        ['radiogroup', er.default],
        ['region', en.default],
        ['row', eo.default],
        ['rowgroup', ea.default],
        ['rowheader', ei.default],
        ['scrollbar', el.default],
        ['search', eu.default],
        ['searchbox', es.default],
        ['separator', ec.default],
        ['slider', ed.default],
        ['spinbutton', ep.default],
        ['status', ef.default],
        ['strong', eb.default],
        ['subscript', em.default],
        ['superscript', ey.default],
        ['switch', ev.default],
        ['tab', eh.default],
        ['table', eg.default],
        ['tablist', eP.default],
        ['tabpanel', eC.default],
        ['term', eq.default],
        ['textbox', ex.default],
        ['time', eE.default],
        ['timer', ew.default],
        ['toolbar', eO.default],
        ['tooltip', eR.default],
        ['tree', ej.default],
        ['treegrid', eS.default],
        ['treeitem', e_.default],
      ];
    },
    95024: e => {
      'use strict';
      e.exports =
        Number.isNaN ||
        function (e) {
          return e != e;
        };
    },
    95399: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    95490: (e, t, r) => {
      var n,
        o = (function () {
          var e = String.fromCharCode,
            t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
            r = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$',
            n = {};
          function o(e, t) {
            if (!n[e]) {
              n[e] = {};
              for (var r = 0; r < e.length; r++) n[e][e.charAt(r)] = r;
            }
            return n[e][t];
          }
          var a = {
            compressToBase64: function (e) {
              if (null == e) return '';
              var r = a._compress(e, 6, function (e) {
                return t.charAt(e);
              });
              switch (r.length % 4) {
                default:
                case 0:
                  return r;
                case 1:
                  return r + '===';
                case 2:
                  return r + '==';
                case 3:
                  return r + '=';
              }
            },
            decompressFromBase64: function (e) {
              return null == e
                ? ''
                : '' == e
                  ? null
                  : a._decompress(e.length, 32, function (r) {
                      return o(t, e.charAt(r));
                    });
            },
            compressToUTF16: function (t) {
              return null == t
                ? ''
                : a._compress(t, 15, function (t) {
                    return e(t + 32);
                  }) + ' ';
            },
            decompressFromUTF16: function (e) {
              return null == e
                ? ''
                : '' == e
                  ? null
                  : a._decompress(e.length, 16384, function (t) {
                      return e.charCodeAt(t) - 32;
                    });
            },
            compressToUint8Array: function (e) {
              for (
                var t = a.compress(e), r = new Uint8Array(2 * t.length), n = 0, o = t.length;
                n < o;
                n++
              ) {
                var i = t.charCodeAt(n);
                (r[2 * n] = i >>> 8), (r[2 * n + 1] = i % 256);
              }
              return r;
            },
            decompressFromUint8Array: function (t) {
              if (null == t) return a.decompress(t);
              for (var r = Array(t.length / 2), n = 0, o = r.length; n < o; n++)
                r[n] = 256 * t[2 * n] + t[2 * n + 1];
              var i = [];
              return (
                r.forEach(function (t) {
                  i.push(e(t));
                }),
                a.decompress(i.join(''))
              );
            },
            compressToEncodedURIComponent: function (e) {
              return null == e
                ? ''
                : a._compress(e, 6, function (e) {
                    return r.charAt(e);
                  });
            },
            decompressFromEncodedURIComponent: function (e) {
              return null == e
                ? ''
                : '' == e
                  ? null
                  : ((e = e.replace(/ /g, '+')),
                    a._decompress(e.length, 32, function (t) {
                      return o(r, e.charAt(t));
                    }));
            },
            compress: function (t) {
              return a._compress(t, 16, function (t) {
                return e(t);
              });
            },
            _compress: function (e, t, r) {
              if (null == e) return '';
              var n,
                o,
                a,
                i = {},
                l = {},
                u = '',
                s = '',
                c = '',
                d = 2,
                p = 3,
                f = 2,
                b = [],
                m = 0,
                y = 0;
              for (a = 0; a < e.length; a += 1)
                if (
                  ((u = e.charAt(a)),
                  Object.prototype.hasOwnProperty.call(i, u) || ((i[u] = p++), (l[u] = !0)),
                  (s = c + u),
                  Object.prototype.hasOwnProperty.call(i, s))
                )
                  c = s;
                else {
                  if (Object.prototype.hasOwnProperty.call(l, c)) {
                    if (256 > c.charCodeAt(0)) {
                      for (n = 0; n < f; n++)
                        (m <<= 1), y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++;
                      for (n = 0, o = c.charCodeAt(0); n < 8; n++)
                        (m = (m << 1) | (1 & o)),
                          y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                          (o >>= 1);
                    } else {
                      for (n = 0, o = 1; n < f; n++)
                        (m = (m << 1) | o),
                          y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                          (o = 0);
                      for (n = 0, o = c.charCodeAt(0); n < 16; n++)
                        (m = (m << 1) | (1 & o)),
                          y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                          (o >>= 1);
                    }
                    0 == --d && ((d = Math.pow(2, f)), f++), delete l[c];
                  } else
                    for (n = 0, o = i[c]; n < f; n++)
                      (m = (m << 1) | (1 & o)),
                        y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                        (o >>= 1);
                  0 == --d && ((d = Math.pow(2, f)), f++), (i[s] = p++), (c = String(u));
                }
              if ('' !== c) {
                if (Object.prototype.hasOwnProperty.call(l, c)) {
                  if (256 > c.charCodeAt(0)) {
                    for (n = 0; n < f; n++)
                      (m <<= 1), y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++;
                    for (n = 0, o = c.charCodeAt(0); n < 8; n++)
                      (m = (m << 1) | (1 & o)),
                        y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                        (o >>= 1);
                  } else {
                    for (n = 0, o = 1; n < f; n++)
                      (m = (m << 1) | o),
                        y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                        (o = 0);
                    for (n = 0, o = c.charCodeAt(0); n < 16; n++)
                      (m = (m << 1) | (1 & o)),
                        y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                        (o >>= 1);
                  }
                  0 == --d && ((d = Math.pow(2, f)), f++), delete l[c];
                } else
                  for (n = 0, o = i[c]; n < f; n++)
                    (m = (m << 1) | (1 & o)),
                      y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                      (o >>= 1);
                0 == --d && ((d = Math.pow(2, f)), f++);
              }
              for (n = 0, o = 2; n < f; n++)
                (m = (m << 1) | (1 & o)),
                  y == t - 1 ? ((y = 0), b.push(r(m)), (m = 0)) : y++,
                  (o >>= 1);
              for (;;) {
                if (((m <<= 1), y == t - 1)) {
                  b.push(r(m));
                  break;
                }
                y++;
              }
              return b.join('');
            },
            decompress: function (e) {
              return null == e
                ? ''
                : '' == e
                  ? null
                  : a._decompress(e.length, 32768, function (t) {
                      return e.charCodeAt(t);
                    });
            },
            _decompress: function (t, r, n) {
              var o,
                a,
                i,
                l,
                u,
                s,
                c,
                d = [],
                p = 4,
                f = 4,
                b = 3,
                m = '',
                y = [],
                v = { val: n(0), position: r, index: 1 };
              for (o = 0; o < 3; o += 1) d[o] = o;
              for (i = 0, u = 4, s = 1; s != u; )
                (l = v.val & v.position),
                  (v.position >>= 1),
                  0 == v.position && ((v.position = r), (v.val = n(v.index++))),
                  (i |= (l > 0) * s),
                  (s <<= 1);
              switch (i) {
                case 0:
                  for (i = 0, u = 256, s = 1; s != u; )
                    (l = v.val & v.position),
                      (v.position >>= 1),
                      0 == v.position && ((v.position = r), (v.val = n(v.index++))),
                      (i |= (l > 0) * s),
                      (s <<= 1);
                  c = e(i);
                  break;
                case 1:
                  for (i = 0, u = 65536, s = 1; s != u; )
                    (l = v.val & v.position),
                      (v.position >>= 1),
                      0 == v.position && ((v.position = r), (v.val = n(v.index++))),
                      (i |= (l > 0) * s),
                      (s <<= 1);
                  c = e(i);
                  break;
                case 2:
                  return '';
              }
              for (d[3] = c, a = c, y.push(c); ; ) {
                if (v.index > t) return '';
                for (i = 0, u = Math.pow(2, b), s = 1; s != u; )
                  (l = v.val & v.position),
                    (v.position >>= 1),
                    0 == v.position && ((v.position = r), (v.val = n(v.index++))),
                    (i |= (l > 0) * s),
                    (s <<= 1);
                switch ((c = i)) {
                  case 0:
                    for (i = 0, u = 256, s = 1; s != u; )
                      (l = v.val & v.position),
                        (v.position >>= 1),
                        0 == v.position && ((v.position = r), (v.val = n(v.index++))),
                        (i |= (l > 0) * s),
                        (s <<= 1);
                    (d[f++] = e(i)), (c = f - 1), p--;
                    break;
                  case 1:
                    for (i = 0, u = 65536, s = 1; s != u; )
                      (l = v.val & v.position),
                        (v.position >>= 1),
                        0 == v.position && ((v.position = r), (v.val = n(v.index++))),
                        (i |= (l > 0) * s),
                        (s <<= 1);
                    (d[f++] = e(i)), (c = f - 1), p--;
                    break;
                  case 2:
                    return y.join('');
                }
                if ((0 == p && ((p = Math.pow(2, b)), b++), d[c])) m = d[c];
                else {
                  if (c !== f) return null;
                  m = a + a.charAt(0);
                }
                y.push(m),
                  (d[f++] = a + m.charAt(0)),
                  p--,
                  (a = m),
                  0 == p && ((p = Math.pow(2, b)), b++);
              }
            },
          };
          return a;
        })();
      void 0 ===
        (n = function () {
          return o;
        }.call(t, r, t, e)) || (e.exports = n);
    },
    95696: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    96529: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'subtitle [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'sectionhead']],
        });
    },
    96732: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'status']],
        });
    },
    97783: e => {
      'use strict';
      var t = Object.defineProperty || !1;
      if (t)
        try {
          t({}, 'a', { value: 1 });
        } catch (e) {
          t = !1;
        }
      e.exports = t;
    },
    98431: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'dialog' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'window']],
        });
    },
    99035: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-activedescendant': null,
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'Device Independence Delivery Unit' } }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    99602: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = H(r(22811)),
        o = H(r(24645)),
        a = H(r(70345)),
        i = H(r(44168)),
        l = H(r(83874)),
        u = H(r(27174)),
        s = H(r(82957)),
        c = H(r(12459)),
        d = H(r(39754)),
        p = H(r(48191)),
        f = H(r(73121)),
        b = H(r(38304)),
        m = H(r(57632)),
        y = H(r(81733)),
        v = H(r(60611)),
        h = H(r(35794)),
        g = H(r(84759)),
        P = H(r(54305)),
        C = H(r(83629)),
        q = H(r(82084)),
        x = H(r(21405)),
        E = H(r(23461)),
        w = H(r(90043)),
        O = H(r(59913)),
        R = H(r(67194)),
        j = H(r(64363)),
        S = H(r(78657)),
        _ = H(r(16866)),
        A = H(r(2517)),
        M = H(r(585)),
        T = H(r(79566)),
        I = H(r(22364)),
        k = H(r(32519)),
        N = H(r(25308)),
        F = H(r(41190)),
        B = H(r(76821)),
        L = H(r(96529)),
        U = H(r(74706)),
        D = H(r(63689));
      function H(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['doc-abstract', n.default],
        ['doc-acknowledgments', o.default],
        ['doc-afterword', a.default],
        ['doc-appendix', i.default],
        ['doc-backlink', l.default],
        ['doc-biblioentry', u.default],
        ['doc-bibliography', s.default],
        ['doc-biblioref', c.default],
        ['doc-chapter', d.default],
        ['doc-colophon', p.default],
        ['doc-conclusion', f.default],
        ['doc-cover', b.default],
        ['doc-credit', m.default],
        ['doc-credits', y.default],
        ['doc-dedication', v.default],
        ['doc-endnote', h.default],
        ['doc-endnotes', g.default],
        ['doc-epigraph', P.default],
        ['doc-epilogue', C.default],
        ['doc-errata', q.default],
        ['doc-example', x.default],
        ['doc-footnote', E.default],
        ['doc-foreword', w.default],
        ['doc-glossary', O.default],
        ['doc-glossref', R.default],
        ['doc-index', j.default],
        ['doc-introduction', S.default],
        ['doc-noteref', _.default],
        ['doc-notice', A.default],
        ['doc-pagebreak', M.default],
        ['doc-pagelist', T.default],
        ['doc-part', I.default],
        ['doc-preface', k.default],
        ['doc-prologue', N.default],
        ['doc-pullquote', F.default],
        ['doc-qna', B.default],
        ['doc-subtitle', L.default],
        ['doc-tip', U.default],
        ['doc-toc', D.default],
      ];
    },
  },
]);
