(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [615],
  {
    1621: (e, t, r) => {
      'use strict';
      r.r(t), r.d(t, { default: () => n });
      var a = r(23798),
        s = r(21462),
        o = r(5879);
      let n = () => {
        let { login: e } = (0, o.As)(),
          [t, r] = (0, s.useState)(''),
          [n, l] = (0, s.useState)(''),
          [c, i] = (0, s.useState)(!1),
          u = async r => {
            if ((r.preventDefault(), t.trim() && n.trim())) {
              i(!0);
              try {
                await e(t, n);
              } catch (e) {
                console.error('Login failed:', e);
              } finally {
                i(!1);
              }
            }
          };
        return (0, a.jsxs)('div', {
          className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
          children: [
            (0, a.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '登入' }),
            (0, a.jsx)('form', {
              onSubmit: u,
              children: (0, a.jsxs)('div', {
                className: 'space-y-4',
                children: [
                  (0, a.jsx)('input', {
                    type: 'text',
                    value: t,
                    onChange: e => r(e.target.value),
                    placeholder: '使用者名稱',
                    className: 'w-full p-2 border rounded',
                  }),
                  (0, a.jsx)('input', {
                    type: 'password',
                    value: n,
                    onChange: e => l(e.target.value),
                    placeholder: '密碼',
                    className: 'w-full p-2 border rounded',
                  }),
                  (0, a.jsx)('button', {
                    type: 'submit',
                    disabled: c,
                    className:
                      'w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300',
                    children: c ? '登入中...' : '登入',
                  }),
                ],
              }),
            }),
          ],
        });
      };
    },
    5879: (e, t, r) => {
      'use strict';
      r.d(t, { As: () => o }), r(23798);
      var a = r(21462);
      r(50730);
      let s = (0, a.createContext)(null),
        o = () => {
          let e = (0, a.useContext)(s);
          if (!e) throw Error('useAuth must be used within an AuthProvider');
          return e;
        };
    },
    50730: (e, t, r) => {
      'use strict';
      r.d(t, { Cz: () => u, Zc: () => i, ef: () => c, xj: () => l });
      var a = r(56958);
      let s = r(77051).env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
        o = a.A.create({ baseURL: s, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
        n = { log: e => {}, error: (e, t) => {} };
      o.interceptors.request.use(
        e => (n.log('Making request to: '.concat(e.baseURL).concat(e.url)), e),
        e => (n.error('Request error:', e), Promise.reject(e))
      ),
        o.interceptors.response.use(
          e => e,
          async e => {
            let { config: t } = e;
            return e.message.includes('Network Error') && t.retry > 0
              ? ((t.retry -= 1),
                n.log('Retrying request to '.concat(t.url, ', ').concat(t.retry, ' attempts left')),
                await new Promise(e => setTimeout(e, t.retryDelay)),
                o(t))
              : Promise.reject(e);
          }
        );
      let l = async function () {
          let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10;
          try {
            return (await o.get('/novels/', { params: { page: e, limit: t } })).data;
          } catch (e) {
            return (
              n.error('Error fetching novel list:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        },
        c = async e => {
          try {
            return (await o.get('/novels/'.concat(e, '/'))).data;
          } catch (e) {
            throw (n.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
          }
        },
        i = async e => {
          try {
            return (await o.get('/chapters/'.concat(e, '/'))).data;
          } catch (t) {
            throw (
              (n.error('Error fetching chapter content for chapterId '.concat(e, ':'), t),
              Error('無法載入章節內容'))
            );
          }
        },
        u = async e => {
          try {
            return (await o.get('/novels/search', { params: { query: e } })).data;
          } catch (e) {
            return (
              n.error('Error searching novels:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        };
    },
    72211: (e, t, r) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/Login',
        function () {
          return r(1621);
        },
      ]);
    },
  },
  e => {
    var t = t => e((e.s = t));
    e.O(0, [958, 636, 593, 792], () => t(72211)), (_N_E = e.O());
  },
]);
