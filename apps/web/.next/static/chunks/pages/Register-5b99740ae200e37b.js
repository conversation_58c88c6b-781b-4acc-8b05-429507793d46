(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [347],
  {
    50617: (e, l, s) => {
      'use strict';
      s.r(l), s.d(l, { default: () => t });
      var r = s(23798),
        a = s(21462);
      let t = e => {
        let { onRegister: l } = e,
          [s, t] = (0, a.useState)(''),
          [n, o] = (0, a.useState)(''),
          [d, u] = (0, a.useState)(''),
          [i, c] = (0, a.useState)(!1),
          m = async e => {
            if ((e.preventDefault(), !(d.length < 6) && l && s && n && d)) {
              c(!0);
              try {
                await l(s, n, d);
              } catch (e) {
                console.error('Registration failed:', e);
              } finally {
                c(!1);
              }
            }
          };
        return (0, r.jsxs)('div', {
          className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
          children: [
            (0, r.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '註冊' }),
            (0, r.jsxs)('form', {
              onSubmit: m,
              className: 'space-y-4',
              children: [
                (0, r.jsxs)('div', {
                  children: [
                    (0, r.jsx)('label', {
                      htmlFor: 'username',
                      className: 'block text-sm font-medium text-gray-700 mb-1',
                      children: '用戶名',
                    }),
                    (0, r.jsx)('input', {
                      type: 'text',
                      id: 'username',
                      value: s,
                      onChange: e => t(e.target.value),
                      required: !0,
                      className:
                        'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                      placeholder: '請輸入用戶名',
                    }),
                  ],
                }),
                (0, r.jsxs)('div', {
                  children: [
                    (0, r.jsx)('label', {
                      htmlFor: 'email',
                      className: 'block text-sm font-medium text-gray-700 mb-1',
                      children: '電子郵件',
                    }),
                    (0, r.jsx)('input', {
                      type: 'email',
                      id: 'email',
                      value: n,
                      onChange: e => o(e.target.value),
                      required: !0,
                      className:
                        'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                      placeholder: '請輸入電子郵件',
                    }),
                  ],
                }),
                (0, r.jsxs)('div', {
                  children: [
                    (0, r.jsx)('label', {
                      htmlFor: 'password',
                      className: 'block text-sm font-medium text-gray-700 mb-1',
                      children: '密碼',
                    }),
                    (0, r.jsx)('input', {
                      type: 'password',
                      id: 'password',
                      value: d,
                      onChange: e => u(e.target.value),
                      required: !0,
                      minLength: 6,
                      className:
                        'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                      placeholder: '請輸入密碼（至少6位）',
                    }),
                  ],
                }),
                (0, r.jsx)('button', {
                  type: 'submit',
                  disabled: i || !s || !n || !d,
                  className:
                    'w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors',
                  children: i ? '註冊中...' : '註冊',
                }),
              ],
            }),
          ],
        });
      };
    },
    63741: (e, l, s) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/Register',
        function () {
          return s(50617);
        },
      ]);
    },
  },
  e => {
    var l = l => e((e.s = l));
    e.O(0, [636, 593, 792], () => l(63741)), (_N_E = e.O());
  },
]);
