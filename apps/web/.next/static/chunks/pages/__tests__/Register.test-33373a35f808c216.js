(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [469],
  {
    22179: () => {},
    37919: (e, t, a) => {
      'use strict';
      a.r(t);
      var s = a(23798),
        n = a(21462),
        l = a(56733),
        i = a(48940),
        o = a(50617);
      let r = jest.fn(() => Promise.resolve());
      describe('<Register />', () => {
        beforeEach(() => {
          jest.clearAllMocks();
        }),
          it('renders initial state with disabled submit', () => {
            (0, l.XX)((0, s.jsx)(o.default, { onRegister: r })),
              expect(l.nj.getByLabelText('用戶名')).toHaveValue(''),
              expect(l.nj.getByLabelText('電子郵件')).toHaveValue(''),
              expect(l.nj.getByLabelText('密碼')).toHaveValue(''),
              expect(l.nj.getByRole('button', { name: '註冊' })).toBeDisabled();
          }),
          it('shows native validation messages for invalid email', async () => {
            (0, l.XX)((0, s.jsx)(o.default, { onRegister: r }));
            let e = l.nj.getByLabelText('電子郵件');
            await (0, n.act)(async () => {
              await i.Ay.type(e, 'invalid'), await i.Ay.tab();
            }),
              expect(e).toBeInvalid();
          }),
          it('shows validation messages when submitting invalid form', async () => {
            (0, l.XX)((0, s.jsx)(o.default, { onRegister: r }));
            let e = l.nj.getByRole('button', { name: '註冊' }),
              t = l.nj.getByLabelText('電子郵件');
            l.rC.click(e),
              expect(t.validationMessage).not.toBe(''),
              expect(r).not.toHaveBeenCalled();
          }),
          it('calls onRegister with form values on success', async () => {
            await (0, n.act)(async () => {
              (0, l.XX)((0, s.jsx)(o.default, { onRegister: r }));
            }),
              await (0, n.act)(async () => {
                await i.Ay.type(l.nj.getByLabelText('用戶名'), 'tester'),
                  await i.Ay.type(l.nj.getByLabelText('電子郵件'), '<EMAIL>'),
                  await i.Ay.type(l.nj.getByLabelText('密碼'), '123456');
              });
            let e = l.nj.getByRole('button', { name: '註冊' });
            expect(e).toBeEnabled(),
              await (0, n.act)(async () => {
                await i.Ay.click(e);
              }),
              expect(r).toHaveBeenCalledWith('tester', '<EMAIL>', '123456');
          }),
          it('sets password minimum length requirement', () => {
            (0, l.XX)((0, s.jsx)(o.default, { onRegister: r })),
              expect(l.nj.getByLabelText('密碼')).toHaveAttribute('minLength', '6');
          }),
          it('prevents submission with too short password', async () => {
            (0, l.XX)((0, s.jsx)(o.default, { onRegister: r })),
              await (0, n.act)(async () => {
                await i.Ay.type(l.nj.getByLabelText('用戶名'), 'tester'),
                  await i.Ay.type(l.nj.getByLabelText('電子郵件'), '<EMAIL>');
                let e = l.nj.getByLabelText('密碼');
                await i.Ay.type(e, '123'), e.setCustomValidity('too short'), await i.Ay.tab();
              }),
              expect(l.nj.getByLabelText('密碼')).toBeInvalid(),
              await (0, n.act)(async () => {
                await i.Ay.click(l.nj.getByRole('button', { name: '註冊' }));
              }),
              expect(r).not.toHaveBeenCalled();
          }),
          it('shows loading state during registration attempt and button text changes', async () => {
            let e = () => {},
              t = jest.fn(
                () =>
                  new Promise(t => {
                    e = t;
                  })
              );
            await (0, n.act)(async () => {
              (0, l.XX)((0, s.jsx)(o.default, { onRegister: t }));
            }),
              await (0, n.act)(async () => {
                await i.Ay.type(l.nj.getByLabelText('用戶名'), 'tester'),
                  await i.Ay.type(l.nj.getByLabelText('電子郵件'), '<EMAIL>'),
                  await i.Ay.type(l.nj.getByLabelText('密碼'), '123456');
              });
            let a = l.nj.getByRole('button', { name: '註冊' });
            expect(a).toBeEnabled(),
              await (0, n.act)(async () => {
                await i.Ay.click(a);
              }),
              expect(t).toHaveBeenCalledTimes(1),
              expect(a).toBeDisabled(),
              expect(a).toHaveTextContent('註冊中...'),
              await (0, n.act)(async () => {
                e();
              }),
              await l.nj.findByRole('button', { name: '註冊' });
          }),
          it('re-enables submit button after registration error', async () => {
            let e = jest.fn(() => Promise.reject(Error('fail'))),
              t = jest.spyOn(console, 'error').mockImplementation(() => {});
            await (0, n.act)(async () => {
              (0, l.XX)((0, s.jsx)(o.default, { onRegister: e }));
            }),
              await (0, n.act)(async () => {
                await i.Ay.type(l.nj.getByLabelText('用戶名'), 'tester'),
                  await i.Ay.type(l.nj.getByLabelText('電子郵件'), '<EMAIL>'),
                  await i.Ay.type(l.nj.getByLabelText('密碼'), '123456');
              }),
              await (0, n.act)(async () => {
                await i.Ay.click(l.nj.getByRole('button', { name: '註冊' }));
              }),
              await (0, n.act)(async () => {
                await l.nj.findByRole('button', { name: '註冊' });
              }),
              expect(l.nj.getByRole('button', { name: '註冊' })).toBeEnabled(),
              expect(t).toHaveBeenCalled(),
              t.mockRestore();
          });
      });
    },
    50617: (e, t, a) => {
      'use strict';
      a.r(t), a.d(t, { default: () => l });
      var s = a(23798),
        n = a(21462);
      let l = e => {
        let { onRegister: t } = e,
          [a, l] = (0, n.useState)(''),
          [i, o] = (0, n.useState)(''),
          [r, c] = (0, n.useState)(''),
          [d, y] = (0, n.useState)(!1),
          u = async e => {
            if ((e.preventDefault(), !(r.length < 6) && t && a && i && r)) {
              y(!0);
              try {
                await t(a, i, r);
              } catch (e) {
                console.error('Registration failed:', e);
              } finally {
                y(!1);
              }
            }
          };
        return (0, s.jsxs)('div', {
          className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
          children: [
            (0, s.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '註冊' }),
            (0, s.jsxs)('form', {
              onSubmit: u,
              className: 'space-y-4',
              children: [
                (0, s.jsxs)('div', {
                  children: [
                    (0, s.jsx)('label', {
                      htmlFor: 'username',
                      className: 'block text-sm font-medium text-gray-700 mb-1',
                      children: '用戶名',
                    }),
                    (0, s.jsx)('input', {
                      type: 'text',
                      id: 'username',
                      value: a,
                      onChange: e => l(e.target.value),
                      required: !0,
                      className:
                        'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                      placeholder: '請輸入用戶名',
                    }),
                  ],
                }),
                (0, s.jsxs)('div', {
                  children: [
                    (0, s.jsx)('label', {
                      htmlFor: 'email',
                      className: 'block text-sm font-medium text-gray-700 mb-1',
                      children: '電子郵件',
                    }),
                    (0, s.jsx)('input', {
                      type: 'email',
                      id: 'email',
                      value: i,
                      onChange: e => o(e.target.value),
                      required: !0,
                      className:
                        'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                      placeholder: '請輸入電子郵件',
                    }),
                  ],
                }),
                (0, s.jsxs)('div', {
                  children: [
                    (0, s.jsx)('label', {
                      htmlFor: 'password',
                      className: 'block text-sm font-medium text-gray-700 mb-1',
                      children: '密碼',
                    }),
                    (0, s.jsx)('input', {
                      type: 'password',
                      id: 'password',
                      value: r,
                      onChange: e => c(e.target.value),
                      required: !0,
                      minLength: 6,
                      className:
                        'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                      placeholder: '請輸入密碼（至少6位）',
                    }),
                  ],
                }),
                (0, s.jsx)('button', {
                  type: 'submit',
                  disabled: d || !a || !i || !r,
                  className:
                    'w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors',
                  children: d ? '註冊中...' : '註冊',
                }),
              ],
            }),
          ],
        });
      };
    },
    80283: (e, t, a) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/__tests__/Register.test',
        function () {
          return a(37919);
        },
      ]);
    },
  },
  e => {
    var t = t => e((e.s = t));
    e.O(0, [593, 733, 940, 636, 792], () => t(80283)), (_N_E = e.O());
  },
]);
