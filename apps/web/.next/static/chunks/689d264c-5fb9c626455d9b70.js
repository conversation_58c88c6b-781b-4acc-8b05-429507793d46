'use strict';
(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [893],
  {
    55481: (e, t, r) => {
      var n, a;
      function o() {
        return (o = Object.assign
          ? Object.assign.bind()
          : function (e) {
              for (var t = 1; t < arguments.length; t++) {
                var r = arguments[t];
                for (var n in r) Object.prototype.hasOwnProperty.call(r, n) && (e[n] = r[n]);
              }
              return e;
            }).apply(this, arguments);
      }
      r.d(t, {
        AO: () => c,
        Gh: () => P,
        HS: () => S,
        Oi: () => s,
        Rr: () => p,
        pb: () => v,
        rc: () => n,
        sC: () => l,
        tH: () => $,
        yD: () => R,
      }),
        (function (e) {
          (e.Pop = 'POP'), (e.Push = 'PUSH'), (e.Replace = 'REPLACE');
        })(n || (n = {}));
      let i = 'popstate';
      function l(e) {
        let t;
        void 0 === e && (e = {});
        let { initialEntries: r = ['/'], initialIndex: a, v5Compat: o = !1 } = e;
        t = r.map((e, t) =>
          f(e, 'string' == typeof e ? null : e.state, 0 === t ? 'default' : void 0)
        );
        let i = h(null == a ? t.length - 1 : a),
          l = n.Pop,
          s = null;
        function h(e) {
          return Math.min(Math.max(e, 0), t.length - 1);
        }
        function f(e, r, n) {
          void 0 === r && (r = null);
          let a = d(t ? t[i].pathname : '/', e, r, n);
          return (
            u(
              '/' === a.pathname.charAt(0),
              'relative pathnames are not supported in memory history: ' + JSON.stringify(e)
            ),
            a
          );
        }
        function y(e) {
          return 'string' == typeof e ? e : c(e);
        }
        return {
          get index() {
            return i;
          },
          get action() {
            return l;
          },
          get location() {
            return t[i];
          },
          createHref: y,
          createURL: e => new URL(y(e), 'http://localhost'),
          encodeLocation(e) {
            let t = 'string' == typeof e ? p(e) : e;
            return { pathname: t.pathname || '', search: t.search || '', hash: t.hash || '' };
          },
          push(e, r) {
            l = n.Push;
            let a = f(e, r);
            (i += 1), t.splice(i, t.length, a), o && s && s({ action: l, location: a, delta: 1 });
          },
          replace(e, r) {
            l = n.Replace;
            let a = f(e, r);
            (t[i] = a), o && s && s({ action: l, location: a, delta: 0 });
          },
          go(e) {
            l = n.Pop;
            let r = h(i + e),
              a = t[r];
            (i = r), s && s({ action: l, location: a, delta: e });
          },
          listen: e => (
            (s = e),
            () => {
              s = null;
            }
          ),
        };
      }
      function s(e, t) {
        if (!1 === e || null == e) throw Error(t);
      }
      function u(e, t) {
        if (!e) {
          'undefined' != typeof console && console.warn(t);
          try {
            throw Error(t);
          } catch (e) {}
        }
      }
      function h(e, t) {
        return { usr: e.state, key: e.key, idx: t };
      }
      function d(e, t, r, n) {
        return (
          void 0 === r && (r = null),
          o(
            { pathname: 'string' == typeof e ? e : e.pathname, search: '', hash: '' },
            'string' == typeof t ? p(t) : t,
            { state: r, key: (t && t.key) || n || Math.random().toString(36).substr(2, 8) }
          )
        );
      }
      function c(e) {
        let { pathname: t = '/', search: r = '', hash: n = '' } = e;
        return (
          r && '?' !== r && (t += '?' === r.charAt(0) ? r : '?' + r),
          n && '#' !== n && (t += '#' === n.charAt(0) ? n : '#' + n),
          t
        );
      }
      function p(e) {
        let t = {};
        if (e) {
          let r = e.indexOf('#');
          r >= 0 && ((t.hash = e.substr(r)), (e = e.substr(0, r)));
          let n = e.indexOf('?');
          n >= 0 && ((t.search = e.substr(n)), (e = e.substr(0, n))), e && (t.pathname = e);
        }
        return t;
      }
      !(function (e) {
        (e.data = 'data'),
          (e.deferred = 'deferred'),
          (e.redirect = 'redirect'),
          (e.error = 'error');
      })(a || (a = {}));
      let f = new Set(['lazy', 'caseSensitive', 'path', 'id', 'index', 'children']),
        y = /^:[\w-]+$/,
        m = e => '*' === e;
      function g(e, t) {
        var r, n, a;
        let o, i;
        'string' == typeof e && (e = { path: e, caseSensitive: !1, end: !0 });
        let [l, s] =
            ((r = e.path),
            (n = e.caseSensitive),
            (a = e.end),
            void 0 === n && (n = !1),
            void 0 === a && (a = !0),
            u(
              '*' === r || !r.endsWith('*') || r.endsWith('/*'),
              'Route path "' +
                r +
                '" will be treated as if it were "' +
                r.replace(/\*$/, '/*') +
                '" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "' +
                r.replace(/\*$/, '/*') +
                '".'
            ),
            (o = []),
            (i =
              '^' +
              r
                .replace(/\/*\*?$/, '')
                .replace(/^\/*/, '/')
                .replace(/[\\.*+^${}|()[\]]/g, '\\$&')
                .replace(
                  /\/:([\w-]+)(\?)?/g,
                  (e, t, r) => (
                    o.push({ paramName: t, isOptional: null != r }),
                    r ? '/?([^\\/]+)?' : '/([^\\/]+)'
                  )
                )),
            r.endsWith('*')
              ? (o.push({ paramName: '*' }),
                (i += '*' === r || '/*' === r ? '(.*)$' : '(?:\\/(.+)|\\/*)$'))
              : a
                ? (i += '\\/*$')
                : '' !== r && '/' !== r && (i += '(?:(?=\\/|$))'),
            [new RegExp(i, n ? void 0 : 'i'), o]),
          h = t.match(l);
        if (!h) return null;
        let d = h[0],
          c = d.replace(/(.)\/+$/, '$1'),
          p = h.slice(1);
        return {
          params: s.reduce((e, t, r) => {
            let { paramName: n, isOptional: a } = t;
            if ('*' === n) {
              let e = p[r] || '';
              c = d.slice(0, d.length - e.length).replace(/(.)\/+$/, '$1');
            }
            let o = p[r];
            return a && !o ? (e[n] = void 0) : (e[n] = (o || '').replace(/%2F/g, '/')), e;
          }, {}),
          pathname: d,
          pathnameBase: c,
          pattern: e,
        };
      }
      function v(e, t) {
        if ('/' === t) return e;
        if (!e.toLowerCase().startsWith(t.toLowerCase())) return null;
        let r = t.endsWith('/') ? t.length - 1 : t.length,
          n = e.charAt(r);
        return n && '/' !== n ? null : e.slice(r) || '/';
      }
      function w(e, t, r, n) {
        return (
          "Cannot include a '" +
          e +
          "' character in a manually specified " +
          ('`to.' + t + '` field [') +
          JSON.stringify(n) +
          '].  Please separate it out to the `to.' +
          r +
          '` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'
        );
      }
      function b(e) {
        return e.filter((e, t) => 0 === t || (e.route.path && e.route.path.length > 0));
      }
      function R(e, t) {
        let r = b(e);
        return t
          ? r.map((e, t) => (t === r.length - 1 ? e.pathname : e.pathnameBase))
          : r.map(e => e.pathnameBase);
      }
      function P(e, t, r, n) {
        let a, i;
        void 0 === n && (n = !1),
          'string' == typeof e
            ? (a = p(e))
            : (s(
                !(a = o({}, e)).pathname || !a.pathname.includes('?'),
                w('?', 'pathname', 'search', a)
              ),
              s(!a.pathname || !a.pathname.includes('#'), w('#', 'pathname', 'hash', a)),
              s(!a.search || !a.search.includes('#'), w('#', 'search', 'hash', a)));
        let l = '' === e || '' === a.pathname,
          u = l ? '/' : a.pathname;
        if (null == u) i = r;
        else {
          let e = t.length - 1;
          if (!n && u.startsWith('..')) {
            let t = u.split('/');
            for (; '..' === t[0]; ) t.shift(), (e -= 1);
            a.pathname = t.join('/');
          }
          i = e >= 0 ? t[e] : '/';
        }
        let h = (function (e, t) {
            var r;
            let n;
            void 0 === t && (t = '/');
            let { pathname: a, search: o = '', hash: i = '' } = 'string' == typeof e ? p(e) : e;
            return {
              pathname: a
                ? a.startsWith('/')
                  ? a
                  : ((r = a),
                    (n = t.replace(/\/+$/, '').split('/')),
                    r.split('/').forEach(e => {
                      '..' === e ? n.length > 1 && n.pop() : '.' !== e && n.push(e);
                    }),
                    n.length > 1 ? n.join('/') : '/')
                : t,
              search: x(o),
              hash: L(i),
            };
          })(a, i),
          d = u && '/' !== u && u.endsWith('/'),
          c = (l || '.' === u) && r.endsWith('/');
        return !h.pathname.endsWith('/') && (d || c) && (h.pathname += '/'), h;
      }
      let S = e => e.join('/').replace(/\/\/+/g, '/'),
        E = e => e.replace(/\/+$/, '').replace(/^\/*/, '/'),
        x = e => (e && '?' !== e ? (e.startsWith('?') ? e : '?' + e) : ''),
        L = e => (e && '#' !== e ? (e.startsWith('#') ? e : '#' + e) : '');
      class $ extends Error {}
      class O {
        constructor(e, t, r, n) {
          void 0 === n && (n = !1),
            (this.status = e),
            (this.statusText = t || ''),
            (this.internal = n),
            r instanceof Error ? ((this.data = r.toString()), (this.error = r)) : (this.data = r);
        }
      }
      function U(e) {
        return (
          null != e &&
          'number' == typeof e.status &&
          'string' == typeof e.statusText &&
          'boolean' == typeof e.internal &&
          'data' in e
        );
      }
      let W = ['post', 'put', 'patch', 'delete'],
        j = new Set(W),
        k = new Set(['get', ...W]),
        C = new Set([301, 302, 303, 307, 308]),
        z = /^(?:[a-z][a-z0-9+.-]*:|\/\/)/i;
      Symbol('deferred');
      function A(e, t, r) {
        void 0 === r && (r = !1);
        let n = e.findIndex(e => e.route.id === t);
        return n >= 0 ? e.slice(0, r ? n + 1 : n) : e;
      }
      function N(e, t) {
        let r = e.route.path;
        return (
          e.pathname !== t.pathname ||
          (null != r && r.endsWith('*') && e.params['*'] !== t.params['*'])
        );
      }
      function D(e, t) {
        if (e.route.shouldRevalidate) {
          let r = e.route.shouldRevalidate(t);
          if ('boolean' == typeof r) return r;
        }
        return t.defaultShouldRevalidate;
      }
      async function q(e, t, r) {
        if (!e.lazy) return;
        let n = await e.lazy();
        if (!e.lazy) return;
        let a = r[e.id];
        s(a, 'No route found in manifest');
        let i = {};
        for (let e in n) {
          let t = void 0 !== a[e] && 'hasErrorBoundary' !== e;
          u(
            !t,
            'Route "' +
              a.id +
              '" has a static property "' +
              e +
              '" defined but its lazy function is also returning a value for this property. The lazy route property "' +
              e +
              '" will be ignored.'
          ),
            t || f.has(e) || (i[e] = n[e]);
        }
        Object.assign(a, i), Object.assign(a, o({}, t(a), { lazy: void 0 }));
      }
      async function I(e, t, r, n, o, i) {
        let l,
          u,
          h = n => {
            let a,
              l = new Promise((e, t) => (a = t));
            (u = () => a()), t.signal.addEventListener('abort', u);
            let s = a =>
              'function' != typeof n
                ? Promise.reject(
                    Error(
                      'You cannot call the handler for a route which defines a boolean ' +
                        ('"' + e + '" [routeId: ') +
                        r.route.id +
                        ']'
                    )
                  )
                : n({ request: t, params: r.params, context: i }, ...(void 0 !== a ? [a] : []));
            return Promise.race([
              (async () => {
                try {
                  let e = await (o ? o(e => s(e)) : s());
                  return { type: 'data', result: e };
                } catch (e) {
                  return { type: 'error', result: e };
                }
              })(),
              l,
            ]);
          };
        try {
          let o = r.route[e];
          if (n)
            if (o) {
              let e,
                [t] = await Promise.all([
                  h(o).catch(t => {
                    e = t;
                  }),
                  n,
                ]);
              if (void 0 !== e) throw e;
              l = t;
            } else if ((await n, (o = r.route[e]))) l = await h(o);
            else {
              if ('action' !== e) return { type: a.data, result: void 0 };
              let n = new URL(t.url),
                o = n.pathname + n.search;
              throw M(405, { method: t.method, pathname: o, routeId: r.route.id });
            }
          else if (o) l = await h(o);
          else {
            let e = new URL(t.url),
              r = e.pathname + e.search;
            throw M(404, { pathname: r });
          }
          s(
            void 0 !== l.result,
            'You defined ' +
              ('action' === e ? 'an action' : 'a loader') +
              ' for route ' +
              ('"' + r.route.id + '" but didn\'t return anything from your `') +
              e +
              '` function. Please return a value or `null`.'
          );
        } catch (e) {
          return { type: a.error, result: e };
        } finally {
          u && t.signal.removeEventListener('abort', u);
        }
        return l;
      }
      function B(e) {
        let t = new URLSearchParams();
        for (let [r, n] of e.entries()) t.append(r, 'string' == typeof n ? n : n.name);
        return t;
      }
      function T(e) {
        let t = new FormData();
        for (let [r, n] of e.entries()) t.append(r, n);
        return t;
      }
      function F(e, t) {
        return (
          (t ? e.slice(0, e.findIndex(e => e.route.id === t) + 1) : [...e])
            .reverse()
            .find(e => !0 === e.route.hasErrorBoundary) || e[0]
        );
      }
      function M(e, t) {
        let { pathname: r, routeId: n, method: a, type: o, message: i } = void 0 === t ? {} : t,
          l = 'Unknown Server Error',
          s = 'Unknown @remix-run/router error';
        return (
          400 === e
            ? ((l = 'Bad Request'),
              a && r && n
                ? (s =
                    'You made a ' +
                    a +
                    ' request to "' +
                    r +
                    '" but did not provide a `loader` for route "' +
                    n +
                    '", so there is no way to handle the request.')
                : 'defer-action' === o
                  ? (s = 'defer() is not supported in actions')
                  : 'invalid-body' === o && (s = 'Unable to encode submission body'))
            : 403 === e
              ? ((l = 'Forbidden'), (s = 'Route "' + n + '" does not match URL "' + r + '"'))
              : 404 === e
                ? ((l = 'Not Found'), (s = 'No route matches URL "' + r + '"'))
                : 405 === e &&
                  ((l = 'Method Not Allowed'),
                  a && r && n
                    ? (s =
                        'You made a ' +
                        a.toUpperCase() +
                        ' request to "' +
                        r +
                        '" but did not provide an `action` for route "' +
                        n +
                        '", so there is no way to handle the request.')
                    : a && (s = 'Invalid request method "' + a.toUpperCase() + '"')),
          new O(e || 500, l, Error(s), !0)
        );
      }
      function Y(e) {
        let t = 'string' == typeof e ? p(e) : e;
        return c(o({}, t, { hash: '' }));
      }
      function _(e) {
        return e.type === a.deferred;
      }
      function H(e) {
        return e.type === a.error;
      }
      function J(e) {
        return (e && e.type) === a.redirect;
      }
      function G(e) {
        return (
          'object' == typeof e &&
          null != e &&
          'type' in e &&
          'data' in e &&
          'init' in e &&
          'DataWithResponseInit' === e.type
        );
      }
      function K(e) {
        return (
          null != e &&
          'number' == typeof e.status &&
          'string' == typeof e.statusText &&
          'object' == typeof e.headers &&
          void 0 !== e.body
        );
      }
      function Q(e) {
        return j.has(e.toLowerCase());
      }
      async function V(e, t, r) {
        if ((void 0 === r && (r = !1), !(await e.deferredData.resolveData(t)))) {
          if (r)
            try {
              return { type: a.data, data: e.deferredData.unwrappedData };
            } catch (e) {
              return { type: a.error, error: e };
            }
          return { type: a.data, data: e.deferredData.data };
        }
      }
      function X(e) {
        return new URLSearchParams(e).getAll('index').some(e => '' === e);
      }
    },
  },
]);
