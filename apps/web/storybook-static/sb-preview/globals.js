import ESM_COMPAT_Module from "node:module";
import { fileURLToPath as ESM_COMPAT_fileURLToPath } from 'node:url';
import { dirname as ESM_COMPAT_dirname } from 'node:path';
const __filename = ESM_COMPAT_fileURLToPath(import.meta.url);
const __dirname = ESM_COMPAT_dirname(__filename);
const require = ESM_COMPAT_Module.createRequire(import.meta.url);

// src/preview/globals/globals.ts
var _ = {
  "@storybook/global": "__STORYBOOK_MODULE_GLOBAL__",
  "storybook/test": "__STORYBOOK_MODULE_TEST__",
  "storybook/actions": "__STORYBOOK_MODULE_ACTIONS__",
  "storybook/preview-api": "__STORYBOOK_MODULE_PREVIEW_API__",
  "storybook/internal/channels": "__STORYBOOK_MODULE_CHANNELS__",
  "storybook/internal/client-logger": "__STORYBOOK_MODULE_CLIENT_LOGGER__",
  "storybook/internal/core-events": "__STORYBOOK_MODULE_CORE_EVENTS__",
  "storybook/internal/preview-errors": "__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__",
  "storybook/internal/types": "__STORYBOOK_MODULE_TYPES__",
  // @deprecated TODO: Remove in 9.1
  "storybook/internal/preview-api": "__STORYBOOK_MODULE_PREVIEW_API__"
}, o = Object.keys(_);
export {
  o as globalPackages,
  _ as globalsNameReferenceMap
};
