try{
(()=>{var Oc=Object.defineProperty;var ut=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var ur=(e,t)=>()=>(e&&(t=e(e=0)),t);var Tc=(e,t)=>{for(var r in t)Oc(e,r,{get:t[r],enumerable:!0})};var ee=ur(()=>{});var te=ur(()=>{});var re=ur(()=>{});var fo={};Tc(fo,{A:()=>Ic,ActionBar:()=>Mt,AddonPanel:()=>Ut,Badge:()=>ct,Bar:()=>$t,Blockquote:()=>_c,Button:()=>ge,Checkbox:()=>Bc,ClipboardCode:()=>Rc,Code:()=>Nc,DL:()=>Fc,Div:()=>Lc,DocumentWrapper:()=>Pc,EmptyTabContent:()=>Ht,ErrorFormatter:()=>jc,FlexBar:()=>mn,Form:()=>de,H1:()=>Mc,H2:()=>Uc,H3:()=>$c,H4:()=>Hc,H5:()=>Vc,H6:()=>zc,HR:()=>qc,IconButton:()=>G,Img:()=>Gc,LI:()=>Wc,Link:()=>be,ListItem:()=>Kc,Loader:()=>Yc,Modal:()=>He,OL:()=>Jc,P:()=>fn,Placeholder:()=>Xc,Pre:()=>Zc,ProgressSpinner:()=>Qc,ResetWrapper:()=>hn,ScrollArea:()=>gn,Separator:()=>bn,Spaced:()=>ed,Span:()=>td,StorybookIcon:()=>rd,StorybookLogo:()=>nd,SyntaxHighlighter:()=>Vt,TT:()=>ad,TabBar:()=>od,TabButton:()=>ld,TabWrapper:()=>id,Table:()=>sd,Tabs:()=>ud,TabsState:()=>cd,TooltipLinkList:()=>zt,TooltipMessage:()=>dd,TooltipNote:()=>Ne,UL:()=>pd,WithTooltip:()=>oe,WithTooltipPure:()=>yn,Zoom:()=>En,codeCommon:()=>Xe,components:()=>md,createCopyToClipboardFunction:()=>fd,default:()=>Dc,getStoryHref:()=>hd,interleaveSeparators:()=>gd,nameSpaceClassNames:()=>bd,resetComponents:()=>yd,withReset:()=>Ze});var Dc,Ic,Mt,Ut,ct,$t,_c,ge,Bc,Rc,Nc,Fc,Lc,Pc,Ht,jc,mn,de,Mc,Uc,$c,Hc,Vc,zc,qc,G,Gc,Wc,be,Kc,Yc,He,Jc,fn,Xc,Zc,Qc,hn,gn,bn,ed,td,rd,nd,Vt,ad,od,ld,id,sd,ud,cd,zt,dd,Ne,pd,oe,yn,En,Xe,md,fd,hd,gd,bd,yd,Ze,U=ur(()=>{ee();te();re();Dc=__STORYBOOK_COMPONENTS__,{A:Ic,ActionBar:Mt,AddonPanel:Ut,Badge:ct,Bar:$t,Blockquote:_c,Button:ge,Checkbox:Bc,ClipboardCode:Rc,Code:Nc,DL:Fc,Div:Lc,DocumentWrapper:Pc,EmptyTabContent:Ht,ErrorFormatter:jc,FlexBar:mn,Form:de,H1:Mc,H2:Uc,H3:$c,H4:Hc,H5:Vc,H6:zc,HR:qc,IconButton:G,Img:Gc,LI:Wc,Link:be,ListItem:Kc,Loader:Yc,Modal:He,OL:Jc,P:fn,Placeholder:Xc,Pre:Zc,ProgressSpinner:Qc,ResetWrapper:hn,ScrollArea:gn,Separator:bn,Spaced:ed,Span:td,StorybookIcon:rd,StorybookLogo:nd,SyntaxHighlighter:Vt,TT:ad,TabBar:od,TabButton:ld,TabWrapper:id,Table:sd,Tabs:ud,TabsState:cd,TooltipLinkList:zt,TooltipMessage:dd,TooltipNote:Ne,UL:pd,WithTooltip:oe,WithTooltipPure:yn,Zoom:En,codeCommon:Xe,components:md,createCopyToClipboardFunction:fd,getStoryHref:hd,interleaveSeparators:gd,nameSpaceClassNames:bd,resetComponents:yd,withReset:Ze}=__STORYBOOK_COMPONENTS__});ee();te();re();ee();te();re();ee();te();re();var n=__REACT__,{Children:cr,Component:Re,Fragment:Te,Profiler:bb,PureComponent:yb,StrictMode:Eb,Suspense:co,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:vb,act:xb,cloneElement:ae,createContext:Ct,createElement:P,createFactory:Ab,createRef:Cb,forwardRef:po,isValidElement:Sb,lazy:mo,memo:he,startTransition:wb,unstable_act:kb,useCallback:$,useContext:dr,useDebugValue:Ob,useDeferredValue:Tb,useEffect:j,useId:Db,useImperativeHandle:Ib,useInsertionEffect:_b,useLayoutEffect:pr,useMemo:ce,useReducer:Bb,useRef:X,useState:R,useSyncExternalStore:Rb,useTransition:Nb,version:Fb}=__REACT__;U();ee();te();re();var Hb=__STORYBOOK_ICONS__,{AccessibilityAltIcon:Vb,AccessibilityIcon:zb,AccessibilityIgnoredIcon:qb,AddIcon:fr,AdminIcon:Gb,AlertAltIcon:Wb,AlertIcon:Kb,AlignLeftIcon:Yb,AlignRightIcon:Jb,AppleIcon:Xb,ArrowBottomLeftIcon:Zb,ArrowBottomRightIcon:Qb,ArrowDownIcon:e1,ArrowLeftIcon:t1,ArrowRightIcon:r1,ArrowSolidDownIcon:n1,ArrowSolidLeftIcon:a1,ArrowSolidRightIcon:o1,ArrowSolidUpIcon:l1,ArrowTopLeftIcon:i1,ArrowTopRightIcon:s1,ArrowUpIcon:u1,AzureDevOpsIcon:c1,BackIcon:d1,BasketIcon:p1,BatchAcceptIcon:m1,BatchDenyIcon:f1,BeakerIcon:h1,BellIcon:g1,BitbucketIcon:b1,BoldIcon:y1,BookIcon:E1,BookmarkHollowIcon:v1,BookmarkIcon:x1,BottomBarIcon:A1,BottomBarToggleIcon:C1,BoxIcon:S1,BranchIcon:w1,BrowserIcon:ho,ButtonIcon:k1,CPUIcon:O1,CalendarIcon:T1,CameraIcon:D1,CameraStabilizeIcon:I1,CategoryIcon:_1,CertificateIcon:B1,ChangedIcon:R1,ChatIcon:N1,CheckIcon:hr,ChevronDownIcon:go,ChevronLeftIcon:F1,ChevronRightIcon:bo,ChevronSmallDownIcon:gr,ChevronSmallLeftIcon:L1,ChevronSmallRightIcon:P1,ChevronSmallUpIcon:yo,ChevronUpIcon:j1,ChromaticIcon:M1,ChromeIcon:U1,CircleHollowIcon:$1,CircleIcon:br,ClearIcon:H1,CloseAltIcon:V1,CloseIcon:z1,CloudHollowIcon:q1,CloudIcon:G1,CogIcon:W1,CollapseIcon:K1,CommandIcon:Y1,CommentAddIcon:J1,CommentIcon:X1,CommentsIcon:Z1,CommitIcon:Q1,CompassIcon:ey,ComponentDrivenIcon:ty,ComponentIcon:ry,ContrastIcon:ny,ContrastIgnoredIcon:ay,ControlsIcon:oy,CopyIcon:ly,CreditIcon:iy,CrossIcon:sy,DashboardIcon:uy,DatabaseIcon:cy,DeleteIcon:dy,DiamondIcon:py,DirectionIcon:my,DiscordIcon:fy,DocChartIcon:hy,DocListIcon:gy,DocumentIcon:dt,DownloadIcon:by,DragIcon:yy,EditIcon:Ey,EllipsisIcon:vy,EmailIcon:xy,ExpandAltIcon:Ay,ExpandIcon:Cy,EyeCloseIcon:Eo,EyeIcon:vo,FaceHappyIcon:Sy,FaceNeutralIcon:wy,FaceSadIcon:ky,FacebookIcon:Oy,FailedIcon:xo,FastForwardIcon:Ao,FigmaIcon:Ty,FilterIcon:Dy,FlagIcon:Iy,FolderIcon:_y,FormIcon:By,GDriveIcon:Ry,GithubIcon:Ny,GitlabIcon:Fy,GlobeIcon:Ly,GoogleIcon:Py,GraphBarIcon:jy,GraphLineIcon:My,GraphqlIcon:Uy,GridAltIcon:$y,GridIcon:Co,GrowIcon:So,HeartHollowIcon:Hy,HeartIcon:Vy,HomeIcon:zy,HourglassIcon:qy,InfoIcon:Gy,ItalicIcon:Wy,JumpToIcon:Ky,KeyIcon:Yy,LightningIcon:Jy,LightningOffIcon:Xy,LinkBrokenIcon:Zy,LinkIcon:Qy,LinkedinIcon:eE,LinuxIcon:tE,ListOrderedIcon:rE,ListUnorderedIcon:wo,LocationIcon:nE,LockIcon:aE,MarkdownIcon:oE,MarkupIcon:ko,MediumIcon:lE,MemoryIcon:iE,MenuIcon:sE,MergeIcon:uE,MirrorIcon:cE,MobileIcon:Oo,MoonIcon:dE,NutIcon:pE,OutboxIcon:mE,OutlineIcon:To,PaintBrushIcon:fE,PaperClipIcon:hE,ParagraphIcon:gE,PassedIcon:vn,PhoneIcon:bE,PhotoDragIcon:yE,PhotoIcon:Do,PhotoStabilizeIcon:EE,PinAltIcon:vE,PinIcon:xE,PlayAllHollowIcon:AE,PlayBackIcon:Io,PlayHollowIcon:CE,PlayIcon:_o,PlayNextIcon:Bo,PlusIcon:SE,PointerDefaultIcon:wE,PointerHandIcon:kE,PowerIcon:OE,PrintIcon:TE,ProceedIcon:DE,ProfileIcon:IE,PullRequestIcon:_E,QuestionIcon:BE,RSSIcon:RE,RedirectIcon:NE,ReduxIcon:FE,RefreshIcon:yr,ReplyIcon:LE,RepoIcon:PE,RequestChangeIcon:jE,RewindIcon:Ro,RulerIcon:No,SaveIcon:ME,SearchIcon:UE,ShareAltIcon:$E,ShareIcon:HE,ShieldIcon:VE,SideBySideIcon:zE,SidebarAltIcon:qE,SidebarAltToggleIcon:GE,SidebarIcon:WE,SidebarToggleIcon:KE,SpeakerIcon:YE,StackedIcon:JE,StarHollowIcon:XE,StarIcon:ZE,StatusFailIcon:QE,StatusIcon:ev,StatusPassIcon:tv,StatusWarnIcon:rv,StickerIcon:nv,StopAltHollowIcon:av,StopAltIcon:Fo,StopIcon:ov,StorybookIcon:lv,StructureIcon:iv,SubtractIcon:Lo,SunIcon:sv,SupportIcon:uv,SweepIcon:cv,SwitchAltIcon:dv,SyncIcon:Po,TabletIcon:jo,ThumbsUpIcon:pv,TimeIcon:mv,TimerIcon:fv,TransferIcon:Mo,TrashIcon:hv,TwitterIcon:gv,TypeIcon:bv,UbuntuIcon:yv,UndoIcon:Er,UnfoldIcon:Ev,UnlockIcon:vv,UnpinIcon:xv,UploadIcon:Av,UserAddIcon:Cv,UserAltIcon:Sv,UserIcon:wv,UsersIcon:kv,VSCodeIcon:Ov,VerifiedIcon:Tv,VideoIcon:Dv,WandIcon:Iv,WatchIcon:_v,WindowsIcon:Bv,WrenchIcon:Rv,XIcon:Nv,YoutubeIcon:Fv,ZoomIcon:Uo,ZoomOutIcon:$o,ZoomResetIcon:Ho,iconList:Lv}=__STORYBOOK_ICONS__;ee();te();re();var $v=__STORYBOOK_THEMING__,{CacheProvider:Hv,ClassNames:Vv,Global:Vo,ThemeProvider:zo,background:zv,color:vr,convert:qo,create:qv,createCache:Gv,createGlobal:Wv,createReset:Kv,css:Yv,darken:Jv,ensure:Xv,ignoreSsrWarning:Go,isPropValid:Zv,jsx:Qv,keyframes:xn,lighten:ex,styled:b,themes:An,typography:Fe,useTheme:De,withTheme:Wo}=__STORYBOOK_THEMING__;ee();te();re();var Qe=(()=>{let e;return typeof window<"u"?e=window:typeof globalThis<"u"?e=globalThis:typeof window<"u"?e=window:typeof self<"u"?e=self:e={},e})();ee();te();re();var ux=__STORYBOOK_API__,{ActiveTabs:cx,Consumer:Ko,ManagerContext:dx,Provider:px,RequestResponseError:mx,addons:Z,combineParameters:fx,controlOrMetaKey:hx,controlOrMetaSymbol:gx,eventMatchesShortcut:bx,eventToShortcut:yx,experimental_MockUniversalStore:Ex,experimental_UniversalStore:vx,experimental_getStatusStore:xx,experimental_getTestProviderStore:Ax,experimental_requestResponse:Cn,experimental_useStatusStore:Yo,experimental_useTestProviderStore:Cx,experimental_useUniversalStore:Sx,internal_fullStatusStore:wx,internal_fullTestProviderStore:kx,internal_universalStatusStore:Ox,internal_universalTestProviderStore:Tx,isMacLike:Dx,isShortcutTaken:Ix,keyToSymbol:_x,merge:Bx,mockChannel:Rx,optionOrAltSymbol:Nx,shortcutMatchesShortcut:Fx,shortcutToHumanString:Lx,types:ye,useAddonState:St,useArgTypes:xr,useArgs:Jo,useChannel:Ar,useGlobalTypes:Px,useGlobals:Ve,useParameter:et,useSharedState:jx,useStoryPrepared:Mx,useStorybookApi:Ee,useStorybookState:Xo}=__STORYBOOK_API__;U();ee();te();re();var zx=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:qx,ARGTYPES_INFO_RESPONSE:Gx,CHANNEL_CREATED:Wx,CHANNEL_WS_DISCONNECT:Kx,CONFIG_ERROR:Yx,CREATE_NEW_STORYFILE_REQUEST:Jx,CREATE_NEW_STORYFILE_RESPONSE:Xx,CURRENT_STORY_WAS_SET:Zx,DOCS_PREPARED:Qx,DOCS_RENDERED:eA,FILE_COMPONENT_SEARCH_REQUEST:tA,FILE_COMPONENT_SEARCH_RESPONSE:rA,FORCE_REMOUNT:Zo,FORCE_RE_RENDER:nA,GLOBALS_UPDATED:aA,NAVIGATE_URL:oA,PLAY_FUNCTION_THREW_EXCEPTION:Qo,PRELOAD_ENTRIES:lA,PREVIEW_BUILDER_PROGRESS:iA,PREVIEW_KEYDOWN:sA,REGISTER_SUBSCRIPTION:uA,REQUEST_WHATS_NEW_DATA:cA,RESET_STORY_ARGS:dA,RESULT_WHATS_NEW_DATA:pA,SAVE_STORY_REQUEST:Sn,SAVE_STORY_RESPONSE:Cr,SELECT_STORY:mA,SET_CONFIG:fA,SET_CURRENT_STORY:hA,SET_FILTER:gA,SET_GLOBALS:bA,SET_INDEX:yA,SET_STORIES:EA,SET_WHATS_NEW_CACHE:vA,SHARED_STATE_CHANGED:xA,SHARED_STATE_SET:AA,STORIES_COLLAPSE_ALL:CA,STORIES_EXPAND_ALL:SA,STORY_ARGS_UPDATED:wA,STORY_CHANGED:qt,STORY_ERRORED:kA,STORY_FINISHED:OA,STORY_HOT_UPDATED:TA,STORY_INDEX_INVALIDATED:DA,STORY_MISSING:IA,STORY_PREPARED:_A,STORY_RENDERED:BA,STORY_RENDER_PHASE_CHANGED:el,STORY_SPECIFIED:RA,STORY_THREW_EXCEPTION:tl,STORY_UNCHANGED:NA,TELEMETRY_ERROR:FA,TOGGLE_WHATS_NEW_NOTIFICATIONS:LA,UNHANDLED_ERRORS_WHILE_PLAYING:rl,UPDATE_GLOBALS:PA,UPDATE_QUERY_PARAMS:jA,UPDATE_STORY_ARGS:MA}=__STORYBOOK_CORE_EVENTS__;ee();te();re();var zA=__STORYBOOK_CLIENT_LOGGER__,{deprecate:qA,logger:tt,once:nl,pretty:GA}=__STORYBOOK_CLIENT_LOGGER__;U();ee();te();re();var Ed=Object.create,kn=Object.defineProperty,vd=Object.getOwnPropertyDescriptor,xd=Object.getOwnPropertyNames,Ad=Object.getPrototypeOf,Cd=Object.prototype.hasOwnProperty,pe=(e,t)=>kn(e,"name",{value:t,configurable:!0}),Sd=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),wd=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of xd(t))!Cd.call(e,o)&&o!==r&&kn(e,o,{get:()=>t[o],enumerable:!(a=vd(t,o))||a.enumerable});return e},kd=(e,t,r)=>(r=e!=null?Ed(Ad(e)):{},wd(t||!e||!e.__esModule?kn(r,"default",{value:e,enumerable:!0}):r,e)),Od=Sd(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isEqual=function(){var t=Object.prototype.toString,r=Object.getPrototypeOf,a=Object.getOwnPropertySymbols?function(o){return Object.keys(o).concat(Object.getOwnPropertySymbols(o))}:Object.keys;return function(o,c){return pe(function l(s,u,d){var m,f,p,h=t.call(s),g=t.call(u);if(s===u)return!0;if(s==null||u==null)return!1;if(d.indexOf(s)>-1&&d.indexOf(u)>-1)return!0;if(d.push(s,u),h!=g||(m=a(s),f=a(u),m.length!=f.length||m.some(function(E){return!l(s[E],u[E],d)})))return!1;switch(h.slice(8,-1)){case"Symbol":return s.valueOf()==u.valueOf();case"Date":case"Number":return+s==+u||+s!=+s&&+u!=+u;case"RegExp":case"Function":case"String":case"Boolean":return""+s==""+u;case"Set":case"Map":m=s.entries(),f=u.entries();do if(!l((p=m.next()).value,f.next().value,d))return!1;while(!p.done);return!0;case"ArrayBuffer":s=new Uint8Array(s),u=new Uint8Array(u);case"DataView":s=new Uint8Array(s.buffer),u=new Uint8Array(u.buffer);case"Float32Array":case"Float64Array":case"Int8Array":case"Int16Array":case"Int32Array":case"Uint8Array":case"Uint16Array":case"Uint32Array":case"Uint8ClampedArray":case"Arguments":case"Array":if(s.length!=u.length)return!1;for(p=0;p<s.length;p++)if((p in s||p in u)&&(p in s!=p in u||!l(s[p],u[p],d)))return!1;return!0;case"Object":return l(r(s),r(u),d);default:return!1}},"n")(o,c,[])}}()});function ll(e){return e.replace(/_/g," ").replace(/-/g," ").replace(/\./g," ").replace(/([^\n])([A-Z])([a-z])/g,(t,r,a,o)=>`${r} ${a}${o}`).replace(/([a-z])([A-Z])/g,(t,r,a)=>`${r} ${a}`).replace(/([a-z])([0-9])/gi,(t,r,a)=>`${r} ${a}`).replace(/([0-9])([a-z])/gi,(t,r,a)=>`${r} ${a}`).replace(/(\s|^)(\w)/g,(t,r,a)=>`${r}${a.toUpperCase()}`).replace(/ +/g," ").trim()}pe(ll,"toStartCaseStr");var al=kd(Od(),1),il=pe(e=>e.map(t=>typeof t<"u").filter(Boolean).length,"count"),Td=pe((e,t)=>{let{exists:r,eq:a,neq:o,truthy:c}=e;if(il([r,a,o,c])>1)throw new Error(`Invalid conditional test ${JSON.stringify({exists:r,eq:a,neq:o})}`);if(typeof a<"u")return(0,al.isEqual)(t,a);if(typeof o<"u")return!(0,al.isEqual)(t,o);if(typeof r<"u"){let l=typeof t<"u";return r?l:!l}return typeof c>"u"||c?!!t:!t},"testValue"),sl=pe((e,t,r)=>{if(!e.if)return!0;let{arg:a,global:o}=e.if;if(il([a,o])!==1)throw new Error(`Invalid conditional value ${JSON.stringify({arg:a,global:o})}`);let c=a?t[a]:r[o];return Td(e.if,c)},"includeConditionalArg");function Dd(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Preview"}pe(Dd,"isPreview");function Id(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Meta"}pe(Id,"isMeta");function _d(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Story"}pe(_d,"isStory");var Bd=pe(e=>e.toLowerCase().replace(/[ ’–—―′¿'`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),"sanitize"),ol=pe((e,t)=>{let r=Bd(e);if(r==="")throw new Error(`Invalid ${t} '${e}', must include alphanumeric characters`);return r},"sanitizeSafe"),XA=pe((e,t)=>`${ol(e,"kind")}${t?`--${ol(t,"name")}`:""}`,"toId"),ZA=pe(e=>ll(e),"storyNameFromExport");function wn(e,t){return Array.isArray(t)?t.includes(e):e.match(t)}pe(wn,"matches");function Rd(e,{includeStories:t,excludeStories:r}){return e!=="__esModule"&&(!t||wn(e,t))&&(!r||!wn(e,r))}pe(Rd,"isExportStory");var QA=pe((e,{rootSeparator:t,groupSeparator:r})=>{let[a,o]=e.split(t,2),c=(o||e).split(r).filter(l=>!!l);return{root:o?a:null,groups:c}},"parseKind"),eC=pe((...e)=>{let t=e.reduce((r,a)=>(a.startsWith("!")?r.delete(a.slice(1)):r.add(a),r),new Set);return Array.from(t)},"combineTags");U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();var Nd=Object.create,rn=Object.defineProperty,Fd=Object.getOwnPropertyDescriptor,Ld=Object.getOwnPropertyNames,Pd=Object.getPrototypeOf,jd=Object.prototype.hasOwnProperty,i=(e,t)=>rn(e,"name",{value:t,configurable:!0}),Sr=(e=>typeof ut<"u"?ut:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof ut<"u"?ut:t)[r]}):e)(function(e){if(typeof ut<"u")return ut.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),q=(e,t)=>()=>(e&&(t=e(e=0)),t),Y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Md=(e,t)=>{for(var r in t)rn(e,r,{get:t[r],enumerable:!0})},Ud=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Ld(t))!jd.call(e,o)&&o!==r&&rn(e,o,{get:()=>t[o],enumerable:!(a=Fd(t,o))||a.enumerable});return e},Ce=(e,t,r)=>(r=e!=null?Nd(Pd(e)):{},Ud(t||!e||!e.__esModule?rn(r,"default",{value:e,enumerable:!0}):r,e));function Oa(e){return typeof e=="symbol"||e instanceof Symbol}var Oi=q(()=>{i(Oa,"isSymbol")});function Ti(e){return Oa(e)?NaN:Number(e)}var $d=q(()=>{Oi(),i(Ti,"toNumber")});function Di(e){return e?(e=Ti(e),e===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e===e?e:0):e===0?e:0}var Hd=q(()=>{$d(),i(Di,"toFinite")});function Ii(e){let t=Di(e),r=t%1;return r?t-r:t}var Vd=q(()=>{Hd(),i(Ii,"toInteger")});function _i(e){return Array.from(new Set(e))}var zd=q(()=>{i(_i,"uniq")});function Bi(e){return e==null||typeof e!="object"&&typeof e!="function"}var qd=q(()=>{i(Bi,"isPrimitive")});function Ta(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}var Ri=q(()=>{i(Ta,"isTypedArray")});function Da(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}var Ni=q(()=>{i(Da,"getSymbols")});function Fi(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}var Gd=q(()=>{i(Fi,"getTag")}),Li,Ia,_a,Ba,Ra,Pi,ji,Mi,Ui,$i,Hi,Vi,zi,qi,Gi,Wi,Ki,Yi,Ji,Xi,Zi,Qi,es=q(()=>{Li="[object RegExp]",Ia="[object String]",_a="[object Number]",Ba="[object Boolean]",Ra="[object Arguments]",Pi="[object Symbol]",ji="[object Date]",Mi="[object Map]",Ui="[object Set]",$i="[object Array]",Hi="[object ArrayBuffer]",Vi="[object Object]",zi="[object DataView]",qi="[object Uint8Array]",Gi="[object Uint8ClampedArray]",Wi="[object Uint16Array]",Ki="[object Uint32Array]",Yi="[object Int8Array]",Ji="[object Int16Array]",Xi="[object Int32Array]",Zi="[object Float32Array]",Qi="[object Float64Array]"});function ts(e,t){return gt(e,void 0,e,new Map,t)}function gt(e,t,r,a=new Map,o=void 0){let c=o?.(e,t,r,a);if(c!=null)return c;if(Bi(e))return e;if(a.has(e))return a.get(e);if(Array.isArray(e)){let l=new Array(e.length);a.set(e,l);for(let s=0;s<e.length;s++)l[s]=gt(e[s],s,r,a,o);return Object.hasOwn(e,"index")&&(l.index=e.index),Object.hasOwn(e,"input")&&(l.input=e.input),l}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let l=new RegExp(e.source,e.flags);return l.lastIndex=e.lastIndex,l}if(e instanceof Map){let l=new Map;a.set(e,l);for(let[s,u]of e)l.set(s,gt(u,s,r,a,o));return l}if(e instanceof Set){let l=new Set;a.set(e,l);for(let s of e)l.add(gt(s,void 0,r,a,o));return l}if(typeof Buffer<"u"&&Buffer.isBuffer(e))return e.subarray();if(Ta(e)){let l=new(Object.getPrototypeOf(e)).constructor(e.length);a.set(e,l);for(let s=0;s<e.length;s++)l[s]=gt(e[s],s,r,a,o);return l}if(e instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let l=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return a.set(e,l),ot(l,e,r,a,o),l}if(typeof File<"u"&&e instanceof File){let l=new File([e],e.name,{type:e.type});return a.set(e,l),ot(l,e,r,a,o),l}if(e instanceof Blob){let l=new Blob([e],{type:e.type});return a.set(e,l),ot(l,e,r,a,o),l}if(e instanceof Error){let l=new e.constructor;return a.set(e,l),l.message=e.message,l.name=e.name,l.stack=e.stack,l.cause=e.cause,ot(l,e,r,a,o),l}if(typeof e=="object"&&rs(e)){let l=Object.create(Object.getPrototypeOf(e));return a.set(e,l),ot(l,e,r,a,o),l}return e}function ot(e,t,r=e,a,o){let c=[...Object.keys(t),...Da(t)];for(let l=0;l<c.length;l++){let s=c[l],u=Object.getOwnPropertyDescriptor(e,s);(u==null||u.writable)&&(e[s]=gt(t[s],s,r,a,o))}}function rs(e){switch(Fi(e)){case Ra:case $i:case Hi:case zi:case Ba:case ji:case Zi:case Qi:case Yi:case Ji:case Xi:case Mi:case _a:case Vi:case Li:case Ui:case Ia:case Pi:case qi:case Gi:case Wi:case Ki:return!0;default:return!1}}var Wd=q(()=>{Ni(),Gd(),es(),qd(),Ri(),i(ts,"cloneDeepWith"),i(gt,"cloneDeepWithImpl"),i(ot,"copyProperties"),i(rs,"isCloneableObject")});function ns(e){return Number.isSafeInteger(e)&&e>=0}var Kd=q(()=>{i(ns,"isLength")});function nn(e){return e!=null&&typeof e!="function"&&ns(e.length)}var Na=q(()=>{Kd(),i(nn,"isArrayLike")});function as(e,t){return ts(e,(r,a,o,c)=>{let l=t?.(r,a,o,c);if(l!=null)return l;if(typeof e=="object")switch(Object.prototype.toString.call(e)){case _a:case Ia:case Ba:{let s=new e.constructor(e?.valueOf());return ot(s,e),s}case Ra:{let s={};return ot(s,e),s.length=e.length,s[Symbol.iterator]=e[Symbol.iterator],s}default:return}})}var Yd=q(()=>{Wd(),es(),i(as,"cloneDeepWith")});function os(e){return as(e)}var Jd=q(()=>{Yd(),i(os,"cloneDeep")});function ls(e,t,r=1){if(t==null&&(t=e,e=0),!Number.isInteger(r)||r===0)throw new Error("The step value must be a non-zero integer.");let a=Math.max(Math.ceil((t-e)/r),0),o=new Array(a);for(let c=0;c<a;c++)o[c]=e+c*r;return o}var Xd=q(()=>{i(ls,"range")});function is(e){return nn(e)?_i(Array.from(e)):[]}var Zd=q(()=>{zd(),Na(),i(is,"uniq")});function ss(e,t,{signal:r,edges:a}={}){let o,c=null,l=a!=null&&a.includes("leading"),s=a==null||a.includes("trailing"),u=i(()=>{c!==null&&(e.apply(o,c),o=void 0,c=null)},"invoke"),d=i(()=>{s&&u(),h()},"onTimerEnd"),m=null,f=i(()=>{m!=null&&clearTimeout(m),m=setTimeout(()=>{m=null,d()},t)},"schedule"),p=i(()=>{m!==null&&(clearTimeout(m),m=null)},"cancelTimer"),h=i(()=>{p(),o=void 0,c=null},"cancel"),g=i(()=>{p(),u()},"flush"),E=i(function(...y){if(r?.aborted)return;o=this,c=y;let v=m==null;f(),l&&v&&u()},"debounced");return E.schedule=f,E.cancel=h,E.flush=g,r?.addEventListener("abort",h,{once:!0}),E}var Qd=q(()=>{i(ss,"debounce")});function us(e,t=0,r={}){typeof r!="object"&&(r={});let{signal:a,leading:o=!1,trailing:c=!0,maxWait:l}=r,s=Array(2);o&&(s[0]="leading"),c&&(s[1]="trailing");let u,d=null,m=ss(function(...h){u=e.apply(this,h),d=null},t,{signal:a,edges:s}),f=i(function(...h){if(l!=null){if(d===null)d=Date.now();else if(Date.now()-d>=l)return u=e.apply(this,h),d=Date.now(),m.cancel(),m.schedule(),u}return m.apply(this,h),u},"debounced"),p=i(()=>(m.flush(),u),"flush");return f.cancel=m.cancel,f.flush=p,f}var ep=q(()=>{Qd(),i(us,"debounce")});function cs(e){return typeof Buffer<"u"&&Buffer.isBuffer(e)}var tp=q(()=>{i(cs,"isBuffer")});function ds(e){let t=e?.constructor,r=typeof t=="function"?t.prototype:Object.prototype;return e===r}var rp=q(()=>{i(ds,"isPrototype")});function ps(e){return Ta(e)}var np=q(()=>{Ri(),i(ps,"isTypedArray")});function ms(e,t){if(e=Ii(e),e<1||!Number.isSafeInteger(e))return[];let r=new Array(e);for(let a=0;a<e;a++)r[a]=typeof t=="function"?t(a):a;return r}var ap=q(()=>{Vd(),i(ms,"times")});function fs(e){if(e==null)return[];switch(typeof e){case"object":case"function":return nn(e)?gs(e):ds(e)?hs(e):nr(e);default:return nr(Object(e))}}function nr(e){let t=[];for(let r in e)t.push(r);return t}function hs(e){return nr(e).filter(t=>t!=="constructor")}function gs(e){let t=ms(e.length,a=>`${a}`),r=new Set(t);return cs(e)&&(r.add("offset"),r.add("parent")),ps(e)&&(r.add("buffer"),r.add("byteLength"),r.add("byteOffset")),[...t,...nr(e).filter(a=>!r.has(a))]}var op=q(()=>{tp(),rp(),Na(),np(),ap(),i(fs,"keysIn"),i(nr,"keysInImpl"),i(hs,"prototypeKeysIn"),i(gs,"arrayLikeKeysIn")});function bs(e){let t=[];for(;e;)t.push(...Da(e)),e=Object.getPrototypeOf(e);return t}var lp=q(()=>{Ni(),i(bs,"getSymbolsIn")});function ys(e,t){if(e==null)return{};let r={};if(t==null)return e;let a=nn(e)?ls(0,e.length):[...fs(e),...bs(e)];for(let o=0;o<a.length;o++){let c=Oa(a[o])?a[o]:a[o].toString(),l=e[c];t(l,c,e)&&(r[c]=l)}return r}var ip=q(()=>{op(),Xd(),lp(),Na(),Oi(),i(ys,"pickBy")}),an=q(()=>{Zd(),ep(),Jd(),ip()}),Oe,or,je=q(()=>{"use strict";Oe=i(e=>`control-${e.replace(/\s+/g,"-")}`,"getControlId"),or=i(e=>`set-${e.replace(/\s+/g,"-")}`,"getControlSetterButtonId")}),sp=Y((e,t)=>{"use strict";t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}),Es=Y((e,t)=>{var r=sp(),a={};for(let l of Object.keys(r))a[r[l]]=l;var o={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};t.exports=o;for(let l of Object.keys(o)){if(!("channels"in o[l]))throw new Error("missing channels property: "+l);if(!("labels"in o[l]))throw new Error("missing channel labels property: "+l);if(o[l].labels.length!==o[l].channels)throw new Error("channel and label counts mismatch: "+l);let{channels:s,labels:u}=o[l];delete o[l].channels,delete o[l].labels,Object.defineProperty(o[l],"channels",{value:s}),Object.defineProperty(o[l],"labels",{value:u})}o.rgb.hsl=function(l){let s=l[0]/255,u=l[1]/255,d=l[2]/255,m=Math.min(s,u,d),f=Math.max(s,u,d),p=f-m,h,g;f===m?h=0:s===f?h=(u-d)/p:u===f?h=2+(d-s)/p:d===f&&(h=4+(s-u)/p),h=Math.min(h*60,360),h<0&&(h+=360);let E=(m+f)/2;return f===m?g=0:E<=.5?g=p/(f+m):g=p/(2-f-m),[h,g*100,E*100]},o.rgb.hsv=function(l){let s,u,d,m,f,p=l[0]/255,h=l[1]/255,g=l[2]/255,E=Math.max(p,h,g),y=E-Math.min(p,h,g),v=i(function(C){return(E-C)/6/y+1/2},"diffc");return y===0?(m=0,f=0):(f=y/E,s=v(p),u=v(h),d=v(g),p===E?m=d-u:h===E?m=1/3+s-d:g===E&&(m=2/3+u-s),m<0?m+=1:m>1&&(m-=1)),[m*360,f*100,E*100]},o.rgb.hwb=function(l){let s=l[0],u=l[1],d=l[2],m=o.rgb.hsl(l)[0],f=1/255*Math.min(s,Math.min(u,d));return d=1-1/255*Math.max(s,Math.max(u,d)),[m,f*100,d*100]},o.rgb.cmyk=function(l){let s=l[0]/255,u=l[1]/255,d=l[2]/255,m=Math.min(1-s,1-u,1-d),f=(1-s-m)/(1-m)||0,p=(1-u-m)/(1-m)||0,h=(1-d-m)/(1-m)||0;return[f*100,p*100,h*100,m*100]};function c(l,s){return(l[0]-s[0])**2+(l[1]-s[1])**2+(l[2]-s[2])**2}i(c,"comparativeDistance"),o.rgb.keyword=function(l){let s=a[l];if(s)return s;let u=1/0,d;for(let m of Object.keys(r)){let f=r[m],p=c(l,f);p<u&&(u=p,d=m)}return d},o.keyword.rgb=function(l){return r[l]},o.rgb.xyz=function(l){let s=l[0]/255,u=l[1]/255,d=l[2]/255;s=s>.04045?((s+.055)/1.055)**2.4:s/12.92,u=u>.04045?((u+.055)/1.055)**2.4:u/12.92,d=d>.04045?((d+.055)/1.055)**2.4:d/12.92;let m=s*.4124+u*.3576+d*.1805,f=s*.2126+u*.7152+d*.0722,p=s*.0193+u*.1192+d*.9505;return[m*100,f*100,p*100]},o.rgb.lab=function(l){let s=o.rgb.xyz(l),u=s[0],d=s[1],m=s[2];u/=95.047,d/=100,m/=108.883,u=u>.008856?u**(1/3):7.787*u+16/116,d=d>.008856?d**(1/3):7.787*d+16/116,m=m>.008856?m**(1/3):7.787*m+16/116;let f=116*d-16,p=500*(u-d),h=200*(d-m);return[f,p,h]},o.hsl.rgb=function(l){let s=l[0]/360,u=l[1]/100,d=l[2]/100,m,f,p;if(u===0)return p=d*255,[p,p,p];d<.5?m=d*(1+u):m=d+u-d*u;let h=2*d-m,g=[0,0,0];for(let E=0;E<3;E++)f=s+1/3*-(E-1),f<0&&f++,f>1&&f--,6*f<1?p=h+(m-h)*6*f:2*f<1?p=m:3*f<2?p=h+(m-h)*(2/3-f)*6:p=h,g[E]=p*255;return g},o.hsl.hsv=function(l){let s=l[0],u=l[1]/100,d=l[2]/100,m=u,f=Math.max(d,.01);d*=2,u*=d<=1?d:2-d,m*=f<=1?f:2-f;let p=(d+u)/2,h=d===0?2*m/(f+m):2*u/(d+u);return[s,h*100,p*100]},o.hsv.rgb=function(l){let s=l[0]/60,u=l[1]/100,d=l[2]/100,m=Math.floor(s)%6,f=s-Math.floor(s),p=255*d*(1-u),h=255*d*(1-u*f),g=255*d*(1-u*(1-f));switch(d*=255,m){case 0:return[d,g,p];case 1:return[h,d,p];case 2:return[p,d,g];case 3:return[p,h,d];case 4:return[g,p,d];case 5:return[d,p,h]}},o.hsv.hsl=function(l){let s=l[0],u=l[1]/100,d=l[2]/100,m=Math.max(d,.01),f,p;p=(2-u)*d;let h=(2-u)*m;return f=u*m,f/=h<=1?h:2-h,f=f||0,p/=2,[s,f*100,p*100]},o.hwb.rgb=function(l){let s=l[0]/360,u=l[1]/100,d=l[2]/100,m=u+d,f;m>1&&(u/=m,d/=m);let p=Math.floor(6*s),h=1-d;f=6*s-p,(p&1)!==0&&(f=1-f);let g=u+f*(h-u),E,y,v;switch(p){default:case 6:case 0:E=h,y=g,v=u;break;case 1:E=g,y=h,v=u;break;case 2:E=u,y=h,v=g;break;case 3:E=u,y=g,v=h;break;case 4:E=g,y=u,v=h;break;case 5:E=h,y=u,v=g;break}return[E*255,y*255,v*255]},o.cmyk.rgb=function(l){let s=l[0]/100,u=l[1]/100,d=l[2]/100,m=l[3]/100,f=1-Math.min(1,s*(1-m)+m),p=1-Math.min(1,u*(1-m)+m),h=1-Math.min(1,d*(1-m)+m);return[f*255,p*255,h*255]},o.xyz.rgb=function(l){let s=l[0]/100,u=l[1]/100,d=l[2]/100,m,f,p;return m=s*3.2406+u*-1.5372+d*-.4986,f=s*-.9689+u*1.8758+d*.0415,p=s*.0557+u*-.204+d*1.057,m=m>.0031308?1.055*m**(1/2.4)-.055:m*12.92,f=f>.0031308?1.055*f**(1/2.4)-.055:f*12.92,p=p>.0031308?1.055*p**(1/2.4)-.055:p*12.92,m=Math.min(Math.max(0,m),1),f=Math.min(Math.max(0,f),1),p=Math.min(Math.max(0,p),1),[m*255,f*255,p*255]},o.xyz.lab=function(l){let s=l[0],u=l[1],d=l[2];s/=95.047,u/=100,d/=108.883,s=s>.008856?s**(1/3):7.787*s+16/116,u=u>.008856?u**(1/3):7.787*u+16/116,d=d>.008856?d**(1/3):7.787*d+16/116;let m=116*u-16,f=500*(s-u),p=200*(u-d);return[m,f,p]},o.lab.xyz=function(l){let s=l[0],u=l[1],d=l[2],m,f,p;f=(s+16)/116,m=u/500+f,p=f-d/200;let h=f**3,g=m**3,E=p**3;return f=h>.008856?h:(f-16/116)/7.787,m=g>.008856?g:(m-16/116)/7.787,p=E>.008856?E:(p-16/116)/7.787,m*=95.047,f*=100,p*=108.883,[m,f,p]},o.lab.lch=function(l){let s=l[0],u=l[1],d=l[2],m;m=Math.atan2(d,u)*360/2/Math.PI,m<0&&(m+=360);let f=Math.sqrt(u*u+d*d);return[s,f,m]},o.lch.lab=function(l){let s=l[0],u=l[1],d=l[2]/360*2*Math.PI,m=u*Math.cos(d),f=u*Math.sin(d);return[s,m,f]},o.rgb.ansi16=function(l,s=null){let[u,d,m]=l,f=s===null?o.rgb.hsv(l)[2]:s;if(f=Math.round(f/50),f===0)return 30;let p=30+(Math.round(m/255)<<2|Math.round(d/255)<<1|Math.round(u/255));return f===2&&(p+=60),p},o.hsv.ansi16=function(l){return o.rgb.ansi16(o.hsv.rgb(l),l[2])},o.rgb.ansi256=function(l){let s=l[0],u=l[1],d=l[2];return s===u&&u===d?s<8?16:s>248?231:Math.round((s-8)/247*24)+232:16+36*Math.round(s/255*5)+6*Math.round(u/255*5)+Math.round(d/255*5)},o.ansi16.rgb=function(l){let s=l%10;if(s===0||s===7)return l>50&&(s+=3.5),s=s/10.5*255,[s,s,s];let u=(~~(l>50)+1)*.5,d=(s&1)*u*255,m=(s>>1&1)*u*255,f=(s>>2&1)*u*255;return[d,m,f]},o.ansi256.rgb=function(l){if(l>=232){let f=(l-232)*10+8;return[f,f,f]}l-=16;let s,u=Math.floor(l/36)/5*255,d=Math.floor((s=l%36)/6)/5*255,m=s%6/5*255;return[u,d,m]},o.rgb.hex=function(l){let s=(((Math.round(l[0])&255)<<16)+((Math.round(l[1])&255)<<8)+(Math.round(l[2])&255)).toString(16).toUpperCase();return"000000".substring(s.length)+s},o.hex.rgb=function(l){let s=l.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!s)return[0,0,0];let u=s[0];s[0].length===3&&(u=u.split("").map(h=>h+h).join(""));let d=parseInt(u,16),m=d>>16&255,f=d>>8&255,p=d&255;return[m,f,p]},o.rgb.hcg=function(l){let s=l[0]/255,u=l[1]/255,d=l[2]/255,m=Math.max(Math.max(s,u),d),f=Math.min(Math.min(s,u),d),p=m-f,h,g;return p<1?h=f/(1-p):h=0,p<=0?g=0:m===s?g=(u-d)/p%6:m===u?g=2+(d-s)/p:g=4+(s-u)/p,g/=6,g%=1,[g*360,p*100,h*100]},o.hsl.hcg=function(l){let s=l[1]/100,u=l[2]/100,d=u<.5?2*s*u:2*s*(1-u),m=0;return d<1&&(m=(u-.5*d)/(1-d)),[l[0],d*100,m*100]},o.hsv.hcg=function(l){let s=l[1]/100,u=l[2]/100,d=s*u,m=0;return d<1&&(m=(u-d)/(1-d)),[l[0],d*100,m*100]},o.hcg.rgb=function(l){let s=l[0]/360,u=l[1]/100,d=l[2]/100;if(u===0)return[d*255,d*255,d*255];let m=[0,0,0],f=s%1*6,p=f%1,h=1-p,g=0;switch(Math.floor(f)){case 0:m[0]=1,m[1]=p,m[2]=0;break;case 1:m[0]=h,m[1]=1,m[2]=0;break;case 2:m[0]=0,m[1]=1,m[2]=p;break;case 3:m[0]=0,m[1]=h,m[2]=1;break;case 4:m[0]=p,m[1]=0,m[2]=1;break;default:m[0]=1,m[1]=0,m[2]=h}return g=(1-u)*d,[(u*m[0]+g)*255,(u*m[1]+g)*255,(u*m[2]+g)*255]},o.hcg.hsv=function(l){let s=l[1]/100,u=l[2]/100,d=s+u*(1-s),m=0;return d>0&&(m=s/d),[l[0],m*100,d*100]},o.hcg.hsl=function(l){let s=l[1]/100,u=l[2]/100*(1-s)+.5*s,d=0;return u>0&&u<.5?d=s/(2*u):u>=.5&&u<1&&(d=s/(2*(1-u))),[l[0],d*100,u*100]},o.hcg.hwb=function(l){let s=l[1]/100,u=l[2]/100,d=s+u*(1-s);return[l[0],(d-s)*100,(1-d)*100]},o.hwb.hcg=function(l){let s=l[1]/100,u=1-l[2]/100,d=u-s,m=0;return d<1&&(m=(u-d)/(1-d)),[l[0],d*100,m*100]},o.apple.rgb=function(l){return[l[0]/65535*255,l[1]/65535*255,l[2]/65535*255]},o.rgb.apple=function(l){return[l[0]/255*65535,l[1]/255*65535,l[2]/255*65535]},o.gray.rgb=function(l){return[l[0]/100*255,l[0]/100*255,l[0]/100*255]},o.gray.hsl=function(l){return[0,0,l[0]]},o.gray.hsv=o.gray.hsl,o.gray.hwb=function(l){return[0,100,l[0]]},o.gray.cmyk=function(l){return[0,0,0,l[0]]},o.gray.lab=function(l){return[l[0],0,0]},o.gray.hex=function(l){let s=Math.round(l[0]/100*255)&255,u=((s<<16)+(s<<8)+s).toString(16).toUpperCase();return"000000".substring(u.length)+u},o.rgb.gray=function(l){return[(l[0]+l[1]+l[2])/3/255*100]}}),up=Y((e,t)=>{var r=Es();function a(){let s={},u=Object.keys(r);for(let d=u.length,m=0;m<d;m++)s[u[m]]={distance:-1,parent:null};return s}i(a,"buildGraph");function o(s){let u=a(),d=[s];for(u[s].distance=0;d.length;){let m=d.pop(),f=Object.keys(r[m]);for(let p=f.length,h=0;h<p;h++){let g=f[h],E=u[g];E.distance===-1&&(E.distance=u[m].distance+1,E.parent=m,d.unshift(g))}}return u}i(o,"deriveBFS");function c(s,u){return function(d){return u(s(d))}}i(c,"link");function l(s,u){let d=[u[s].parent,s],m=r[u[s].parent][s],f=u[s].parent;for(;u[f].parent;)d.unshift(u[f].parent),m=c(r[u[f].parent][f],m),f=u[f].parent;return m.conversion=d,m}i(l,"wrapConversion"),t.exports=function(s){let u=o(s),d={},m=Object.keys(u);for(let f=m.length,p=0;p<f;p++){let h=m[p];u[h].parent!==null&&(d[h]=l(h,u))}return d}}),cp=Y((e,t)=>{var r=Es(),a=up(),o={},c=Object.keys(r);function l(u){let d=i(function(...m){let f=m[0];return f==null?f:(f.length>1&&(m=f),u(m))},"wrappedFn");return"conversion"in u&&(d.conversion=u.conversion),d}i(l,"wrapRaw");function s(u){let d=i(function(...m){let f=m[0];if(f==null)return f;f.length>1&&(m=f);let p=u(m);if(typeof p=="object")for(let h=p.length,g=0;g<h;g++)p[g]=Math.round(p[g]);return p},"wrappedFn");return"conversion"in u&&(d.conversion=u.conversion),d}i(s,"wrapRounded"),c.forEach(u=>{o[u]={},Object.defineProperty(o[u],"channels",{value:r[u].channels}),Object.defineProperty(o[u],"labels",{value:r[u].labels});let d=a(u);Object.keys(d).forEach(m=>{let f=d[m];o[u][m]=s(f),o[u][m].raw=l(f)})}),t.exports=o});function rt(){return(rt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function wr(e,t){if(e==null)return{};var r,a,o={},c=Object.keys(e);for(a=0;a<c.length;a++)t.indexOf(r=c[a])>=0||(o[r]=e[r]);return o}function Mr(e){var t=X(e),r=X(function(a){t.current&&t.current(a)});return t.current=e,r.current}function On(e,t,r){var a=Mr(r),o=R(function(){return e.toHsva(t)}),c=o[0],l=o[1],s=X({color:t,hsva:c});j(function(){if(!e.equal(t,s.current.color)){var d=e.toHsva(t);s.current={hsva:d,color:t},l(d)}},[t,e]),j(function(){var d;na(c,s.current.hsva)||e.equal(d=e.fromHsva(c),s.current.color)||(s.current={hsva:c,color:d},a(d))},[c,e,a]);var u=$(function(d){l(function(m){return Object.assign({},m,d)})},[]);return[c,u]}var pt,wt,kr,Tn,Dn,Or,kt,Tr,le,ul,cl,Dr,dl,pl,ml,fl,In,Ir,Gt,_n,hl,Wt,gl,Bn,Rn,Nn,na,Fn,bl,dp,yl,El,Ln,Pn,vl,xl,vs,Al,jn,Cl,xs,Sl,As,pp=q(()=>{i(rt,"u"),i(wr,"c"),i(Mr,"i"),pt=i(function(e,t,r){return t===void 0&&(t=0),r===void 0&&(r=1),e>r?r:e<t?t:e},"s"),wt=i(function(e){return"touches"in e},"f"),kr=i(function(e){return e&&e.ownerDocument.defaultView||self},"v"),Tn=i(function(e,t,r){var a=e.getBoundingClientRect(),o=wt(t)?function(c,l){for(var s=0;s<c.length;s++)if(c[s].identifier===l)return c[s];return c[0]}(t.touches,r):t;return{left:pt((o.pageX-(a.left+kr(e).pageXOffset))/a.width),top:pt((o.pageY-(a.top+kr(e).pageYOffset))/a.height)}},"d"),Dn=i(function(e){!wt(e)&&e.preventDefault()},"h"),Or=n.memo(function(e){var t=e.onMove,r=e.onKey,a=wr(e,["onMove","onKey"]),o=X(null),c=Mr(t),l=Mr(r),s=X(null),u=X(!1),d=ce(function(){var h=i(function(y){Dn(y),(wt(y)?y.touches.length>0:y.buttons>0)&&o.current?c(Tn(o.current,y,s.current)):E(!1)},"e"),g=i(function(){return E(!1)},"r");function E(y){var v=u.current,C=kr(o.current),w=y?C.addEventListener:C.removeEventListener;w(v?"touchmove":"mousemove",h),w(v?"touchend":"mouseup",g)}return i(E,"t"),[function(y){var v=y.nativeEvent,C=o.current;if(C&&(Dn(v),!function(k,D){return D&&!wt(k)}(v,u.current)&&C)){if(wt(v)){u.current=!0;var w=v.changedTouches||[];w.length&&(s.current=w[0].identifier)}C.focus(),c(Tn(C,v,s.current)),E(!0)}},function(y){var v=y.which||y.keyCode;v<37||v>40||(y.preventDefault(),l({left:v===39?.05:v===37?-.05:0,top:v===40?.05:v===38?-.05:0}))},E]},[l,c]),m=d[0],f=d[1],p=d[2];return j(function(){return p},[p]),n.createElement("div",rt({},a,{onTouchStart:m,onMouseDown:m,className:"react-colorful__interactive",ref:o,onKeyDown:f,tabIndex:0,role:"slider"}))}),kt=i(function(e){return e.filter(Boolean).join(" ")},"g"),Tr=i(function(e){var t=e.color,r=e.left,a=e.top,o=a===void 0?.5:a,c=kt(["react-colorful__pointer",e.className]);return n.createElement("div",{className:c,style:{top:100*o+"%",left:100*r+"%"}},n.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:t}}))},"p"),le=i(function(e,t,r){return t===void 0&&(t=0),r===void 0&&(r=Math.pow(10,t)),Math.round(r*e)/r},"b"),ul={grad:.9,turn:360,rad:360/(2*Math.PI)},cl=i(function(e){return Bn(Dr(e))},"x"),Dr=i(function(e){return e[0]==="#"&&(e=e.substring(1)),e.length<6?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:e.length===4?le(parseInt(e[3]+e[3],16)/255,2):1}:{r:parseInt(e.substring(0,2),16),g:parseInt(e.substring(2,4),16),b:parseInt(e.substring(4,6),16),a:e.length===8?le(parseInt(e.substring(6,8),16)/255,2):1}},"C"),dl=i(function(e,t){return t===void 0&&(t="deg"),Number(e)*(ul[t]||1)},"E"),pl=i(function(e){var t=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?ml({h:dl(t[1],t[2]),s:Number(t[3]),l:Number(t[4]),a:t[5]===void 0?1:Number(t[5])/(t[6]?100:1)}):{h:0,s:0,v:0,a:1}},"H"),ml=i(function(e){var t=e.s,r=e.l;return{h:e.h,s:(t*=(r<50?r:100-r)/100)>0?2*t/(r+t)*100:0,v:r+t,a:e.a}},"N"),fl=i(function(e){return gl(_n(e))},"w"),In=i(function(e){var t=e.s,r=e.v,a=e.a,o=(200-t)*r/100;return{h:le(e.h),s:le(o>0&&o<200?t*r/100/(o<=100?o:200-o)*100:0),l:le(o/2),a:le(a,2)}},"y"),Ir=i(function(e){var t=In(e);return"hsl("+t.h+", "+t.s+"%, "+t.l+"%)"},"q"),Gt=i(function(e){var t=In(e);return"hsla("+t.h+", "+t.s+"%, "+t.l+"%, "+t.a+")"},"k"),_n=i(function(e){var t=e.h,r=e.s,a=e.v,o=e.a;t=t/360*6,r/=100,a/=100;var c=Math.floor(t),l=a*(1-r),s=a*(1-(t-c)*r),u=a*(1-(1-t+c)*r),d=c%6;return{r:le(255*[a,s,l,l,u,a][d]),g:le(255*[u,a,a,s,l,l][d]),b:le(255*[l,l,u,a,a,s][d]),a:le(o,2)}},"I"),hl=i(function(e){var t=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?Bn({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:t[7]===void 0?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}},"z"),Wt=i(function(e){var t=e.toString(16);return t.length<2?"0"+t:t},"D"),gl=i(function(e){var t=e.r,r=e.g,a=e.b,o=e.a,c=o<1?Wt(le(255*o)):"";return"#"+Wt(t)+Wt(r)+Wt(a)+c},"K"),Bn=i(function(e){var t=e.r,r=e.g,a=e.b,o=e.a,c=Math.max(t,r,a),l=c-Math.min(t,r,a),s=l?c===t?(r-a)/l:c===r?2+(a-t)/l:4+(t-r)/l:0;return{h:le(60*(s<0?s+6:s)),s:le(c?l/c*100:0),v:le(c/255*100),a:o}},"L"),Rn=n.memo(function(e){var t=e.hue,r=e.onChange,a=kt(["react-colorful__hue",e.className]);return n.createElement("div",{className:a},n.createElement(Or,{onMove:i(function(o){r({h:360*o.left})},"onMove"),onKey:i(function(o){r({h:pt(t+360*o.left,0,360)})},"onKey"),"aria-label":"Hue","aria-valuenow":le(t),"aria-valuemax":"360","aria-valuemin":"0"},n.createElement(Tr,{className:"react-colorful__hue-pointer",left:t/360,color:Ir({h:t,s:100,v:100,a:1})})))}),Nn=n.memo(function(e){var t=e.hsva,r=e.onChange,a={backgroundColor:Ir({h:t.h,s:100,v:100,a:1})};return n.createElement("div",{className:"react-colorful__saturation",style:a},n.createElement(Or,{onMove:i(function(o){r({s:100*o.left,v:100-100*o.top})},"onMove"),onKey:i(function(o){r({s:pt(t.s+100*o.left,0,100),v:pt(t.v-100*o.top,0,100)})},"onKey"),"aria-label":"Color","aria-valuetext":"Saturation "+le(t.s)+"%, Brightness "+le(t.v)+"%"},n.createElement(Tr,{className:"react-colorful__saturation-pointer",top:1-t.v/100,left:t.s/100,color:Ir(t)})))}),na=i(function(e,t){if(e===t)return!0;for(var r in e)if(e[r]!==t[r])return!1;return!0},"F"),Fn=i(function(e,t){return e.replace(/\s/g,"")===t.replace(/\s/g,"")},"P"),bl=i(function(e,t){return e.toLowerCase()===t.toLowerCase()||na(Dr(e),Dr(t))},"X"),i(On,"Y"),yl=typeof window<"u"?pr:j,El=i(function(){return dp||(typeof __webpack_nonce__<"u"?__webpack_nonce__:void 0)},"$"),Ln=new Map,Pn=i(function(e){yl(function(){var t=e.current?e.current.ownerDocument:document;if(t!==void 0&&!Ln.has(t)){var r=t.createElement("style");r.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,Ln.set(t,r);var a=El();a&&r.setAttribute("nonce",a),t.head.appendChild(r)}},[])},"Q"),vl=i(function(e){var t=e.className,r=e.colorModel,a=e.color,o=a===void 0?r.defaultColor:a,c=e.onChange,l=wr(e,["className","colorModel","color","onChange"]),s=X(null);Pn(s);var u=On(r,o,c),d=u[0],m=u[1],f=kt(["react-colorful",t]);return n.createElement("div",rt({},l,{ref:s,className:f}),n.createElement(Nn,{hsva:d,onChange:m}),n.createElement(Rn,{hue:d.h,onChange:m,className:"react-colorful__last-control"}))},"U"),xl={defaultColor:"000",toHsva:cl,fromHsva:i(function(e){return fl({h:e.h,s:e.s,v:e.v,a:1})},"fromHsva"),equal:bl},vs=i(function(e){return n.createElement(vl,rt({},e,{colorModel:xl}))},"Z"),Al=i(function(e){var t=e.className,r=e.hsva,a=e.onChange,o={backgroundImage:"linear-gradient(90deg, "+Gt(Object.assign({},r,{a:0}))+", "+Gt(Object.assign({},r,{a:1}))+")"},c=kt(["react-colorful__alpha",t]),l=le(100*r.a);return n.createElement("div",{className:c},n.createElement("div",{className:"react-colorful__alpha-gradient",style:o}),n.createElement(Or,{onMove:i(function(s){a({a:s.left})},"onMove"),onKey:i(function(s){a({a:pt(r.a+s.left)})},"onKey"),"aria-label":"Alpha","aria-valuetext":l+"%","aria-valuenow":l,"aria-valuemin":"0","aria-valuemax":"100"},n.createElement(Tr,{className:"react-colorful__alpha-pointer",left:r.a,color:Gt(r)})))},"ee"),jn=i(function(e){var t=e.className,r=e.colorModel,a=e.color,o=a===void 0?r.defaultColor:a,c=e.onChange,l=wr(e,["className","colorModel","color","onChange"]),s=X(null);Pn(s);var u=On(r,o,c),d=u[0],m=u[1],f=kt(["react-colorful",t]);return n.createElement("div",rt({},l,{ref:s,className:f}),n.createElement(Nn,{hsva:d,onChange:m}),n.createElement(Rn,{hue:d.h,onChange:m}),n.createElement(Al,{hsva:d,onChange:m,className:"react-colorful__last-control"}))},"re"),Cl={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:pl,fromHsva:Gt,equal:Fn},xs=i(function(e){return n.createElement(jn,rt({},e,{colorModel:Cl}))},"ue"),Sl={defaultColor:"rgba(0, 0, 0, 1)",toHsva:hl,fromHsva:i(function(e){var t=_n(e);return"rgba("+t.r+", "+t.g+", "+t.b+", "+t.a+")"},"fromHsva"),equal:Fn},As=i(function(e){return n.createElement(jn,rt({},e,{colorModel:Sl}))},"He")}),Cs={};Md(Cs,{ColorControl:()=>aa,default:()=>Ss});var we,wl,kl,Ol,Tl,Dl,Il,_l,Mn,Bl,Rl,Un,_r,Nl,Fl,Ll,Br,Pl,jl,Kt,$n,Ml,Ul,$l,mt,Hl,Vl,Yt,zl,aa,Ss,mp=q(()=>{"use strict";we=Ce(cp()),an(),pp(),je(),wl=b.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),kl=b(oe)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),Ol=b.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),Tl=b(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),Dl=b.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),Il=b.div(({theme:e,active:t})=>({width:16,height:16,boxShadow:t?`${e.appBorderColor} 0 0 0 1px inset, ${e.textMutedColor}50 0 0 0 4px`:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:e.appBorderRadius})),_l=`url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`,Mn=i(({value:e,style:t,...r})=>{let a=`linear-gradient(${e}, ${e}), ${_l}, linear-gradient(#fff, #fff)`;return n.createElement(Il,{...r,style:{...t,backgroundImage:a}})},"Swatch"),Bl=b(de.Input)(({theme:e,readOnly:t})=>({width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:e.typography.fonts.base})),Rl=b(ko)(({theme:e})=>({position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:e.input.color})),Un=(e=>(e.RGB="rgb",e.HSL="hsl",e.HEX="hex",e))(Un||{}),_r=Object.values(Un),Nl=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,Fl=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,Ll=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,Br=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,Pl=/^\s*#?([0-9a-f]{3})\s*$/i,jl={hex:vs,rgb:As,hsl:xs},Kt={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},$n=i(e=>{let t=e?.match(Nl);if(!t)return[0,0,0,1];let[,r,a,o,c=1]=t;return[r,a,o,c].map(Number)},"stringToArgs"),Ml=i(e=>{let[t,r,a,o]=$n(e),[c,l,s]=we.default.rgb.hsl([t,r,a])||[0,0,0];return{valid:!0,value:e,keyword:we.default.rgb.keyword([t,r,a]),colorSpace:"rgb",rgb:e,hsl:`hsla(${c}, ${l}%, ${s}%, ${o})`,hex:`#${we.default.rgb.hex([t,r,a]).toLowerCase()}`}},"parseRgb"),Ul=i(e=>{let[t,r,a,o]=$n(e),[c,l,s]=we.default.hsl.rgb([t,r,a])||[0,0,0];return{valid:!0,value:e,keyword:we.default.hsl.keyword([t,r,a]),colorSpace:"hsl",rgb:`rgba(${c}, ${l}, ${s}, ${o})`,hsl:e,hex:`#${we.default.hsl.hex([t,r,a]).toLowerCase()}`}},"parseHsl"),$l=i(e=>{let t=e.replace("#",""),r=we.default.keyword.rgb(t)||we.default.hex.rgb(t),a=we.default.rgb.hsl(r),o=e;/[^#a-f0-9]/i.test(e)?o=t:Br.test(e)&&(o=`#${t}`);let c=!0;if(o.startsWith("#"))c=Br.test(o);else try{we.default.keyword.hex(o)}catch{c=!1}return{valid:c,value:o,keyword:we.default.rgb.keyword(r),colorSpace:"hex",rgb:`rgba(${r[0]}, ${r[1]}, ${r[2]}, 1)`,hsl:`hsla(${a[0]}, ${a[1]}%, ${a[2]}%, 1)`,hex:o}},"parseHexOrKeyword"),mt=i(e=>{if(e)return Fl.test(e)?Ml(e):Ll.test(e)?Ul(e):$l(e)},"parseValue"),Hl=i((e,t,r)=>{if(!e||!t?.valid)return Kt[r];if(r!=="hex")return t?.[r]||Kt[r];if(!t.hex.startsWith("#"))try{return`#${we.default.keyword.hex(t.hex)}`}catch{return Kt.hex}let a=t.hex.match(Pl);if(!a)return Br.test(t.hex)?t.hex:Kt.hex;let[o,c,l]=a[1].split("");return`#${o}${o}${c}${c}${l}${l}`},"getRealValue"),Vl=i((e,t)=>{let[r,a]=R(e||""),[o,c]=R(()=>mt(r)),[l,s]=R(o?.colorSpace||"hex");j(()=>{let f=e||"",p=mt(f);a(f),c(p),s(p?.colorSpace||"hex")},[e]);let u=ce(()=>Hl(r,o,l).toLowerCase(),[r,o,l]),d=$(f=>{let p=mt(f),h=p?.value||f||"";a(h),h===""&&(c(void 0),t(void 0)),p&&(c(p),s(p.colorSpace),t(p.value))},[t]),m=$(()=>{let f=(_r.indexOf(l)+1)%_r.length,p=_r[f];s(p);let h=o?.[p]||"";a(h),t(h)},[o,l,t]);return{value:r,realValue:u,updateValue:d,color:o,colorSpace:l,cycleColorSpace:m}},"useColorInput"),Yt=i(e=>e.replace(/\s*/,"").toLowerCase(),"id"),zl=i((e,t,r)=>{let[a,o]=R(t?.valid?[t]:[]);j(()=>{t===void 0&&o([])},[t]);let c=ce(()=>(e||[]).map(s=>typeof s=="string"?mt(s):s.title?{...mt(s.color),keyword:s.title}:mt(s.color)).concat(a).filter(Boolean).slice(-27),[e,a]),l=$(s=>{s?.valid&&(c.some(u=>u&&u[r]&&Yt(u[r]||"")===Yt(s[r]||""))||o(u=>u.concat(s)))},[r,c]);return{presets:c,addPreset:l}},"usePresets"),aa=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,presetColors:c,startOpen:l=!1,argType:s})=>{let u=$(us(r,200),[r]),{value:d,realValue:m,updateValue:f,color:p,colorSpace:h,cycleColorSpace:g}=Vl(t,u),{presets:E,addPreset:y}=zl(c??[],p,h),v=jl[h],C=!!s?.table?.readonly;return n.createElement(wl,{"aria-readonly":C},n.createElement(kl,{startOpen:l,trigger:C?null:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>p&&y(p),tooltip:n.createElement(Ol,null,n.createElement(v,{color:m==="transparent"?"#000000":m,onChange:f,onFocus:a,onBlur:o}),E.length>0&&n.createElement(Dl,null,E.map((w,k)=>n.createElement(oe,{key:`${w?.value||k}-${k}`,hasChrome:!1,tooltip:n.createElement(Tl,{note:w?.keyword||w?.value||""})},n.createElement(Mn,{value:w?.[h]||"",active:!!(p&&w&&w[h]&&Yt(w[h]||"")===Yt(p[h])),onClick:()=>w&&f(w.value||"")})))))},n.createElement(Mn,{value:m,style:{margin:4}})),n.createElement(Bl,{id:Oe(e),value:d,onChange:w=>f(w.target.value),onFocus:w=>w.target.select(),readOnly:C,placeholder:"Choose color..."}),d?n.createElement(Rl,{onClick:g}):null)},"ColorControl"),Ss=aa}),fp=Y((e,t)=>{(function(r){if(typeof e=="object"&&typeof t<"u")t.exports=r();else if(typeof define=="function"&&define.amd)define([],r);else{var a;typeof window<"u"||typeof window<"u"?a=window:typeof self<"u"?a=self:a=this,a.memoizerific=r()}})(function(){var r,a,o;return i(function c(l,s,u){function d(p,h){if(!s[p]){if(!l[p]){var g=typeof Sr=="function"&&Sr;if(!h&&g)return g(p,!0);if(m)return m(p,!0);var E=new Error("Cannot find module '"+p+"'");throw E.code="MODULE_NOT_FOUND",E}var y=s[p]={exports:{}};l[p][0].call(y.exports,function(v){var C=l[p][1][v];return d(C||v)},y,y.exports,c,l,s,u)}return s[p].exports}i(d,"s");for(var m=typeof Sr=="function"&&Sr,f=0;f<u.length;f++)d(u[f]);return d},"e")({1:[function(c,l,s){l.exports=function(u){if(typeof Map!="function"||u){var d=c("./similar");return new d}else return new Map}},{"./similar":2}],2:[function(c,l,s){function u(){return this.list=[],this.lastItem=void 0,this.size=0,this}i(u,"Similar"),u.prototype.get=function(d){var m;if(this.lastItem&&this.isEqual(this.lastItem.key,d))return this.lastItem.val;if(m=this.indexOf(d),m>=0)return this.lastItem=this.list[m],this.list[m].val},u.prototype.set=function(d,m){var f;return this.lastItem&&this.isEqual(this.lastItem.key,d)?(this.lastItem.val=m,this):(f=this.indexOf(d),f>=0?(this.lastItem=this.list[f],this.list[f].val=m,this):(this.lastItem={key:d,val:m},this.list.push(this.lastItem),this.size++,this))},u.prototype.delete=function(d){var m;if(this.lastItem&&this.isEqual(this.lastItem.key,d)&&(this.lastItem=void 0),m=this.indexOf(d),m>=0)return this.size--,this.list.splice(m,1)[0]},u.prototype.has=function(d){var m;return this.lastItem&&this.isEqual(this.lastItem.key,d)?!0:(m=this.indexOf(d),m>=0?(this.lastItem=this.list[m],!0):!1)},u.prototype.forEach=function(d,m){var f;for(f=0;f<this.size;f++)d.call(m||this,this.list[f].val,this.list[f].key,this)},u.prototype.indexOf=function(d){var m;for(m=0;m<this.size;m++)if(this.isEqual(this.list[m].key,d))return m;return-1},u.prototype.isEqual=function(d,m){return d===m||d!==d&&m!==m},l.exports=u},{}],3:[function(c,l,s){var u=c("map-or-similar");l.exports=function(p){var h=new u(!1),g=[];return function(E){var y=i(function(){var v=h,C,w,k=arguments.length-1,D=Array(k+1),T=!0,_;if((y.numArgs||y.numArgs===0)&&y.numArgs!==k+1)throw new Error("Memoizerific functions should always be called with the same number of arguments");for(_=0;_<k;_++){if(D[_]={cacheItem:v,arg:arguments[_]},v.has(arguments[_])){v=v.get(arguments[_]);continue}T=!1,C=new u(!1),v.set(arguments[_],C),v=C}return T&&(v.has(arguments[k])?w=v.get(arguments[k]):T=!1),T||(w=E.apply(null,arguments),v.set(arguments[k],w)),p>0&&(D[k]={cacheItem:v,arg:arguments[k]},T?d(g,D):g.push(D),g.length>p&&m(g.shift())),y.wasMemoized=T,y.numArgs=k+1,w},"memoizerific");return y.limit=p,y.wasMemoized=!1,y.cache=h,y.lru=g,y}};function d(p,h){var g=p.length,E=h.length,y,v,C;for(v=0;v<g;v++){for(y=!0,C=0;C<E;C++)if(!f(p[v][C].arg,h[C].arg)){y=!1;break}if(y)break}p.push(p.splice(v,1)[0])}i(d,"moveToMostRecentLru");function m(p){var h=p.length,g=p[h-1],E,y;for(g.cacheItem.delete(g.arg),y=h-2;y>=0&&(g=p[y],E=g.cacheItem.get(g.arg),!E||!E.size);y--)g.cacheItem.delete(g.arg)}i(m,"removeCachedResult");function f(p,h){return p===h||p!==p&&h!==h}i(f,"isEqual")},{"map-or-similar":1}]},{},[3])(3)})}),ws=Y((e,t)=>{t.exports={Aacute:"\xC1",aacute:"\xE1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223E",acd:"\u223F",acE:"\u223E\u0333",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",Acy:"\u0410",acy:"\u0430",AElig:"\xC6",aelig:"\xE6",af:"\u2061",Afr:"\u{1D504}",afr:"\u{1D51E}",Agrave:"\xC0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03B1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2A3F",amp:"&",AMP:"&",andand:"\u2A55",And:"\u2A53",and:"\u2227",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angmsd:"\u2221",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1D538}",aopf:"\u{1D552}",apacir:"\u2A6F",ap:"\u2248",apE:"\u2A70",ape:"\u224A",apid:"\u224B",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224A",Aring:"\xC5",aring:"\xE5",Ascr:"\u{1D49C}",ascr:"\u{1D4B6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224D",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",Backslash:"\u2216",Barv:"\u2AE7",barvee:"\u22BD",barwed:"\u2305",Barwed:"\u2306",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",Because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",Bernoullis:"\u212C",Beta:"\u0392",beta:"\u03B2",beth:"\u2136",between:"\u226C",Bfr:"\u{1D505}",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bNot:"\u2AED",bnot:"\u2310",Bopf:"\u{1D539}",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxbox:"\u29C9",boxdl:"\u2510",boxdL:"\u2555",boxDl:"\u2556",boxDL:"\u2557",boxdr:"\u250C",boxdR:"\u2552",boxDr:"\u2553",boxDR:"\u2554",boxh:"\u2500",boxH:"\u2550",boxhd:"\u252C",boxHd:"\u2564",boxhD:"\u2565",boxHD:"\u2566",boxhu:"\u2534",boxHu:"\u2567",boxhU:"\u2568",boxHU:"\u2569",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxul:"\u2518",boxuL:"\u255B",boxUl:"\u255C",boxUL:"\u255D",boxur:"\u2514",boxuR:"\u2558",boxUr:"\u2559",boxUR:"\u255A",boxv:"\u2502",boxV:"\u2551",boxvh:"\u253C",boxvH:"\u256A",boxVh:"\u256B",boxVH:"\u256C",boxvl:"\u2524",boxvL:"\u2561",boxVl:"\u2562",boxVL:"\u2563",boxvr:"\u251C",boxvR:"\u255E",boxVr:"\u255F",boxVR:"\u2560",bprime:"\u2035",breve:"\u02D8",Breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",Bscr:"\u212C",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsolb:"\u29C5",bsol:"\\",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",Bumpeq:"\u224E",bumpeq:"\u224F",Cacute:"\u0106",cacute:"\u0107",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",cap:"\u2229",Cap:"\u22D2",capcup:"\u2A47",capdot:"\u2A40",CapitalDifferentialD:"\u2145",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",Cayleys:"\u212D",ccaps:"\u2A4D",Ccaron:"\u010C",ccaron:"\u010D",Ccedil:"\xC7",ccedil:"\xE7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2A4C",ccupssm:"\u2A50",Cdot:"\u010A",cdot:"\u010B",cedil:"\xB8",Cedilla:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",CenterDot:"\xB7",cfr:"\u{1D520}",Cfr:"\u212D",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03A7",chi:"\u03C7",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",CircleDot:"\u2299",circledR:"\xAE",circledS:"\u24C8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cir:"\u25CB",cirE:"\u29C3",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",colon:":",Colon:"\u2237",Colone:"\u2A74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",Congruent:"\u2261",conint:"\u222E",Conint:"\u222F",ContourIntegral:"\u222E",copf:"\u{1D554}",Copf:"\u2102",coprod:"\u2210",Coproduct:"\u2210",copy:"\xA9",COPY:"\xA9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21B5",cross:"\u2717",Cross:"\u2A2F",Cscr:"\u{1D49E}",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cupbrcap:"\u2A48",cupcap:"\u2A46",CupCap:"\u224D",cup:"\u222A",Cup:"\u22D3",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dagger:"\u2020",Dagger:"\u2021",daleth:"\u2138",darr:"\u2193",Darr:"\u21A1",dArr:"\u21D3",dash:"\u2010",Dashv:"\u2AE4",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",Dcaron:"\u010E",dcaron:"\u010F",Dcy:"\u0414",dcy:"\u0434",ddagger:"\u2021",ddarr:"\u21CA",DD:"\u2145",dd:"\u2146",DDotrahd:"\u2911",ddotseq:"\u2A77",deg:"\xB0",Del:"\u2207",Delta:"\u0394",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",Dfr:"\u{1D507}",dfr:"\u{1D521}",dHar:"\u2965",dharl:"\u21C3",dharr:"\u21C2",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",diam:"\u22C4",diamond:"\u22C4",Diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",DifferentialD:"\u2146",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",Dopf:"\u{1D53B}",dopf:"\u{1D555}",Dot:"\xA8",dot:"\u02D9",DotDot:"\u20DC",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrowBar:"\u2913",downarrow:"\u2193",DownArrow:"\u2193",Downarrow:"\u21D3",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVectorBar:"\u2956",DownLeftVector:"\u21BD",DownRightTeeVector:"\u295F",DownRightVectorBar:"\u2957",DownRightVector:"\u21C1",DownTeeArrow:"\u21A7",DownTee:"\u22A4",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",Dscr:"\u{1D49F}",dscr:"\u{1D4B9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29F6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",DZcy:"\u040F",dzcy:"\u045F",dzigrarr:"\u27FF",Eacute:"\xC9",eacute:"\xE9",easter:"\u2A6E",Ecaron:"\u011A",ecaron:"\u011B",Ecirc:"\xCA",ecirc:"\xEA",ecir:"\u2256",ecolon:"\u2255",Ecy:"\u042D",ecy:"\u044D",eDDot:"\u2A77",Edot:"\u0116",edot:"\u0117",eDot:"\u2251",ee:"\u2147",efDot:"\u2252",Efr:"\u{1D508}",efr:"\u{1D522}",eg:"\u2A9A",Egrave:"\xC8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",Element:"\u2208",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25FB",emptyv:"\u2205",EmptyVerySmallSquare:"\u25AB",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",ENG:"\u014A",eng:"\u014B",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1D53C}",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",Epsilon:"\u0395",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",Equal:"\u2A75",equals:"=",EqualTilde:"\u2242",equest:"\u225F",Equilibrium:"\u21CC",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erarr:"\u2971",erDot:"\u2253",escr:"\u212F",Escr:"\u2130",esdot:"\u2250",Esim:"\u2A73",esim:"\u2242",Eta:"\u0397",eta:"\u03B7",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",exponentiale:"\u2147",ExponentialE:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",Ffr:"\u{1D509}",ffr:"\u{1D523}",filig:"\uFB01",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",Fopf:"\u{1D53D}",fopf:"\u{1D557}",forall:"\u2200",ForAll:"\u2200",fork:"\u22D4",forkv:"\u2AD9",Fouriertrf:"\u2131",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",Fscr:"\u2131",gacute:"\u01F5",Gamma:"\u0393",gamma:"\u03B3",Gammad:"\u03DC",gammad:"\u03DD",gap:"\u2A86",Gbreve:"\u011E",gbreve:"\u011F",Gcedil:"\u0122",Gcirc:"\u011C",gcirc:"\u011D",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",ge:"\u2265",gE:"\u2267",gEl:"\u2A8C",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",gescc:"\u2AA9",ges:"\u2A7E",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",Gfr:"\u{1D50A}",gfr:"\u{1D524}",gg:"\u226B",Gg:"\u22D9",ggg:"\u22D9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gla:"\u2AA5",gl:"\u2277",glE:"\u2A92",glj:"\u2AA4",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gnE:"\u2269",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",Gopf:"\u{1D53E}",gopf:"\u{1D558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gtcc:"\u2AA7",gtcir:"\u2A7A",gt:">",GT:">",Gt:"\u226B",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",Hacek:"\u02C7",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",HARDcy:"\u042A",hardcy:"\u044A",harrcir:"\u2948",harr:"\u2194",hArr:"\u21D4",harrw:"\u21AD",Hat:"^",hbar:"\u210F",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",Hfr:"\u210C",HilbertSpace:"\u210B",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",Hopf:"\u210D",horbar:"\u2015",HorizontalLine:"\u2500",hscr:"\u{1D4BD}",Hscr:"\u210B",hslash:"\u210F",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224E",HumpEqual:"\u224F",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xCD",iacute:"\xED",ic:"\u2063",Icirc:"\xCE",icirc:"\xEE",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",Ifr:"\u2111",Igrave:"\xCC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Imacr:"\u012A",imacr:"\u012B",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",Im:"\u2111",imof:"\u22B7",imped:"\u01B5",Implies:"\u21D2",incare:"\u2105",in:"\u2208",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",intcal:"\u22BA",int:"\u222B",Int:"\u222C",integers:"\u2124",Integral:"\u222B",intercal:"\u22BA",Intersection:"\u22C2",intlarhk:"\u2A17",intprod:"\u2A3C",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012E",iogon:"\u012F",Iopf:"\u{1D540}",iopf:"\u{1D55A}",Iota:"\u0399",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",Iscr:"\u2110",isin:"\u2208",isindot:"\u22F5",isinE:"\u22F9",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xCF",iuml:"\xEF",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1D50D}",jfr:"\u{1D527}",jmath:"\u0237",Jopf:"\u{1D541}",jopf:"\u{1D55B}",Jscr:"\u{1D4A5}",jscr:"\u{1D4BF}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039A",kappa:"\u03BA",kappav:"\u03F0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041A",kcy:"\u043A",Kfr:"\u{1D50E}",kfr:"\u{1D528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040C",kjcy:"\u045C",Kopf:"\u{1D542}",kopf:"\u{1D55C}",Kscr:"\u{1D4A6}",kscr:"\u{1D4C0}",lAarr:"\u21DA",Lacute:"\u0139",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",Lambda:"\u039B",lambda:"\u03BB",lang:"\u27E8",Lang:"\u27EA",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",Laplacetrf:"\u2112",laquo:"\xAB",larrb:"\u21E4",larrbfs:"\u291F",larr:"\u2190",Larr:"\u219E",lArr:"\u21D0",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",latail:"\u2919",lAtail:"\u291B",lat:"\u2AAB",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lBarr:"\u290E",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",Lcaron:"\u013D",lcaron:"\u013E",Lcedil:"\u013B",lcedil:"\u013C",lceil:"\u2308",lcub:"{",Lcy:"\u041B",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",lE:"\u2266",LeftAngleBracket:"\u27E8",LeftArrowBar:"\u21E4",leftarrow:"\u2190",LeftArrow:"\u2190",Leftarrow:"\u21D0",LeftArrowRightArrow:"\u21C6",leftarrowtail:"\u21A2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVectorBar:"\u2959",LeftDownVector:"\u21C3",LeftFloor:"\u230A",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",LeftRightArrow:"\u2194",Leftrightarrow:"\u21D4",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",LeftRightVector:"\u294E",LeftTeeArrow:"\u21A4",LeftTee:"\u22A3",LeftTeeVector:"\u295A",leftthreetimes:"\u22CB",LeftTriangleBar:"\u29CF",LeftTriangle:"\u22B2",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVectorBar:"\u2958",LeftUpVector:"\u21BF",LeftVectorBar:"\u2952",LeftVector:"\u21BC",lEg:"\u2A8B",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",lescc:"\u2AA8",les:"\u2A7D",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2AA1",lesssim:"\u2272",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",lfisht:"\u297C",lfloor:"\u230A",Lfr:"\u{1D50F}",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lHar:"\u2962",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",llarr:"\u21C7",ll:"\u226A",Ll:"\u22D8",llcorner:"\u231E",Lleftarrow:"\u21DA",llhard:"\u296B",lltri:"\u25FA",Lmidot:"\u013F",lmidot:"\u0140",lmoustache:"\u23B0",lmoust:"\u23B0",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lnE:"\u2268",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",LongLeftArrow:"\u27F5",Longleftarrow:"\u27F8",longleftrightarrow:"\u27F7",LongLeftRightArrow:"\u27F7",Longleftrightarrow:"\u27FA",longmapsto:"\u27FC",longrightarrow:"\u27F6",LongRightArrow:"\u27F6",Longrightarrow:"\u27F9",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",Lopf:"\u{1D543}",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",Lscr:"\u2112",lsh:"\u21B0",Lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",Lstrok:"\u0141",lstrok:"\u0142",ltcc:"\u2AA6",ltcir:"\u2A79",lt:"<",LT:"<",Lt:"\u226A",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",ltrPar:"\u2996",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",Mcy:"\u041C",mcy:"\u043C",mdash:"\u2014",mDDot:"\u223A",measuredangle:"\u2221",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",midast:"*",midcir:"\u2AF0",mid:"\u2223",middot:"\xB7",minusb:"\u229F",minus:"\u2212",minusd:"\u2238",minusdu:"\u2A2A",MinusPlus:"\u2213",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",Mopf:"\u{1D544}",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",Mscr:"\u2133",mstpos:"\u223E",Mu:"\u039C",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266E",naturals:"\u2115",natur:"\u266E",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",Ncy:"\u041D",ncy:"\u043D",ndash:"\u2013",nearhk:"\u2924",nearr:"\u2197",neArr:"\u21D7",nearrow:"\u2197",ne:"\u2260",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1D511}",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",nGg:"\u22D9\u0338",ngsim:"\u2275",nGt:"\u226B\u20D2",ngt:"\u226F",ngtr:"\u226F",nGtv:"\u226B\u0338",nharr:"\u21AE",nhArr:"\u21CE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",NJcy:"\u040A",njcy:"\u045A",nlarr:"\u219A",nlArr:"\u21CD",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nleftarrow:"\u219A",nLeftarrow:"\u21CD",nleftrightarrow:"\u21AE",nLeftrightarrow:"\u21CE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nLl:"\u22D8\u0338",nlsim:"\u2274",nLt:"\u226A\u20D2",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nLtv:"\u226A\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xA0",nopf:"\u{1D55F}",Nopf:"\u2115",Not:"\u2AEC",not:"\xAC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",notin:"\u2209",notindot:"\u22F5\u0338",notinE:"\u22F9\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangle:"\u22EB",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",nparallel:"\u2226",npar:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",nprec:"\u2280",npreceq:"\u2AAF\u0338",npre:"\u2AAF\u0338",nrarrc:"\u2933\u0338",nrarr:"\u219B",nrArr:"\u21CF",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nRightarrow:"\u21CF",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",Nscr:"\u{1D4A9}",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",Ntilde:"\xD1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",Nu:"\u039D",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvDash:"\u22AD",nVdash:"\u22AE",nVDash:"\u22AF",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvHarr:"\u2904",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwarhk:"\u2923",nwarr:"\u2196",nwArr:"\u21D6",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xD3",oacute:"\xF3",oast:"\u229B",Ocirc:"\xD4",ocirc:"\xF4",ocir:"\u229A",Ocy:"\u041E",ocy:"\u043E",odash:"\u229D",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29BF",Ofr:"\u{1D512}",ofr:"\u{1D52C}",ogon:"\u02DB",Ograve:"\xD2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",Omacr:"\u014C",omacr:"\u014D",Omega:"\u03A9",omega:"\u03C9",Omicron:"\u039F",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",Oopf:"\u{1D546}",oopf:"\u{1D560}",opar:"\u29B7",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",operp:"\u29B9",oplus:"\u2295",orarr:"\u21BB",Or:"\u2A54",or:"\u2228",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oS:"\u24C8",Oscr:"\u{1D4AA}",oscr:"\u2134",Oslash:"\xD8",oslash:"\xF8",osol:"\u2298",Otilde:"\xD5",otilde:"\xF5",otimesas:"\u2A36",Otimes:"\u2A37",otimes:"\u2297",Ouml:"\xD6",ouml:"\xF6",ovbar:"\u233D",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",para:"\xB6",parallel:"\u2225",par:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",PartialD:"\u2202",Pcy:"\u041F",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",Pfr:"\u{1D513}",pfr:"\u{1D52D}",Phi:"\u03A6",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",Pi:"\u03A0",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plus:"+",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",PlusMinus:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",Poincareplane:"\u210C",pointint:"\u2A15",popf:"\u{1D561}",Popf:"\u2119",pound:"\xA3",prap:"\u2AB7",Pr:"\u2ABB",pr:"\u227A",prcue:"\u227C",precapprox:"\u2AB7",prec:"\u227A",preccurlyeq:"\u227C",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",pre:"\u2AAF",prE:"\u2AB3",precsim:"\u227E",prime:"\u2032",Prime:"\u2033",primes:"\u2119",prnap:"\u2AB9",prnE:"\u2AB5",prnsim:"\u22E8",prod:"\u220F",Product:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",Proportional:"\u221D",Proportion:"\u2237",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",Pscr:"\u{1D4AB}",pscr:"\u{1D4C5}",Psi:"\u03A8",psi:"\u03C8",puncsp:"\u2008",Qfr:"\u{1D514}",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",Qopf:"\u211A",qprime:"\u2057",Qscr:"\u{1D4AC}",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',QUOT:'"',rAarr:"\u21DB",race:"\u223D\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",Rang:"\u27EB",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarr:"\u2192",Rarr:"\u21A0",rArr:"\u21D2",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",rAtail:"\u291C",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rBarr:"\u290F",RBarr:"\u2910",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",Re:"\u211C",rect:"\u25AD",reg:"\xAE",REG:"\xAE",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",Rfr:"\u211C",rHar:"\u2964",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",Rho:"\u03A1",rho:"\u03C1",rhov:"\u03F1",RightAngleBracket:"\u27E9",RightArrowBar:"\u21E5",rightarrow:"\u2192",RightArrow:"\u2192",Rightarrow:"\u21D2",RightArrowLeftArrow:"\u21C4",rightarrowtail:"\u21A3",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVectorBar:"\u2955",RightDownVector:"\u21C2",RightFloor:"\u230B",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",RightTeeArrow:"\u21A6",RightTee:"\u22A2",RightTeeVector:"\u295B",rightthreetimes:"\u22CC",RightTriangleBar:"\u29D0",RightTriangle:"\u22B3",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVectorBar:"\u2954",RightUpVector:"\u21BE",RightVectorBar:"\u2953",RightVector:"\u21C0",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoustache:"\u23B1",rmoust:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",Ropf:"\u211D",roplus:"\u2A2E",rotimes:"\u2A35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",Rrightarrow:"\u21DB",rsaquo:"\u203A",rscr:"\u{1D4C7}",Rscr:"\u211B",rsh:"\u21B1",Rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",RuleDelayed:"\u29F4",ruluhar:"\u2968",rx:"\u211E",Sacute:"\u015A",sacute:"\u015B",sbquo:"\u201A",scap:"\u2AB8",Scaron:"\u0160",scaron:"\u0161",Sc:"\u2ABC",sc:"\u227B",sccue:"\u227D",sce:"\u2AB0",scE:"\u2AB4",Scedil:"\u015E",scedil:"\u015F",Scirc:"\u015C",scirc:"\u015D",scnap:"\u2ABA",scnE:"\u2AB6",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",Scy:"\u0421",scy:"\u0441",sdotb:"\u22A1",sdot:"\u22C5",sdote:"\u2A66",searhk:"\u2925",searr:"\u2198",seArr:"\u21D8",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1D516}",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xAD",Sigma:"\u03A3",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",SOFTcy:"\u042C",softcy:"\u044C",solbar:"\u233F",solb:"\u29C4",sol:"/",Sopf:"\u{1D54A}",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",Sqrt:"\u221A",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",square:"\u25A1",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25AA",squ:"\u25A1",squf:"\u25AA",srarr:"\u2192",Sscr:"\u{1D4AE}",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",Star:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",Sub:"\u22D0",subdot:"\u2ABD",subE:"\u2AC5",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",Subset:"\u22D0",subseteq:"\u2286",subseteqq:"\u2AC5",SubsetEqual:"\u2286",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succapprox:"\u2AB8",succ:"\u227B",succcurlyeq:"\u227D",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",SuchThat:"\u220B",sum:"\u2211",Sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",Sup:"\u22D1",supdot:"\u2ABE",supdsub:"\u2AD8",supE:"\u2AC6",supe:"\u2287",supedot:"\u2AC4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",Supset:"\u22D1",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swarhk:"\u2926",swarr:"\u2199",swArr:"\u21D9",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",Tab:"	",target:"\u2316",Tau:"\u03A4",tau:"\u03C4",tbrk:"\u23B4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",Tfr:"\u{1D517}",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",Therefore:"\u2234",Theta:"\u0398",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",THORN:"\xDE",thorn:"\xFE",tilde:"\u02DC",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",timesbar:"\u2A31",timesb:"\u22A0",times:"\xD7",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",topbot:"\u2336",topcir:"\u2AF1",top:"\u22A4",Topf:"\u{1D54B}",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",TRADE:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",TripleDot:"\u20DB",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",Tscr:"\u{1D4AF}",tscr:"\u{1D4C9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040B",tshcy:"\u045B",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",Uacute:"\xDA",uacute:"\xFA",uarr:"\u2191",Uarr:"\u219F",uArr:"\u21D1",Uarrocir:"\u2949",Ubrcy:"\u040E",ubrcy:"\u045E",Ubreve:"\u016C",ubreve:"\u016D",Ucirc:"\xDB",ucirc:"\xFB",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21C5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",Ufr:"\u{1D518}",ufr:"\u{1D532}",Ugrave:"\xD9",ugrave:"\xF9",uHar:"\u2963",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",Umacr:"\u016A",umacr:"\u016B",uml:"\xA8",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1D54C}",uopf:"\u{1D566}",UpArrowBar:"\u2912",uparrow:"\u2191",UpArrow:"\u2191",Uparrow:"\u21D1",UpArrowDownArrow:"\u21C5",updownarrow:"\u2195",UpDownArrow:"\u2195",Updownarrow:"\u21D5",UpEquilibrium:"\u296E",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",upsi:"\u03C5",Upsi:"\u03D2",upsih:"\u03D2",Upsilon:"\u03A5",upsilon:"\u03C5",UpTeeArrow:"\u21A5",UpTee:"\u22A5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",Uring:"\u016E",uring:"\u016F",urtri:"\u25F9",Uscr:"\u{1D4B0}",uscr:"\u{1D4CA}",utdot:"\u22F0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",Uuml:"\xDC",uuml:"\xFC",uwangle:"\u29A7",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",vArr:"\u21D5",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vBar:"\u2AE8",Vbar:"\u2AEB",vBarv:"\u2AE9",Vcy:"\u0412",vcy:"\u0432",vdash:"\u22A2",vDash:"\u22A8",Vdash:"\u22A9",VDash:"\u22AB",Vdashl:"\u2AE6",veebar:"\u22BB",vee:"\u2228",Vee:"\u22C1",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",Verbar:"\u2016",vert:"|",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",Vopf:"\u{1D54D}",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",Vscr:"\u{1D4B1}",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",Vvdash:"\u22AA",vzigzag:"\u299A",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",Wedge:"\u22C0",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1D51A}",wfr:"\u{1D534}",Wopf:"\u{1D54E}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1D4B2}",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",Xfr:"\u{1D51B}",xfr:"\u{1D535}",xharr:"\u27F7",xhArr:"\u27FA",Xi:"\u039E",xi:"\u03BE",xlarr:"\u27F5",xlArr:"\u27F8",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",Xopf:"\u{1D54F}",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrarr:"\u27F6",xrArr:"\u27F9",Xscr:"\u{1D4B3}",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",Yacute:"\xDD",yacute:"\xFD",YAcy:"\u042F",yacy:"\u044F",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042B",ycy:"\u044B",yen:"\xA5",Yfr:"\u{1D51C}",yfr:"\u{1D536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1D550}",yopf:"\u{1D56A}",Yscr:"\u{1D4B4}",yscr:"\u{1D4CE}",YUcy:"\u042E",yucy:"\u044E",yuml:"\xFF",Yuml:"\u0178",Zacute:"\u0179",zacute:"\u017A",Zcaron:"\u017D",zcaron:"\u017E",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017B",zdot:"\u017C",zeetrf:"\u2128",ZeroWidthSpace:"\u200B",Zeta:"\u0396",zeta:"\u03B6",zfr:"\u{1D537}",Zfr:"\u2128",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",Zopf:"\u2124",Zscr:"\u{1D4B5}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}}),hp=Y((e,t)=>{t.exports={Aacute:"\xC1",aacute:"\xE1",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",AElig:"\xC6",aelig:"\xE6",Agrave:"\xC0",agrave:"\xE0",amp:"&",AMP:"&",Aring:"\xC5",aring:"\xE5",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",brvbar:"\xA6",Ccedil:"\xC7",ccedil:"\xE7",cedil:"\xB8",cent:"\xA2",copy:"\xA9",COPY:"\xA9",curren:"\xA4",deg:"\xB0",divide:"\xF7",Eacute:"\xC9",eacute:"\xE9",Ecirc:"\xCA",ecirc:"\xEA",Egrave:"\xC8",egrave:"\xE8",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",frac12:"\xBD",frac14:"\xBC",frac34:"\xBE",gt:">",GT:">",Iacute:"\xCD",iacute:"\xED",Icirc:"\xCE",icirc:"\xEE",iexcl:"\xA1",Igrave:"\xCC",igrave:"\xEC",iquest:"\xBF",Iuml:"\xCF",iuml:"\xEF",laquo:"\xAB",lt:"<",LT:"<",macr:"\xAF",micro:"\xB5",middot:"\xB7",nbsp:"\xA0",not:"\xAC",Ntilde:"\xD1",ntilde:"\xF1",Oacute:"\xD3",oacute:"\xF3",Ocirc:"\xD4",ocirc:"\xF4",Ograve:"\xD2",ograve:"\xF2",ordf:"\xAA",ordm:"\xBA",Oslash:"\xD8",oslash:"\xF8",Otilde:"\xD5",otilde:"\xF5",Ouml:"\xD6",ouml:"\xF6",para:"\xB6",plusmn:"\xB1",pound:"\xA3",quot:'"',QUOT:'"',raquo:"\xBB",reg:"\xAE",REG:"\xAE",sect:"\xA7",shy:"\xAD",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",szlig:"\xDF",THORN:"\xDE",thorn:"\xFE",times:"\xD7",Uacute:"\xDA",uacute:"\xFA",Ucirc:"\xDB",ucirc:"\xFB",Ugrave:"\xD9",ugrave:"\xF9",uml:"\xA8",Uuml:"\xDC",uuml:"\xFC",Yacute:"\xDD",yacute:"\xFD",yen:"\xA5",yuml:"\xFF"}}),ks=Y((e,t)=>{t.exports={amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}}),gp=Y((e,t)=>{t.exports={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376}}),bp=Y(e=>{"use strict";var t=e&&e.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(e,"__esModule",{value:!0});var r=t(gp()),a=String.fromCodePoint||function(c){var l="";return c>65535&&(c-=65536,l+=String.fromCharCode(c>>>10&1023|55296),c=56320|c&1023),l+=String.fromCharCode(c),l};function o(c){return c>=55296&&c<=57343||c>1114111?"\uFFFD":(c in r.default&&(c=r.default[c]),a(c))}i(o,"decodeCodePoint"),e.default=o}),ql=Y(e=>{"use strict";var t=e&&e.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(e,"__esModule",{value:!0}),e.decodeHTML=e.decodeHTMLStrict=e.decodeXML=void 0;var r=t(ws()),a=t(hp()),o=t(ks()),c=t(bp()),l=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;e.decodeXML=s(o.default),e.decodeHTMLStrict=s(r.default);function s(m){var f=d(m);return function(p){return String(p).replace(l,f)}}i(s,"getStrictDecoder");var u=i(function(m,f){return m<f?1:-1},"sorter");e.decodeHTML=function(){for(var m=Object.keys(a.default).sort(u),f=Object.keys(r.default).sort(u),p=0,h=0;p<f.length;p++)m[h]===f[p]?(f[p]+=";?",h++):f[p]+=";";var g=new RegExp("&(?:"+f.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),E=d(r.default);function y(v){return v.substr(-1)!==";"&&(v+=";"),E(v)}return i(y,"replacer"),function(v){return String(v).replace(g,y)}}();function d(m){return i(function(f){if(f.charAt(1)==="#"){var p=f.charAt(2);return p==="X"||p==="x"?c.default(parseInt(f.substr(3),16)):c.default(parseInt(f.substr(2),10))}return m[f.slice(1,-1)]||f},"replace")}i(d,"getReplacer")}),Gl=Y(e=>{"use strict";var t=e&&e.__importDefault||function(C){return C&&C.__esModule?C:{default:C}};Object.defineProperty(e,"__esModule",{value:!0}),e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=void 0;var r=t(ks()),a=u(r.default),o=d(a);e.encodeXML=v(a);var c=t(ws()),l=u(c.default),s=d(l);e.encodeHTML=h(l,s),e.encodeNonAsciiHTML=v(l);function u(C){return Object.keys(C).sort().reduce(function(w,k){return w[C[k]]="&"+k+";",w},{})}i(u,"getInverseObj");function d(C){for(var w=[],k=[],D=0,T=Object.keys(C);D<T.length;D++){var _=T[D];_.length===1?w.push("\\"+_):k.push(_)}w.sort();for(var F=0;F<w.length-1;F++){for(var M=F;M<w.length-1&&w[M].charCodeAt(1)+1===w[M+1].charCodeAt(1);)M+=1;var H=1+M-F;H<3||w.splice(F,H,w[F]+"-"+w[M])}return k.unshift("["+w.join("")+"]"),new RegExp(k.join("|"),"g")}i(d,"getInverseReplacer");var m=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,f=String.prototype.codePointAt!=null?function(C){return C.codePointAt(0)}:function(C){return(C.charCodeAt(0)-55296)*1024+C.charCodeAt(1)-56320+65536};function p(C){return"&#x"+(C.length>1?f(C):C.charCodeAt(0)).toString(16).toUpperCase()+";"}i(p,"singleCharReplacer");function h(C,w){return function(k){return k.replace(w,function(D){return C[D]}).replace(m,p)}}i(h,"getInverse");var g=new RegExp(o.source+"|"+m.source,"g");function E(C){return C.replace(g,p)}i(E,"escape"),e.escape=E;function y(C){return C.replace(o,p)}i(y,"escapeUTF8"),e.escapeUTF8=y;function v(C){return function(w){return w.replace(g,function(k){return C[k]||p(k)})}}i(v,"getASCIIEncoder")}),yp=Y(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.decodeXMLStrict=e.decodeHTML5Strict=e.decodeHTML4Strict=e.decodeHTML5=e.decodeHTML4=e.decodeHTMLStrict=e.decodeHTML=e.decodeXML=e.encodeHTML5=e.encodeHTML4=e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=e.encode=e.decodeStrict=e.decode=void 0;var t=ql(),r=Gl();function a(u,d){return(!d||d<=0?t.decodeXML:t.decodeHTML)(u)}i(a,"decode"),e.decode=a;function o(u,d){return(!d||d<=0?t.decodeXML:t.decodeHTMLStrict)(u)}i(o,"decodeStrict"),e.decodeStrict=o;function c(u,d){return(!d||d<=0?r.encodeXML:r.encodeHTML)(u)}i(c,"encode"),e.encode=c;var l=Gl();Object.defineProperty(e,"encodeXML",{enumerable:!0,get:i(function(){return l.encodeXML},"get")}),Object.defineProperty(e,"encodeHTML",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")}),Object.defineProperty(e,"encodeNonAsciiHTML",{enumerable:!0,get:i(function(){return l.encodeNonAsciiHTML},"get")}),Object.defineProperty(e,"escape",{enumerable:!0,get:i(function(){return l.escape},"get")}),Object.defineProperty(e,"escapeUTF8",{enumerable:!0,get:i(function(){return l.escapeUTF8},"get")}),Object.defineProperty(e,"encodeHTML4",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")}),Object.defineProperty(e,"encodeHTML5",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")});var s=ql();Object.defineProperty(e,"decodeXML",{enumerable:!0,get:i(function(){return s.decodeXML},"get")}),Object.defineProperty(e,"decodeHTML",{enumerable:!0,get:i(function(){return s.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTMLStrict",{enumerable:!0,get:i(function(){return s.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeHTML4",{enumerable:!0,get:i(function(){return s.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTML5",{enumerable:!0,get:i(function(){return s.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTML4Strict",{enumerable:!0,get:i(function(){return s.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeHTML5Strict",{enumerable:!0,get:i(function(){return s.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeXMLStrict",{enumerable:!0,get:i(function(){return s.decodeXML},"get")})}),Ep=Y((e,t)=>{"use strict";function r(A,x){if(!(A instanceof x))throw new TypeError("Cannot call a class as a function")}i(r,"_classCallCheck");function a(A,x){for(var S=0;S<x.length;S++){var B=x[S];B.enumerable=B.enumerable||!1,B.configurable=!0,"value"in B&&(B.writable=!0),Object.defineProperty(A,B.key,B)}}i(a,"_defineProperties");function o(A,x,S){return x&&a(A.prototype,x),S&&a(A,S),A}i(o,"_createClass");function c(A,x){var S=typeof Symbol<"u"&&A[Symbol.iterator]||A["@@iterator"];if(!S){if(Array.isArray(A)||(S=l(A))||x&&A&&typeof A.length=="number"){S&&(A=S);var B=0,O=i(function(){},"F");return{s:O,n:i(function(){return B>=A.length?{done:!0}:{done:!1,value:A[B++]}},"n"),e:i(function(J){throw J},"e"),f:O}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var I=!0,N=!1,V;return{s:i(function(){S=S.call(A)},"s"),n:i(function(){var J=S.next();return I=J.done,J},"n"),e:i(function(J){N=!0,V=J},"e"),f:i(function(){try{!I&&S.return!=null&&S.return()}finally{if(N)throw V}},"f")}}i(c,"_createForOfIteratorHelper");function l(A,x){if(A){if(typeof A=="string")return s(A,x);var S=Object.prototype.toString.call(A).slice(8,-1);if(S==="Object"&&A.constructor&&(S=A.constructor.name),S==="Map"||S==="Set")return Array.from(A);if(S==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(S))return s(A,x)}}i(l,"_unsupportedIterableToArray");function s(A,x){(x==null||x>A.length)&&(x=A.length);for(var S=0,B=new Array(x);S<x;S++)B[S]=A[S];return B}i(s,"_arrayLikeToArray");var u=yp(),d={fg:"#FFF",bg:"#000",newline:!1,escapeXML:!1,stream:!1,colors:m()};function m(){var A={0:"#000",1:"#A00",2:"#0A0",3:"#A50",4:"#00A",5:"#A0A",6:"#0AA",7:"#AAA",8:"#555",9:"#F55",10:"#5F5",11:"#FF5",12:"#55F",13:"#F5F",14:"#5FF",15:"#FFF"};return C(0,5).forEach(function(x){C(0,5).forEach(function(S){C(0,5).forEach(function(B){return f(x,S,B,A)})})}),C(0,23).forEach(function(x){var S=x+232,B=p(x*10+8);A[S]="#"+B+B+B}),A}i(m,"getDefaultColors");function f(A,x,S,B){var O=16+A*36+x*6+S,I=A>0?A*40+55:0,N=x>0?x*40+55:0,V=S>0?S*40+55:0;B[O]=h([I,N,V])}i(f,"setStyleColor");function p(A){for(var x=A.toString(16);x.length<2;)x="0"+x;return x}i(p,"toHexString");function h(A){var x=[],S=c(A),B;try{for(S.s();!(B=S.n()).done;){var O=B.value;x.push(p(O))}}catch(I){S.e(I)}finally{S.f()}return"#"+x.join("")}i(h,"toColorHexString");function g(A,x,S,B){var O;return x==="text"?O=D(S,B):x==="display"?O=y(A,S,B):x==="xterm256Foreground"?O=F(A,B.colors[S]):x==="xterm256Background"?O=M(A,B.colors[S]):x==="rgb"&&(O=E(A,S)),O}i(g,"generateOutput");function E(A,x){x=x.substring(2).slice(0,-1);var S=+x.substr(0,2),B=x.substring(5).split(";"),O=B.map(function(I){return("0"+Number(I).toString(16)).substr(-2)}).join("");return _(A,(S===38?"color:#":"background-color:#")+O)}i(E,"handleRgb");function y(A,x,S){x=parseInt(x,10);var B={"-1":i(function(){return"<br/>"},"_"),0:i(function(){return A.length&&v(A)},"_"),1:i(function(){return T(A,"b")},"_"),3:i(function(){return T(A,"i")},"_"),4:i(function(){return T(A,"u")},"_"),8:i(function(){return _(A,"display:none")},"_"),9:i(function(){return T(A,"strike")},"_"),22:i(function(){return _(A,"font-weight:normal;text-decoration:none;font-style:normal")},"_"),23:i(function(){return H(A,"i")},"_"),24:i(function(){return H(A,"u")},"_"),39:i(function(){return F(A,S.fg)},"_"),49:i(function(){return M(A,S.bg)},"_"),53:i(function(){return _(A,"text-decoration:overline")},"_")},O;return B[x]?O=B[x]():4<x&&x<7?O=T(A,"blink"):29<x&&x<38?O=F(A,S.colors[x-30]):39<x&&x<48?O=M(A,S.colors[x-40]):89<x&&x<98?O=F(A,S.colors[8+(x-90)]):99<x&&x<108&&(O=M(A,S.colors[8+(x-100)])),O}i(y,"handleDisplay");function v(A){var x=A.slice(0);return A.length=0,x.reverse().map(function(S){return"</"+S+">"}).join("")}i(v,"resetStyles");function C(A,x){for(var S=[],B=A;B<=x;B++)S.push(B);return S}i(C,"range");function w(A){return function(x){return(A===null||x.category!==A)&&A!=="all"}}i(w,"notCategory");function k(A){A=parseInt(A,10);var x=null;return A===0?x="all":A===1?x="bold":2<A&&A<5?x="underline":4<A&&A<7?x="blink":A===8?x="hide":A===9?x="strike":29<A&&A<38||A===39||89<A&&A<98?x="foreground-color":(39<A&&A<48||A===49||99<A&&A<108)&&(x="background-color"),x}i(k,"categoryForCode");function D(A,x){return x.escapeXML?u.encodeXML(A):A}i(D,"pushText");function T(A,x,S){return S||(S=""),A.push(x),"<".concat(x).concat(S?' style="'.concat(S,'"'):"",">")}i(T,"pushTag");function _(A,x){return T(A,"span",x)}i(_,"pushStyle");function F(A,x){return T(A,"span","color:"+x)}i(F,"pushForegroundColor");function M(A,x){return T(A,"span","background-color:"+x)}i(M,"pushBackgroundColor");function H(A,x){var S;if(A.slice(-1)[0]===x&&(S=A.pop()),S)return"</"+x+">"}i(H,"closeTag");function z(A,x,S){var B=!1,O=3;function I(){return""}i(I,"remove");function N(_e,Be){return S("xterm256Foreground",Be),""}i(N,"removeXterm256Foreground");function V(_e,Be){return S("xterm256Background",Be),""}i(V,"removeXterm256Background");function J(_e){return x.newline?S("display",-1):S("text",_e),""}i(J,"newline");function Lt(_e,Be){B=!0,Be.trim().length===0&&(Be="0"),Be=Be.trimRight(";").split(";");var sr=c(Be),uo;try{for(sr.s();!(uo=sr.n()).done;){var wc=uo.value;S("display",wc)}}catch(kc){sr.e(kc)}finally{sr.f()}return""}i(Lt,"ansiMess");function At(_e){return S("text",_e),""}i(At,"realText");function Pt(_e){return S("rgb",_e),""}i(Pt,"rgb");var oo=[{pattern:/^\x08+/,sub:I},{pattern:/^\x1b\[[012]?K/,sub:I},{pattern:/^\x1b\[\(B/,sub:I},{pattern:/^\x1b\[[34]8;2;\d+;\d+;\d+m/,sub:Pt},{pattern:/^\x1b\[38;5;(\d+)m/,sub:N},{pattern:/^\x1b\[48;5;(\d+)m/,sub:V},{pattern:/^\n/,sub:J},{pattern:/^\r+\n/,sub:J},{pattern:/^\r/,sub:J},{pattern:/^\x1b\[((?:\d{1,3};?)+|)m/,sub:Lt},{pattern:/^\x1b\[\d?J/,sub:I},{pattern:/^\x1b\[\d{0,3};\d{0,3}f/,sub:I},{pattern:/^\x1b\[?[\d;]{0,3}/,sub:I},{pattern:/^(([^\x1b\x08\r\n])+)/,sub:At}];function lo(_e,Be){Be>O&&B||(B=!1,A=A.replace(_e.pattern,_e.sub))}i(lo,"process");var io=[],Ac=A,jt=Ac.length;e:for(;jt>0;){for(var pn=0,so=0,Cc=oo.length;so<Cc;pn=++so){var Sc=oo[pn];if(lo(Sc,pn),A.length!==jt){jt=A.length;continue e}}if(A.length===jt)break;io.push(0),jt=A.length}return io}i(z,"tokenize");function Q(A,x,S){return x!=="text"&&(A=A.filter(w(k(S))),A.push({token:x,data:S,category:k(S)})),A}i(Q,"updateStickyStack");var ne=function(){function A(x){r(this,A),x=x||{},x.colors&&(x.colors=Object.assign({},d.colors,x.colors)),this.options=Object.assign({},d,x),this.stack=[],this.stickyStack=[]}return i(A,"Filter"),o(A,[{key:"toHtml",value:i(function(x){var S=this;x=typeof x=="string"?[x]:x;var B=this.stack,O=this.options,I=[];return this.stickyStack.forEach(function(N){var V=g(B,N.token,N.data,O);V&&I.push(V)}),z(x.join(""),O,function(N,V){var J=g(B,N,V,O);J&&I.push(J),O.stream&&(S.stickyStack=Q(S.stickyStack,N,V))}),B.length&&I.push(v(B)),I.join("")},"toHtml")}]),A}();t.exports=ne}),Fa=Y((e,t)=>{function r(){return t.exports=r=Object.assign||function(a){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&(a[l]=c[l])}return a},r.apply(this,arguments)}i(r,"_extends"),t.exports=r}),vp=Y((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),s,u;for(u=0;u<l.length;u++)s=l[u],!(o.indexOf(s)>=0)&&(c[s]=a[s]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),La=Y((e,t)=>{var r=vp();function a(o,c){if(o==null)return{};var l=r(o,c),s,u;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(u=0;u<d.length;u++)s=d[u],!(c.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(o,s)&&(l[s]=o[s])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),xp=Y((e,t)=>{function r(a,o,c){return o in a?Object.defineProperty(a,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[o]=c,a}i(r,"_defineProperty"),t.exports=r}),Ap=Y((e,t)=>{var r=xp();function a(c,l){var s=Object.keys(c);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(c);l&&(u=u.filter(function(d){return Object.getOwnPropertyDescriptor(c,d).enumerable})),s.push.apply(s,u)}return s}i(a,"ownKeys");function o(c){for(var l=1;l<arguments.length;l++){var s=arguments[l]!=null?arguments[l]:{};l%2?a(s,!0).forEach(function(u){r(c,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(s)):a(s).forEach(function(u){Object.defineProperty(c,u,Object.getOwnPropertyDescriptor(s,u))})}return c}i(o,"_objectSpread2"),t.exports=o}),Cp=Y((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),s,u;for(u=0;u<l.length;u++)s=l[u],!(o.indexOf(s)>=0)&&(c[s]=a[s]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),Sp=Y((e,t)=>{var r=Cp();function a(o,c){if(o==null)return{};var l=r(o,c),s,u;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(u=0;u<d.length;u++)s=d[u],!(c.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(o,s)&&(l[s]=o[s])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),wp=Y((e,t)=>{function r(a,o,c){return o in a?Object.defineProperty(a,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[o]=c,a}i(r,"_defineProperty"),t.exports=r}),kp=Y((e,t)=>{var r=wp();function a(c,l){var s=Object.keys(c);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(c);l&&(u=u.filter(function(d){return Object.getOwnPropertyDescriptor(c,d).enumerable})),s.push.apply(s,u)}return s}i(a,"ownKeys");function o(c){for(var l=1;l<arguments.length;l++){var s=arguments[l]!=null?arguments[l]:{};l%2?a(s,!0).forEach(function(u){r(c,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(s)):a(s).forEach(function(u){Object.defineProperty(c,u,Object.getOwnPropertyDescriptor(s,u))})}return c}i(o,"_objectSpread2"),t.exports=o}),Op=Y((e,t)=>{function r(){return t.exports=r=Object.assign||function(a){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&(a[l]=c[l])}return a},r.apply(this,arguments)}i(r,"_extends"),t.exports=r}),Tp=Y((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),s,u;for(u=0;u<l.length;u++)s=l[u],!(o.indexOf(s)>=0)&&(c[s]=a[s]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),Dp=Y((e,t)=>{var r=Tp();function a(o,c){if(o==null)return{};var l=r(o,c),s,u;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(u=0;u<d.length;u++)s=d[u],!(c.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(o,s)&&(l[s]=o[s])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),Wl=Object.prototype.hasOwnProperty;function oa(e,t,r){for(r of e.keys())if(st(r,t))return r}i(oa,"find");function st(e,t){var r,a,o;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((a=e.length)===t.length)for(;a--&&st(e[a],t[a]););return a===-1}if(r===Set){if(e.size!==t.size)return!1;for(a of e)if(o=a,o&&typeof o=="object"&&(o=oa(t,o),!o)||!t.has(o))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(a of e)if(o=a[0],o&&typeof o=="object"&&(o=oa(t,o),!o)||!st(a[1],t.get(o)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((a=e.byteLength)===t.byteLength)for(;a--&&e.getInt8(a)===t.getInt8(a););return a===-1}if(ArrayBuffer.isView(e)){if((a=e.byteLength)===t.byteLength)for(;a--&&e[a]===t[a];);return a===-1}if(!r||typeof e=="object"){a=0;for(r in e)if(Wl.call(e,r)&&++a&&!Wl.call(t,r)||!(r in t)||!st(e[r],t[r]))return!1;return Object.keys(t).length===a}}return e!==e&&t!==t}i(st,"dequal");an();function fe(){return fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},fe.apply(null,arguments)}i(fe,"_extends");function Os(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}i(Os,"_assertThisInitialized");function Rt(e,t){return Rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,a){return r.__proto__=a,r},Rt(e,t)}i(Rt,"_setPrototypeOf");function Ts(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Rt(e,t)}i(Ts,"_inheritsLoose");function Kr(e){return Kr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Kr(e)}i(Kr,"_getPrototypeOf");function Ds(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}i(Ds,"_isNativeFunction");function Pa(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Pa=i(function(){return!!e},"_isNativeReflectConstruct"))()}i(Pa,"_isNativeReflectConstruct");function Is(e,t,r){if(Pa())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,t);var o=new(e.bind.apply(e,a));return r&&Rt(o,r.prototype),o}i(Is,"_construct");function Yr(e){var t=typeof Map=="function"?new Map:void 0;return Yr=i(function(r){if(r===null||!Ds(r))return r;if(typeof r!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(r))return t.get(r);t.set(r,a)}function a(){return Is(r,arguments,Kr(this).constructor)}return i(a,"Wrapper"),a.prototype=Object.create(r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),Rt(a,r)},"_wrapNativeSuper"),Yr(e)}i(Yr,"_wrapNativeSuper");var Ip={1:`Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).

`,2:`Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).

`,3:`Passed an incorrect argument to a color function, please pass a string representation of a color.

`,4:`Couldn't generate valid rgb string from %s, it returned %s.

`,5:`Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.

`,6:`Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).

`,7:`Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).

`,8:`Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.

`,9:`Please provide a number of steps to the modularScale helper.

`,10:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,11:`Invalid value passed as base to modularScale, expected number or em string but got "%s"

`,12:`Expected a string ending in "px" or a number passed as the first argument to %s(), got "%s" instead.

`,13:`Expected a string ending in "px" or a number passed as the second argument to %s(), got "%s" instead.

`,14:`Passed invalid pixel value ("%s") to %s(), please pass a value like "12px" or 12.

`,15:`Passed invalid base value ("%s") to %s(), please pass a value like "12px" or 12.

`,16:`You must provide a template to this method.

`,17:`You passed an unsupported selector state to this method.

`,18:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,19:`fromSize and toSize must be provided as stringified numbers with the same units.

`,20:`expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,21:"expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\n\n",22:"expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\n\n",23:`fontFace expects a name of a font-family.

`,24:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,25:`fontFace expects localFonts to be an array.

`,26:`fontFace expects fileFormats to be an array.

`,27:`radialGradient requries at least 2 color-stops to properly render.

`,28:`Please supply a filename to retinaImage() as the first argument.

`,29:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,30:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",31:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation

`,32:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])
To pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')

`,33:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation

`,34:`borderRadius expects a radius value as a string or number as the second argument.

`,35:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,36:`Property must be a string value.

`,37:`Syntax Error at %s.

`,38:`Formula contains a function that needs parentheses at %s.

`,39:`Formula is missing closing parenthesis at %s.

`,40:`Formula has too many closing parentheses at %s.

`,41:`All values in a formula must have the same unit or be unitless.

`,42:`Please provide a number of steps to the modularScale helper.

`,43:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,44:`Invalid value passed as base to modularScale, expected number or em/rem string but got %s.

`,45:`Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.

`,46:`Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.

`,47:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,48:`fromSize and toSize must be provided as stringified numbers with the same units.

`,49:`Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,50:`Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.

`,51:`Expects the first argument object to have the properties prop, fromSize, and toSize.

`,52:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,53:`fontFace expects localFonts to be an array.

`,54:`fontFace expects fileFormats to be an array.

`,55:`fontFace expects a name of a font-family.

`,56:`linearGradient requries at least 2 color-stops to properly render.

`,57:`radialGradient requries at least 2 color-stops to properly render.

`,58:`Please supply a filename to retinaImage() as the first argument.

`,59:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,60:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",61:`Property must be a string value.

`,62:`borderRadius expects a radius value as a string or number as the second argument.

`,63:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,64:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.

`,65:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').

`,66:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.

`,67:`You must provide a template to this method.

`,68:`You passed an unsupported selector state to this method.

`,69:`Expected a string ending in "px" or a number passed as the first argument to %s(), got %s instead.

`,70:`Expected a string ending in "px" or a number passed as the second argument to %s(), got %s instead.

`,71:`Passed invalid pixel value %s to %s(), please pass a value like "12px" or 12.

`,72:`Passed invalid base value %s to %s(), please pass a value like "12px" or 12.

`,73:`Please provide a valid CSS variable.

`,74:`CSS variable not found and no default was provided.

`,75:`important requires a valid style object, got a %s instead.

`,76:`fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.

`,77:`remToPx expects a value in "rem" but you provided it in "%s".

`,78:`base must be set in "px" or "%" but you set it in "%s".
`};function _s(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=t[0],o=[],c;for(c=1;c<t.length;c+=1)o.push(t[c]);return o.forEach(function(l){a=a.replace(/%[a-z]/,l)}),a}i(_s,"format");var Ae=function(e){Ts(t,e);function t(r){for(var a,o=arguments.length,c=new Array(o>1?o-1:0),l=1;l<o;l++)c[l-1]=arguments[l];return a=e.call(this,_s.apply(void 0,[Ip[r]].concat(c)))||this,Os(a)}return i(t,"PolishedError"),t}(Yr(Error));function la(e,t){return e.substr(-t.length)===t}i(la,"endsWith");var _p=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function ia(e){if(typeof e!="string")return e;var t=e.match(_p);return t?parseFloat(e):e}i(ia,"stripUnit");var Bp=i(function(e){return function(t,r){r===void 0&&(r="16px");var a=t,o=r;if(typeof t=="string"){if(!la(t,"px"))throw new Ae(69,e,t);a=ia(t)}if(typeof r=="string"){if(!la(r,"px"))throw new Ae(70,e,r);o=ia(r)}if(typeof a=="string")throw new Ae(71,t,e);if(typeof o=="string")throw new Ae(72,r,e);return""+a/o+e}},"pxtoFactory"),Bs=Bp,OC=Bs("em"),TC=Bs("rem");function Ur(e){return Math.round(e*255)}i(Ur,"colorToInt");function Rs(e,t,r){return Ur(e)+","+Ur(t)+","+Ur(r)}i(Rs,"convertToInt");function Nt(e,t,r,a){if(a===void 0&&(a=Rs),t===0)return a(r,r,r);var o=(e%360+360)%360/60,c=(1-Math.abs(2*r-1))*t,l=c*(1-Math.abs(o%2-1)),s=0,u=0,d=0;o>=0&&o<1?(s=c,u=l):o>=1&&o<2?(s=l,u=c):o>=2&&o<3?(u=c,d=l):o>=3&&o<4?(u=l,d=c):o>=4&&o<5?(s=l,d=c):o>=5&&o<6&&(s=c,d=l);var m=r-c/2,f=s+m,p=u+m,h=d+m;return a(f,p,h)}i(Nt,"hslToRgb");var Kl={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};function Ns(e){if(typeof e!="string")return e;var t=e.toLowerCase();return Kl[t]?"#"+Kl[t]:e}i(Ns,"nameToHex");var Rp=/^#[a-fA-F0-9]{6}$/,Np=/^#[a-fA-F0-9]{8}$/,Fp=/^#[a-fA-F0-9]{3}$/,Lp=/^#[a-fA-F0-9]{4}$/,Hn=/^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i,Pp=/^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i,jp=/^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i,Mp=/^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;function bt(e){if(typeof e!="string")throw new Ae(3);var t=Ns(e);if(t.match(Rp))return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16)};if(t.match(Np)){var r=parseFloat((parseInt(""+t[7]+t[8],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16),alpha:r}}if(t.match(Fp))return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16)};if(t.match(Lp)){var a=parseFloat((parseInt(""+t[4]+t[4],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16),alpha:a}}var o=Hn.exec(t);if(o)return{red:parseInt(""+o[1],10),green:parseInt(""+o[2],10),blue:parseInt(""+o[3],10)};var c=Pp.exec(t.substring(0,50));if(c)return{red:parseInt(""+c[1],10),green:parseInt(""+c[2],10),blue:parseInt(""+c[3],10),alpha:parseFloat(""+c[4])>1?parseFloat(""+c[4])/100:parseFloat(""+c[4])};var l=jp.exec(t);if(l){var s=parseInt(""+l[1],10),u=parseInt(""+l[2],10)/100,d=parseInt(""+l[3],10)/100,m="rgb("+Nt(s,u,d)+")",f=Hn.exec(m);if(!f)throw new Ae(4,t,m);return{red:parseInt(""+f[1],10),green:parseInt(""+f[2],10),blue:parseInt(""+f[3],10)}}var p=Mp.exec(t.substring(0,50));if(p){var h=parseInt(""+p[1],10),g=parseInt(""+p[2],10)/100,E=parseInt(""+p[3],10)/100,y="rgb("+Nt(h,g,E)+")",v=Hn.exec(y);if(!v)throw new Ae(4,t,y);return{red:parseInt(""+v[1],10),green:parseInt(""+v[2],10),blue:parseInt(""+v[3],10),alpha:parseFloat(""+p[4])>1?parseFloat(""+p[4])/100:parseFloat(""+p[4])}}throw new Ae(5)}i(bt,"parseToRgb");function Fs(e){var t=e.red/255,r=e.green/255,a=e.blue/255,o=Math.max(t,r,a),c=Math.min(t,r,a),l=(o+c)/2;if(o===c)return e.alpha!==void 0?{hue:0,saturation:0,lightness:l,alpha:e.alpha}:{hue:0,saturation:0,lightness:l};var s,u=o-c,d=l>.5?u/(2-o-c):u/(o+c);switch(o){case t:s=(r-a)/u+(r<a?6:0);break;case r:s=(a-t)/u+2;break;default:s=(t-r)/u+4;break}return s*=60,e.alpha!==void 0?{hue:s,saturation:d,lightness:l,alpha:e.alpha}:{hue:s,saturation:d,lightness:l}}i(Fs,"rgbToHsl");function Ye(e){return Fs(bt(e))}i(Ye,"parseToHsl");var Up=i(function(e){return e.length===7&&e[1]===e[2]&&e[3]===e[4]&&e[5]===e[6]?"#"+e[1]+e[3]+e[5]:e},"reduceHexValue"),sa=Up;function at(e){var t=e.toString(16);return t.length===1?"0"+t:t}i(at,"numberToHex");function $r(e){return at(Math.round(e*255))}i($r,"colorToHex");function Ls(e,t,r){return sa("#"+$r(e)+$r(t)+$r(r))}i(Ls,"convertToHex");function ar(e,t,r){return Nt(e,t,r,Ls)}i(ar,"hslToHex");function Ps(e,t,r){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number")return ar(e,t,r);if(typeof e=="object"&&t===void 0&&r===void 0)return ar(e.hue,e.saturation,e.lightness);throw new Ae(1)}i(Ps,"hsl");function js(e,t,r,a){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number"&&typeof a=="number")return a>=1?ar(e,t,r):"rgba("+Nt(e,t,r)+","+a+")";if(typeof e=="object"&&t===void 0&&r===void 0&&a===void 0)return e.alpha>=1?ar(e.hue,e.saturation,e.lightness):"rgba("+Nt(e.hue,e.saturation,e.lightness)+","+e.alpha+")";throw new Ae(2)}i(js,"hsla");function Jr(e,t,r){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number")return sa("#"+at(e)+at(t)+at(r));if(typeof e=="object"&&t===void 0&&r===void 0)return sa("#"+at(e.red)+at(e.green)+at(e.blue));throw new Ae(6)}i(Jr,"rgb");function Le(e,t,r,a){if(typeof e=="string"&&typeof t=="number"){var o=bt(e);return"rgba("+o.red+","+o.green+","+o.blue+","+t+")"}else{if(typeof e=="number"&&typeof t=="number"&&typeof r=="number"&&typeof a=="number")return a>=1?Jr(e,t,r):"rgba("+e+","+t+","+r+","+a+")";if(typeof e=="object"&&t===void 0&&r===void 0&&a===void 0)return e.alpha>=1?Jr(e.red,e.green,e.blue):"rgba("+e.red+","+e.green+","+e.blue+","+e.alpha+")"}throw new Ae(7)}i(Le,"rgba");var $p=i(function(e){return typeof e.red=="number"&&typeof e.green=="number"&&typeof e.blue=="number"&&(typeof e.alpha!="number"||typeof e.alpha>"u")},"isRgb"),Hp=i(function(e){return typeof e.red=="number"&&typeof e.green=="number"&&typeof e.blue=="number"&&typeof e.alpha=="number"},"isRgba"),Vp=i(function(e){return typeof e.hue=="number"&&typeof e.saturation=="number"&&typeof e.lightness=="number"&&(typeof e.alpha!="number"||typeof e.alpha>"u")},"isHsl"),zp=i(function(e){return typeof e.hue=="number"&&typeof e.saturation=="number"&&typeof e.lightness=="number"&&typeof e.alpha=="number"},"isHsla");function Je(e){if(typeof e!="object")throw new Ae(8);if(Hp(e))return Le(e);if($p(e))return Jr(e);if(zp(e))return js(e);if(Vp(e))return Ps(e);throw new Ae(8)}i(Je,"toColorString");function ja(e,t,r){return i(function(){var a=r.concat(Array.prototype.slice.call(arguments));return a.length>=t?e.apply(this,a):ja(e,t,a)},"fn")}i(ja,"curried");function Se(e){return ja(e,e.length,[])}i(Se,"curry");function Ms(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{hue:r.hue+parseFloat(e)}))}i(Ms,"adjustHue");var DC=Se(Ms);function vt(e,t,r){return Math.max(e,Math.min(t,r))}i(vt,"guard");function Us(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{lightness:vt(0,1,r.lightness-parseFloat(e))}))}i(Us,"darken");var qp=Se(Us),Ge=qp;function $s(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{saturation:vt(0,1,r.saturation-parseFloat(e))}))}i($s,"desaturate");var IC=Se($s);function Hs(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{lightness:vt(0,1,r.lightness+parseFloat(e))}))}i(Hs,"lighten");var Gp=Se(Hs),ht=Gp;function Vs(e,t,r){if(t==="transparent")return r;if(r==="transparent")return t;if(e===0)return r;var a=bt(t),o=fe({},a,{alpha:typeof a.alpha=="number"?a.alpha:1}),c=bt(r),l=fe({},c,{alpha:typeof c.alpha=="number"?c.alpha:1}),s=o.alpha-l.alpha,u=parseFloat(e)*2-1,d=u*s===-1?u:u+s,m=1+u*s,f=(d/m+1)/2,p=1-f,h={red:Math.floor(o.red*f+l.red*p),green:Math.floor(o.green*f+l.green*p),blue:Math.floor(o.blue*f+l.blue*p),alpha:o.alpha*parseFloat(e)+l.alpha*(1-parseFloat(e))};return Le(h)}i(Vs,"mix");var Wp=Se(Vs),zs=Wp;function qs(e,t){if(t==="transparent")return t;var r=bt(t),a=typeof r.alpha=="number"?r.alpha:1,o=fe({},r,{alpha:vt(0,1,(a*100+parseFloat(e)*100)/100)});return Le(o)}i(qs,"opacify");var Kp=Se(qs),Xt=Kp;function Gs(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{saturation:vt(0,1,r.saturation+parseFloat(e))}))}i(Gs,"saturate");var _C=Se(Gs);function Ws(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{hue:parseFloat(e)}))}i(Ws,"setHue");var BC=Se(Ws);function Ks(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{lightness:parseFloat(e)}))}i(Ks,"setLightness");var RC=Se(Ks);function Ys(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{saturation:parseFloat(e)}))}i(Ys,"setSaturation");var NC=Se(Ys);function Js(e,t){return t==="transparent"?t:zs(parseFloat(e),"rgb(0, 0, 0)",t)}i(Js,"shade");var FC=Se(Js);function Xs(e,t){return t==="transparent"?t:zs(parseFloat(e),"rgb(255, 255, 255)",t)}i(Xs,"tint");var LC=Se(Xs);function Zs(e,t){if(t==="transparent")return t;var r=bt(t),a=typeof r.alpha=="number"?r.alpha:1,o=fe({},r,{alpha:vt(0,1,+(a*100-parseFloat(e)*100).toFixed(2)/100)});return Le(o)}i(Zs,"transparentize");var Yp=Se(Zs),W=Yp,Jp=b.div(Ze,({theme:e})=>({backgroundColor:e.base==="light"?"rgba(0,0,0,.01)":"rgba(255,255,255,.01)",borderRadius:e.appBorderRadius,border:`1px dashed ${e.appBorderColor}`,display:"flex",alignItems:"center",justifyContent:"center",padding:20,margin:"25px 0 40px",color:W(.3,e.color.defaultText),fontSize:e.typography.size.s2})),Qs=i(e=>n.createElement(Jp,{...e,className:"docblock-emptyblock sb-unstyled"}),"EmptyBlock"),Xp=b(Vt)(({theme:e})=>({fontSize:`${e.typography.size.s2-1}px`,lineHeight:"19px",margin:"25px 0 40px",borderRadius:e.appBorderRadius,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0","pre.prismjs":{padding:20,background:"inherit"}})),Zp=b.div(({theme:e})=>({background:e.background.content,borderRadius:e.appBorderRadius,border:`1px solid ${e.appBorderColor}`,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",margin:"25px 0 40px",padding:"20px 20px 20px 22px"})),Rr=b.div(({theme:e})=>({animation:`${e.animation.glow} 1.5s ease-in-out infinite`,background:e.appBorderColor,height:17,marginTop:1,width:"60%",[`&:first-child${Go}`]:{margin:0}})),Qp=i(()=>n.createElement(Zp,null,n.createElement(Rr,null),n.createElement(Rr,{style:{width:"80%"}}),n.createElement(Rr,{style:{width:"30%"}}),n.createElement(Rr,{style:{width:"80%"}})),"SourceSkeleton"),em=i(({isLoading:e,error:t,language:r,code:a,dark:o,format:c=!0,...l})=>{let{typography:s}=De();if(e)return n.createElement(Qp,null);if(t)return n.createElement(Qs,null,t);let u=n.createElement(Xp,{bordered:!0,copyable:!0,format:c,language:r??"jsx",className:"docblock-source sb-unstyled",...l},a);if(typeof o>"u")return u;let d=o?An.dark:An.light;return n.createElement(zo,{theme:qo({...d,fontCode:s.fonts.mono,fontBase:s.fonts.base})},u)},"Source"),ie=i(e=>`& :where(${e}:not(.sb-anchor, .sb-unstyled, .sb-unstyled ${e}))`,"toGlobalSelector"),Ma=600,KC=b.h1(Ze,({theme:e})=>({color:e.color.defaultText,fontSize:e.typography.size.m3,fontWeight:e.typography.weight.bold,lineHeight:"32px",[`@media (min-width: ${Ma}px)`]:{fontSize:e.typography.size.l1,lineHeight:"36px",marginBottom:"16px"}})),YC=b.h2(Ze,({theme:e})=>({fontWeight:e.typography.weight.regular,fontSize:e.typography.size.s3,lineHeight:"20px",borderBottom:"none",marginBottom:15,[`@media (min-width: ${Ma}px)`]:{fontSize:e.typography.size.m1,lineHeight:"28px",marginBottom:24},color:W(.25,e.color.defaultText)})),JC=b.div(({theme:e})=>{let t={fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s3,margin:0,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch"},r={margin:"20px 0 8px",padding:0,cursor:"text",position:"relative",color:e.color.defaultText,"&:first-of-type":{marginTop:0,paddingTop:0},"&:hover a.anchor":{textDecoration:"none"},"& code":{fontSize:"inherit"}},a={lineHeight:1,margin:"0 2px",padding:"3px 5px",whiteSpace:"nowrap",borderRadius:3,fontSize:e.typography.size.s2-1,border:e.base==="light"?`1px solid ${e.color.mediumlight}`:`1px solid ${e.color.darker}`,color:e.base==="light"?W(.1,e.color.defaultText):W(.3,e.color.defaultText),backgroundColor:e.base==="light"?e.color.lighter:e.color.border};return{maxWidth:1e3,width:"100%",minWidth:0,[ie("a")]:{...t,fontSize:"inherit",lineHeight:"24px",color:e.color.secondary,textDecoration:"none","&.absent":{color:"#cc0000"},"&.anchor":{display:"block",paddingLeft:30,marginLeft:-30,cursor:"pointer",position:"absolute",top:0,left:0,bottom:0}},[ie("blockquote")]:{...t,margin:"16px 0",borderLeft:`4px solid ${e.color.medium}`,padding:"0 15px",color:e.color.dark,"& > :first-of-type":{marginTop:0},"& > :last-child":{marginBottom:0}},[ie("div")]:t,[ie("dl")]:{...t,margin:"16px 0",padding:0,"& dt":{fontSize:"14px",fontWeight:"bold",fontStyle:"italic",padding:0,margin:"16px 0 4px"},"& dt:first-of-type":{padding:0},"& dt > :first-of-type":{marginTop:0},"& dt > :last-child":{marginBottom:0},"& dd":{margin:"0 0 16px",padding:"0 15px"},"& dd > :first-of-type":{marginTop:0},"& dd > :last-child":{marginBottom:0}},[ie("h1")]:{...t,...r,fontSize:`${e.typography.size.l1}px`,fontWeight:e.typography.weight.bold},[ie("h2")]:{...t,...r,fontSize:`${e.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${e.appBorderColor}`},[ie("h3")]:{...t,...r,fontSize:`${e.typography.size.m1}px`,fontWeight:e.typography.weight.bold},[ie("h4")]:{...t,...r,fontSize:`${e.typography.size.s3}px`},[ie("h5")]:{...t,...r,fontSize:`${e.typography.size.s2}px`},[ie("h6")]:{...t,...r,fontSize:`${e.typography.size.s2}px`,color:e.color.dark},[ie("hr")]:{border:"0 none",borderTop:`1px solid ${e.appBorderColor}`,height:4,padding:0},[ie("img")]:{maxWidth:"100%"},[ie("li")]:{...t,fontSize:e.typography.size.s2,color:e.color.defaultText,lineHeight:"24px","& + li":{marginTop:".25em"},"& ul, & ol":{marginTop:".25em",marginBottom:0},"& code":a},[ie("ol")]:{...t,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0}},[ie("p")]:{...t,margin:"16px 0",fontSize:e.typography.size.s2,lineHeight:"24px",color:e.color.defaultText,"& code":a},[ie("pre")]:{...t,fontFamily:e.typography.fonts.mono,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",lineHeight:"18px",padding:"11px 1rem",whiteSpace:"pre-wrap",color:"inherit",borderRadius:3,margin:"1rem 0","&:not(.prismjs)":{background:"transparent",border:"none",borderRadius:0,padding:0,margin:0},"& pre, &.prismjs":{padding:15,margin:0,whiteSpace:"pre-wrap",color:"inherit",fontSize:"13px",lineHeight:"19px",code:{color:"inherit",fontSize:"inherit"}},"& code":{whiteSpace:"pre"},"& code, & tt":{border:"none"}},[ie("span")]:{...t,"&.frame":{display:"block",overflow:"hidden","& > span":{border:`1px solid ${e.color.medium}`,display:"block",float:"left",overflow:"hidden",margin:"13px 0 0",padding:7,width:"auto"},"& span img":{display:"block",float:"left"},"& span span":{clear:"both",color:e.color.darkest,display:"block",padding:"5px 0 0"}},"&.align-center":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"center"},"& span img":{margin:"0 auto",textAlign:"center"}},"&.align-right":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px 0 0",textAlign:"right"},"& span img":{margin:0,textAlign:"right"}},"&.float-left":{display:"block",marginRight:13,overflow:"hidden",float:"left","& span":{margin:"13px 0 0"}},"&.float-right":{display:"block",marginLeft:13,overflow:"hidden",float:"right","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"right"}}},[ie("table")]:{...t,margin:"16px 0",fontSize:e.typography.size.s2,lineHeight:"24px",padding:0,borderCollapse:"collapse","& tr":{borderTop:`1px solid ${e.appBorderColor}`,backgroundColor:e.appContentBg,margin:0,padding:0},"& tr:nth-of-type(2n)":{backgroundColor:e.base==="dark"?e.color.darker:e.color.lighter},"& tr th":{fontWeight:"bold",color:e.color.defaultText,border:`1px solid ${e.appBorderColor}`,margin:0,padding:"6px 13px"},"& tr td":{border:`1px solid ${e.appBorderColor}`,color:e.color.defaultText,margin:0,padding:"6px 13px"},"& tr th :first-of-type, & tr td :first-of-type":{marginTop:0},"& tr th :last-child, & tr td :last-child":{marginBottom:0}},[ie("ul")]:{...t,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0},listStyle:"disc"}}}),XC=b.div(({theme:e})=>({background:e.background.content,display:"flex",flexDirection:"row-reverse",justifyContent:"center",padding:"4rem 20px",minHeight:"100vh",boxSizing:"border-box",gap:"3rem",[`@media (min-width: ${Ma}px)`]:{}})),on=i(e=>({borderRadius:e.appBorderRadius,background:e.background.content,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",border:`1px solid ${e.appBorderColor}`}),"getBlockBackgroundStyle"),{window:f5}=globalThis,tm=Ct({scale:1}),{PREVIEW_URL:g5}=globalThis,b5=b.strong(({theme:e})=>({color:e.color.orange})),rm=b(mn)({position:"absolute",left:0,right:0,top:0,transition:"transform .2s linear"}),nm=b.div({display:"flex",alignItems:"center",gap:4}),am=b.div(({theme:e})=>({width:14,height:14,borderRadius:2,margin:"0 7px",backgroundColor:e.appBorderColor,animation:`${e.animation.glow} 1.5s ease-in-out infinite`})),om=i(({isLoading:e,storyId:t,baseUrl:r,zoom:a,resetZoom:o,...c})=>n.createElement(rm,{...c},n.createElement(nm,{key:"left"},e?[1,2,3].map(l=>n.createElement(am,{key:l})):n.createElement(n.Fragment,null,n.createElement(G,{key:"zoomin",onClick:l=>{l.preventDefault(),a(.8)},title:"Zoom in"},n.createElement(Uo,null)),n.createElement(G,{key:"zoomout",onClick:l=>{l.preventDefault(),a(1.25)},title:"Zoom out"},n.createElement($o,null)),n.createElement(G,{key:"zoomreset",onClick:l=>{l.preventDefault(),o()},title:"Reset zoom"},n.createElement(Ho,null))))),"Toolbar"),lm=b.div(({isColumn:e,columns:t,layout:r})=>({display:e||!t?"block":"flex",position:"relative",flexWrap:"wrap",overflow:"auto",flexDirection:e?"column":"row","& .innerZoomElementWrapper > *":e?{width:r!=="fullscreen"?"calc(100% - 20px)":"100%",display:"block"}:{maxWidth:r!=="fullscreen"?"calc(100% - 20px)":"100%",display:"inline-block"}}),({layout:e="padded",inline:t})=>e==="centered"||e==="padded"?{padding:t?"32px 22px":"0px","& .innerZoomElementWrapper > *":{width:"auto",border:"8px solid transparent!important"}}:{},({layout:e="padded",inline:t})=>e==="centered"&&t?{display:"flex",justifyContent:"center",justifyItems:"center",alignContent:"center",alignItems:"center"}:{},({columns:e})=>e&&e>1?{".innerZoomElementWrapper > *":{minWidth:`calc(100% / ${e} - 20px)`}}:{}),Yl=b(em)(({theme:e})=>({margin:0,borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:e.appBorderRadius,borderBottomRightRadius:e.appBorderRadius,border:"none",background:e.base==="light"?"rgba(0, 0, 0, 0.85)":Ge(.05,e.background.content),color:e.color.lightest,button:{background:e.base==="light"?"rgba(0, 0, 0, 0.85)":Ge(.05,e.background.content)}})),im=b.div(({theme:e,withSource:t,isExpanded:r})=>({position:"relative",overflow:"hidden",margin:"25px 0 40px",...on(e),borderBottomLeftRadius:t&&r&&0,borderBottomRightRadius:t&&r&&0,borderBottomWidth:r&&0,"h3 + &":{marginTop:"16px"}}),({withToolbar:e})=>e&&{paddingTop:40}),sm=i((e,t,r)=>{switch(!0){case!!(e&&e.error):return{source:null,actionItem:{title:"No code available",className:"docblock-code-toggle docblock-code-toggle--disabled",disabled:!0,onClick:i(()=>r(!1),"onClick")}};case t:return{source:n.createElement(Yl,{...e,dark:!0}),actionItem:{title:"Hide code",className:"docblock-code-toggle docblock-code-toggle--expanded",onClick:i(()=>r(!1),"onClick")}};default:return{source:n.createElement(Yl,{...e,dark:!0}),actionItem:{title:"Show code",className:"docblock-code-toggle",onClick:i(()=>r(!0),"onClick")}}}},"getSource");function eu(e){if(cr.count(e)===1){let t=e;if(t.props)return t.props.id}return null}i(eu,"getStoryId");var um=b(om)({position:"absolute",top:0,left:0,right:0,height:40}),cm=b.div({overflow:"hidden",position:"relative"}),dm=i(({isLoading:e,isColumn:t,columns:r,children:a,withSource:o,withToolbar:c=!1,isExpanded:l=!1,additionalActions:s,className:u,layout:d="padded",inline:m=!1,...f})=>{let[p,h]=R(l),{source:g,actionItem:E}=sm(o,p,h),[y,v]=R(1),C=[u].concat(["sbdocs","sbdocs-preview","sb-unstyled"]),w=o?[E]:[],[k,D]=R(s?[...s]:[]),T=[...w,...k],{window:_}=globalThis,F=$(async H=>{let{createCopyToClipboardFunction:z}=await Promise.resolve().then(()=>(U(),fo));z()},[]),M=i(H=>{let z=_.getSelection();z&&z.type==="Range"||(H.preventDefault(),k.filter(Q=>Q.title==="Copied").length===0&&F(g?.props.code??"").then(()=>{D([...k,{title:"Copied",onClick:i(()=>{},"onClick")}]),_.setTimeout(()=>D(k.filter(Q=>Q.title!=="Copied")),1500)}))},"onCopyCapture");return n.createElement(im,{withSource:o,withToolbar:c,...f,className:C.join(" ")},c&&n.createElement(um,{isLoading:e,border:!0,zoom:H=>v(y*H),resetZoom:()=>v(1),storyId:eu(a),baseUrl:"./iframe.html"}),n.createElement(tm.Provider,{value:{scale:y}},n.createElement(cm,{className:"docs-story",onCopyCapture:o&&M},n.createElement(lm,{isColumn:t||!Array.isArray(a),columns:r,layout:d,inline:m},n.createElement(En.Element,{centered:d==="centered",scale:m?y:1},Array.isArray(a)?a.map((H,z)=>n.createElement("div",{key:z},H)):n.createElement("div",null,a))),n.createElement(Mt,{actionItems:T}))),o&&p&&g)},"Preview"),A5=b(dm)(()=>({".docs-story":{paddingTop:32,paddingBottom:40}})),_5=b.div(({theme:e})=>({marginRight:30,fontSize:`${e.typography.size.s1}px`,color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText)})),B5=b.div({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),R5=b.div({display:"flex",flexDirection:"row",alignItems:"baseline","&:not(:last-child)":{marginBottom:"1rem"}}),N5=b.div(Ze,({theme:e})=>({...on(e),margin:"25px 0 40px",padding:"30px 20px"})),U5=b.div(({theme:e})=>({fontWeight:e.typography.weight.bold,color:e.color.defaultText})),$5=b.div(({theme:e})=>({color:e.base==="light"?W(.2,e.color.defaultText):W(.6,e.color.defaultText)})),H5=b.div({flex:"0 0 30%",lineHeight:"20px",marginTop:5}),V5=b.div(({theme:e})=>({flex:1,textAlign:"center",fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,lineHeight:1,overflow:"hidden",color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText),"> div":{display:"inline-block",overflow:"hidden",maxWidth:"100%",textOverflow:"ellipsis"},span:{display:"block",marginTop:2}})),z5=b.div({display:"flex",flexDirection:"row"}),q5=b.div(({background:e})=>({position:"relative",flex:1,"&::before":{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:e,content:'""'}})),G5=b.div(({theme:e})=>({...on(e),display:"flex",flexDirection:"row",height:50,marginBottom:5,overflow:"hidden",backgroundColor:"white",backgroundImage:"repeating-linear-gradient(-45deg, #ccc, #ccc 1px, #fff 1px, #fff 16px)",backgroundClip:"padding-box"})),W5=b.div({display:"flex",flexDirection:"column",flex:1,position:"relative",marginBottom:30}),K5=b.div({flex:1,display:"flex",flexDirection:"row"}),Y5=b.div({display:"flex",alignItems:"flex-start"}),J5=b.div({flex:"0 0 30%"}),X5=b.div({flex:1}),Z5=b.div(({theme:e})=>({display:"flex",flexDirection:"row",alignItems:"center",paddingBottom:20,fontWeight:e.typography.weight.bold,color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText)})),Q5=b.div(({theme:e})=>({fontSize:e.typography.size.s2,lineHeight:"20px",display:"flex",flexDirection:"column"})),oS=b.div(({theme:e})=>({fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s1,color:e.color.defaultText,marginLeft:10,lineHeight:1.2,display:"-webkit-box",overflow:"hidden",wordBreak:"break-word",textOverflow:"ellipsis",WebkitLineClamp:2,WebkitBoxOrient:"vertical"})),lS=b.div(({theme:e})=>({...on(e),overflow:"hidden",height:40,width:40,display:"flex",alignItems:"center",justifyContent:"center",flex:"none","> img, > svg":{width:20,height:20}})),iS=b.div({display:"inline-flex",flexDirection:"row",alignItems:"center",width:"100%"}),sS=b.div({display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(140px, 1fr))",gridGap:"8px 16px",gridAutoFlow:"row dense",gridAutoRows:50}),gS=b.aside(()=>({width:"10rem","@media (max-width: 768px)":{display:"none"}})),bS=b.nav(({theme:e})=>({position:"fixed",bottom:0,top:0,width:"10rem",paddingTop:"4rem",paddingBottom:"2rem",overflowY:"auto",fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s2,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch","& *":{boxSizing:"border-box"},"& > .toc-wrapper > .toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`}}},"& .toc-list-item":{position:"relative",listStyleType:"none",marginLeft:20,paddingTop:3,paddingBottom:3},"& .toc-list-item::before":{content:'""',position:"absolute",height:"100%",top:0,left:0,transform:"translateX(calc(-2px - 20px))",borderLeft:`solid 2px ${e.color.mediumdark}`,opacity:0,transition:"opacity 0.2s"},"& .toc-list-item.is-active-li::before":{opacity:1},"& .toc-list-item > a":{color:e.color.defaultText,textDecoration:"none"},"& .toc-list-item.is-active-li > a":{fontWeight:600,color:e.color.secondary,textDecoration:"none"}})),yS=b.p(({theme:e})=>({fontWeight:600,fontSize:"0.875em",color:e.textColor,textTransform:"uppercase",marginBottom:10}));function lt(){return lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},lt.apply(this,arguments)}i(lt,"t");var pm=["children","options"],L={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},Jl;(function(e){e[e.MAX=0]="MAX",e[e.HIGH=1]="HIGH",e[e.MED=2]="MED",e[e.LOW=3]="LOW",e[e.MIN=4]="MIN"})(Jl||(Jl={}));var Xl=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce((e,t)=>(e[t.toLowerCase()]=t,e),{class:"className",for:"htmlFor"}),Zl={amp:"&",apos:"'",gt:">",lt:"<",nbsp:"\xA0",quot:"\u201C"},mm=["style","script"],fm=["src","href","data","formAction","srcDoc","action"],hm=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,gm=/mailto:/i,bm=/\n{2,}$/,tu=/^(\s*>[\s\S]*?)(?=\n\n|$)/,ym=/^ *> ?/gm,Em=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,vm=/^ {2,}\n/,xm=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,ru=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,nu=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,Am=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,Cm=/^(?:\n *)*\n/,Sm=/\r\n?/g,wm=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,km=/^\[\^([^\]]+)]/,Om=/\f/g,Tm=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,Dm=/^\s*?\[(x|\s)\]/,au=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,ou=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,lu=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,ua=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,Im=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,iu=/^<!--[\s\S]*?(?:-->)/,_m=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,ca=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,Bm=/^\{.*\}$/,Rm=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,Nm=/^<([^ >]+@[^ >]+)>/,Fm=/^<([^ >]+:\/[^ >]+)>/,Lm=/-([a-z])?/gi,su=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,Pm=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,jm=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,Mm=/^\[([^\]]*)\] ?\[([^\]]*)\]/,Um=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,$m=/\t/g,Hm=/(^ *\||\| *$)/g,Vm=/^ *:-+: *$/,zm=/^ *:-+ *$/,qm=/^ *-+: *$/,ln="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",Gm=new RegExp(`^([*_])\\1${ln}\\1\\1(?!\\1)`),Wm=new RegExp(`^([*_])${ln}\\1(?!\\1)`),Km=new RegExp(`^(==)${ln}\\1`),Ym=new RegExp(`^(~~)${ln}\\1`),Jm=/^\\([^0-9A-Za-z\s])/,Ql=/\\([^0-9A-Za-z\s])/g,Xm=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,Zm=/^\n+/,Qm=/^([ \t]*)/,ef=/\\([^\\])/g,tf=/(?:^|\n)( *)$/,Ua="(?:\\d+\\.)",$a="(?:[*+-])";function Ha(e){return"( *)("+(e===1?Ua:$a)+") +"}i(Ha,"de");var uu=Ha(1),cu=Ha(2);function Va(e){return new RegExp("^"+(e===1?uu:cu))}i(Va,"fe");var rf=Va(1),nf=Va(2);function za(e){return new RegExp("^"+(e===1?uu:cu)+"[^\\n]*(?:\\n(?!\\1"+(e===1?Ua:$a)+" )[^\\n]*)*(\\n|$)","gm")}i(za,"ge");var af=za(1),of=za(2);function qa(e){let t=e===1?Ua:$a;return new RegExp("^( *)("+t+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+t+" (?!"+t+" ))\\n*|\\s*\\n*$)")}i(qa,"xe");var du=qa(1),pu=qa(2);function da(e,t){let r=t===1,a=r?du:pu,o=r?af:of,c=r?rf:nf;return{match:yt(function(l,s){let u=tf.exec(s.prevCapture);return u&&(s.list||!s.inline&&!s.simple)?a.exec(l=u[1]+l):null}),order:1,parse(l,s,u){let d=r?+l[2]:void 0,m=l[0].replace(bm,`
`).match(o),f=!1;return{items:m.map(function(p,h){let g=c.exec(p)[0].length,E=new RegExp("^ {1,"+g+"}","gm"),y=p.replace(E,"").replace(c,""),v=h===m.length-1,C=y.indexOf(`

`)!==-1||v&&f;f=C;let w=u.inline,k=u.list,D;u.list=!0,C?(u.inline=!1,D=Ft(y)+`

`):(u.inline=!0,D=Ft(y));let T=s(D,u);return u.inline=w,u.list=k,T}),ordered:r,start:d}},render:i((l,s,u)=>e(l.ordered?"ol":"ul",{key:u.key,start:l.type===L.orderedList?l.start:void 0},l.items.map(function(d,m){return e("li",{key:m},s(d,u))})),"render")}}i(da,"Ce");var lf=new RegExp(`^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['"]([\\s\\S]*?)['"])?\\s*\\)`),sf=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,mu=[tu,ru,nu,au,lu,ou,su,du,pu],uf=[...mu,/^[^\n]+(?:  \n|\n{2,})/,ua,iu,ca];function Ft(e){let t=e.length;for(;t>0&&e[t-1]<=" ";)t--;return e.slice(0,t)}i(Ft,"ze");function Dt(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}i(Dt,"Le");function fu(e){return qm.test(e)?"right":Vm.test(e)?"center":zm.test(e)?"left":null}i(fu,"Ae");function pa(e,t,r,a){let o=r.inTable;r.inTable=!0;let c=[[]],l="";function s(){if(!l)return;let u=c[c.length-1];u.push.apply(u,t(l,r)),l=""}return i(s,"a"),e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((u,d,m)=>{u.trim()==="|"&&(s(),a)?d!==0&&d!==m.length-1&&c.push([]):l+=u}),s(),r.inTable=o,c}i(pa,"Oe");function hu(e,t,r){r.inline=!0;let a=e[2]?e[2].replace(Hm,"").split("|").map(fu):[],o=e[3]?function(l,s,u){return l.trim().split(`
`).map(function(d){return pa(d,s,u,!0)})}(e[3],t,r):[],c=pa(e[1],t,r,!!o.length);return r.inline=!1,o.length?{align:a,cells:o,header:c,type:L.table}:{children:c,type:L.paragraph}}i(hu,"Te");function ma(e,t){return e.align[t]==null?{}:{textAlign:e.align[t]}}i(ma,"Be");function yt(e){return e.inline=1,e}i(yt,"Me");function We(e){return yt(function(t,r){return r.inline?e.exec(t):null})}i(We,"Re");function Ke(e){return yt(function(t,r){return r.inline||r.simple?e.exec(t):null})}i(Ke,"Ie");function ze(e){return function(t,r){return r.inline||r.simple?null:e.exec(t)}}i(ze,"De");function It(e){return yt(function(t){return e.exec(t)})}i(It,"Ue");function gu(e,t){if(t.inline||t.simple)return null;let r="";e.split(`
`).every(o=>(o+=`
`,!mu.some(c=>c.test(o))&&(r+=o,!!o.trim())));let a=Ft(r);return a==""?null:[r,,a]}i(gu,"Ne");var cf=/(javascript|vbscript|data(?!:image)):/i;function bu(e){try{let t=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(cf.test(t))return null}catch{return null}return e}i(bu,"He");function fa(e){return e.replace(ef,"$1")}i(fa,"Pe");function Zt(e,t,r){let a=r.inline||!1,o=r.simple||!1;r.inline=!0,r.simple=!0;let c=e(t,r);return r.inline=a,r.simple=o,c}i(Zt,"_e");function yu(e,t,r){let a=r.inline||!1,o=r.simple||!1;r.inline=!1,r.simple=!0;let c=e(t,r);return r.inline=a,r.simple=o,c}i(yu,"Fe");function Eu(e,t,r){let a=r.inline||!1;r.inline=!1;let o=e(t,r);return r.inline=a,o}i(Eu,"We");var Vn=i((e,t,r)=>({children:Zt(t,e[2],r)}),"Ge");function Hr(){return{}}i(Hr,"Ze");function Vr(){return null}i(Vr,"qe");function vu(...e){return e.filter(Boolean).join(" ")}i(vu,"Qe");function zr(e,t,r){let a=e,o=t.split(".");for(;o.length&&(a=a[o[0]],a!==void 0);)o.shift();return a||r}i(zr,"Ve");function xu(e="",t={}){function r(p,h,...g){let E=zr(t.overrides,`${p}.props`,{});return t.createElement(function(y,v){let C=zr(v,y);return C?typeof C=="function"||typeof C=="object"&&"render"in C?C:zr(v,`${y}.component`,y):y}(p,t.overrides),lt({},h,E,{className:vu(h?.className,E.className)||void 0}),...g)}i(r,"u");function a(p){p=p.replace(Tm,"");let h=!1;t.forceInline?h=!0:t.forceBlock||(h=Um.test(p)===!1);let g=d(u(h?p:`${Ft(p).replace(Zm,"")}

`,{inline:h}));for(;typeof g[g.length-1]=="string"&&!g[g.length-1].trim();)g.pop();if(t.wrapper===null)return g;let E=t.wrapper||(h?"span":"div"),y;if(g.length>1||t.forceWrapper)y=g;else{if(g.length===1)return y=g[0],typeof y=="string"?r("span",{key:"outer"},y):y;y=null}return t.createElement(E,{key:"outer"},y)}i(a,"Z");function o(p,h){let g=h.match(hm);return g?g.reduce(function(E,y){let v=y.indexOf("=");if(v!==-1){let C=function(T){return T.indexOf("-")!==-1&&T.match(_m)===null&&(T=T.replace(Lm,function(_,F){return F.toUpperCase()})),T}(y.slice(0,v)).trim(),w=function(T){let _=T[0];return(_==='"'||_==="'")&&T.length>=2&&T[T.length-1]===_?T.slice(1,-1):T}(y.slice(v+1).trim()),k=Xl[C]||C;if(k==="ref")return E;let D=E[k]=function(T,_,F,M){return _==="style"?function(H){let z=[],Q="",ne=!1,A=!1,x="";if(!H)return z;for(let B=0;B<H.length;B++){let O=H[B];if(O!=='"'&&O!=="'"||ne||(A?O===x&&(A=!1,x=""):(A=!0,x=O)),O==="("&&Q.endsWith("url")?ne=!0:O===")"&&ne&&(ne=!1),O!==";"||A||ne)Q+=O;else{let I=Q.trim();if(I){let N=I.indexOf(":");if(N>0){let V=I.slice(0,N).trim(),J=I.slice(N+1).trim();z.push([V,J])}}Q=""}}let S=Q.trim();if(S){let B=S.indexOf(":");if(B>0){let O=S.slice(0,B).trim(),I=S.slice(B+1).trim();z.push([O,I])}}return z}(F).reduce(function(H,[z,Q]){return H[z.replace(/(-[a-z])/g,ne=>ne[1].toUpperCase())]=M(Q,T,z),H},{}):fm.indexOf(_)!==-1?M(F,T,_):(F.match(Bm)&&(F=F.slice(1,F.length-1)),F==="true"||F!=="false"&&F)}(p,C,w,t.sanitizer);typeof D=="string"&&(ua.test(D)||ca.test(D))&&(E[k]=a(D.trim()))}else y!=="style"&&(E[Xl[y]||y]=!0);return E},{}):null}i(o,"q"),t.overrides=t.overrides||{},t.sanitizer=t.sanitizer||bu,t.slugify=t.slugify||Dt,t.namedCodesToUnicode=t.namedCodesToUnicode?lt({},Zl,t.namedCodesToUnicode):Zl,t.createElement=t.createElement||P;let c=[],l={},s={[L.blockQuote]:{match:ze(tu),order:1,parse(p,h,g){let[,E,y]=p[0].replace(ym,"").match(Em);return{alert:E,children:h(y,g)}},render(p,h,g){let E={key:g.key};return p.alert&&(E.className="markdown-alert-"+t.slugify(p.alert.toLowerCase(),Dt),p.children.unshift({attrs:{},children:[{type:L.text,text:p.alert}],noInnerParse:!0,type:L.htmlBlock,tag:"header"})),r("blockquote",E,h(p.children,g))}},[L.breakLine]:{match:It(vm),order:1,parse:Hr,render:i((p,h,g)=>r("br",{key:g.key}),"render")},[L.breakThematic]:{match:ze(xm),order:1,parse:Hr,render:i((p,h,g)=>r("hr",{key:g.key}),"render")},[L.codeBlock]:{match:ze(nu),order:0,parse:i(p=>({lang:void 0,text:Ft(p[0].replace(/^ {4}/gm,"")).replace(Ql,"$1")}),"parse"),render:i((p,h,g)=>r("pre",{key:g.key},r("code",lt({},p.attrs,{className:p.lang?`lang-${p.lang}`:""}),p.text)),"render")},[L.codeFenced]:{match:ze(ru),order:0,parse:i(p=>({attrs:o("code",p[3]||""),lang:p[2]||void 0,text:p[4],type:L.codeBlock}),"parse")},[L.codeInline]:{match:Ke(Am),order:3,parse:i(p=>({text:p[2].replace(Ql,"$1")}),"parse"),render:i((p,h,g)=>r("code",{key:g.key},p.text),"render")},[L.footnote]:{match:ze(wm),order:0,parse:i(p=>(c.push({footnote:p[2],identifier:p[1]}),{}),"parse"),render:Vr},[L.footnoteReference]:{match:We(km),order:1,parse:i(p=>({target:`#${t.slugify(p[1],Dt)}`,text:p[1]}),"parse"),render:i((p,h,g)=>r("a",{key:g.key,href:t.sanitizer(p.target,"a","href")},r("sup",{key:g.key},p.text)),"render")},[L.gfmTask]:{match:We(Dm),order:1,parse:i(p=>({completed:p[1].toLowerCase()==="x"}),"parse"),render:i((p,h,g)=>r("input",{checked:p.completed,key:g.key,readOnly:!0,type:"checkbox"}),"render")},[L.heading]:{match:ze(t.enforceAtxHeadings?ou:au),order:1,parse:i((p,h,g)=>({children:Zt(h,p[2],g),id:t.slugify(p[2],Dt),level:p[1].length}),"parse"),render:i((p,h,g)=>r(`h${p.level}`,{id:p.id,key:g.key},h(p.children,g)),"render")},[L.headingSetext]:{match:ze(lu),order:0,parse:i((p,h,g)=>({children:Zt(h,p[1],g),level:p[2]==="="?1:2,type:L.heading}),"parse")},[L.htmlBlock]:{match:It(ua),order:1,parse(p,h,g){let[,E]=p[3].match(Qm),y=new RegExp(`^${E}`,"gm"),v=p[3].replace(y,""),C=(w=v,uf.some(F=>F.test(w))?Eu:Zt);var w;let k=p[1].toLowerCase(),D=mm.indexOf(k)!==-1,T=(D?k:p[1]).trim(),_={attrs:o(T,p[2]),noInnerParse:D,tag:T};return g.inAnchor=g.inAnchor||k==="a",D?_.text=p[3]:_.children=C(h,v,g),g.inAnchor=!1,_},render:i((p,h,g)=>r(p.tag,lt({key:g.key},p.attrs),p.text||(p.children?h(p.children,g):"")),"render")},[L.htmlSelfClosing]:{match:It(ca),order:1,parse(p){let h=p[1].trim();return{attrs:o(h,p[2]||""),tag:h}},render:i((p,h,g)=>r(p.tag,lt({},p.attrs,{key:g.key})),"render")},[L.htmlComment]:{match:It(iu),order:1,parse:i(()=>({}),"parse"),render:Vr},[L.image]:{match:Ke(sf),order:1,parse:i(p=>({alt:p[1],target:fa(p[2]),title:p[3]}),"parse"),render:i((p,h,g)=>r("img",{key:g.key,alt:p.alt||void 0,title:p.title||void 0,src:t.sanitizer(p.target,"img","src")}),"render")},[L.link]:{match:We(lf),order:3,parse:i((p,h,g)=>({children:yu(h,p[1],g),target:fa(p[2]),title:p[3]}),"parse"),render:i((p,h,g)=>r("a",{key:g.key,href:t.sanitizer(p.target,"a","href"),title:p.title},h(p.children,g)),"render")},[L.linkAngleBraceStyleDetector]:{match:We(Fm),order:0,parse:i(p=>({children:[{text:p[1],type:L.text}],target:p[1],type:L.link}),"parse")},[L.linkBareUrlDetector]:{match:yt((p,h)=>h.inAnchor||t.disableAutoLink?null:We(Rm)(p,h)),order:0,parse:i(p=>({children:[{text:p[1],type:L.text}],target:p[1],title:void 0,type:L.link}),"parse")},[L.linkMailtoDetector]:{match:We(Nm),order:0,parse(p){let h=p[1],g=p[1];return gm.test(g)||(g="mailto:"+g),{children:[{text:h.replace("mailto:",""),type:L.text}],target:g,type:L.link}}},[L.orderedList]:da(r,1),[L.unorderedList]:da(r,2),[L.newlineCoalescer]:{match:ze(Cm),order:3,parse:Hr,render:i(()=>`
`,"render")},[L.paragraph]:{match:yt(gu),order:3,parse:Vn,render:i((p,h,g)=>r("p",{key:g.key},h(p.children,g)),"render")},[L.ref]:{match:We(Pm),order:0,parse:i(p=>(l[p[1]]={target:p[2],title:p[4]},{}),"parse"),render:Vr},[L.refImage]:{match:Ke(jm),order:0,parse:i(p=>({alt:p[1]||void 0,ref:p[2]}),"parse"),render:i((p,h,g)=>l[p.ref]?r("img",{key:g.key,alt:p.alt,src:t.sanitizer(l[p.ref].target,"img","src"),title:l[p.ref].title}):null,"render")},[L.refLink]:{match:We(Mm),order:0,parse:i((p,h,g)=>({children:h(p[1],g),fallbackChildren:p[0],ref:p[2]}),"parse"),render:i((p,h,g)=>l[p.ref]?r("a",{key:g.key,href:t.sanitizer(l[p.ref].target,"a","href"),title:l[p.ref].title},h(p.children,g)):r("span",{key:g.key},p.fallbackChildren),"render")},[L.table]:{match:ze(su),order:1,parse:hu,render(p,h,g){let E=p;return r("table",{key:g.key},r("thead",null,r("tr",null,E.header.map(function(y,v){return r("th",{key:v,style:ma(E,v)},h(y,g))}))),r("tbody",null,E.cells.map(function(y,v){return r("tr",{key:v},y.map(function(C,w){return r("td",{key:w,style:ma(E,w)},h(C,g))}))})))}},[L.text]:{match:It(Xm),order:4,parse:i(p=>({text:p[0].replace(Im,(h,g)=>t.namedCodesToUnicode[g]?t.namedCodesToUnicode[g]:h)}),"parse"),render:i(p=>p.text,"render")},[L.textBolded]:{match:Ke(Gm),order:2,parse:i((p,h,g)=>({children:h(p[2],g)}),"parse"),render:i((p,h,g)=>r("strong",{key:g.key},h(p.children,g)),"render")},[L.textEmphasized]:{match:Ke(Wm),order:3,parse:i((p,h,g)=>({children:h(p[2],g)}),"parse"),render:i((p,h,g)=>r("em",{key:g.key},h(p.children,g)),"render")},[L.textEscaped]:{match:Ke(Jm),order:1,parse:i(p=>({text:p[1],type:L.text}),"parse")},[L.textMarked]:{match:Ke(Km),order:3,parse:Vn,render:i((p,h,g)=>r("mark",{key:g.key},h(p.children,g)),"render")},[L.textStrikethroughed]:{match:Ke(Ym),order:3,parse:Vn,render:i((p,h,g)=>r("del",{key:g.key},h(p.children,g)),"render")}};t.disableParsingRawHTML===!0&&(delete s[L.htmlBlock],delete s[L.htmlSelfClosing]);let u=function(p){let h=Object.keys(p);function g(E,y){let v,C,w=[],k="",D="";for(y.prevCapture=y.prevCapture||"";E;){let T=0;for(;T<h.length;){if(k=h[T],v=p[k],y.inline&&!v.match.inline){T++;continue}let _=v.match(E,y);if(_){D=_[0],y.prevCapture+=D,E=E.substring(D.length),C=v.parse(_,g,y),C.type==null&&(C.type=k),w.push(C);break}T++}}return y.prevCapture="",w}return i(g,"n"),h.sort(function(E,y){let v=p[E].order,C=p[y].order;return v!==C?v-C:E<y?-1:1}),function(E,y){return g(function(v){return v.replace(Sm,`
`).replace(Om,"").replace($m,"    ")}(E),y)}}(s),d=(m=function(p,h){return function(g,E,y){let v=p[g.type].render;return h?h(()=>v(g,E,y),g,E,y):v(g,E,y)}}(s,t.renderRule),i(function p(h,g={}){if(Array.isArray(h)){let E=g.key,y=[],v=!1;for(let C=0;C<h.length;C++){g.key=C;let w=p(h[C],g),k=typeof w=="string";k&&v?y[y.length-1]+=w:w!==null&&y.push(w),v=k}return g.key=E,y}return m(h,p,g)},"e"));var m;let f=a(e);return c.length?r("div",null,f,r("footer",{key:"footer"},c.map(function(p){return r("div",{id:t.slugify(p.identifier,Dt),key:p.identifier},p.identifier,d(u(p.footnote,{inline:!0})))}))):f}i(xu,"Xe");var df=i(e=>{let{children:t="",options:r}=e,a=function(o,c){if(o==null)return{};var l,s,u={},d=Object.keys(o);for(s=0;s<d.length;s++)c.indexOf(l=d[s])>=0||(u[l]=o[l]);return u}(e,pm);return ae(xu(t,r),a)},"default");je();var pf=b.label(({theme:e})=>({lineHeight:"18px",alignItems:"center",marginBottom:8,display:"inline-block",position:"relative",whiteSpace:"nowrap",background:e.boolean.background,borderRadius:"3em",padding:1,'&[aria-disabled="true"]':{opacity:.5,input:{cursor:"not-allowed"}},input:{appearance:"none",width:"100%",height:"100%",position:"absolute",left:0,top:0,margin:0,padding:0,border:"none",background:"transparent",cursor:"pointer",borderRadius:"3em","&:focus":{outline:"none",boxShadow:`${e.color.secondary} 0 0 0 1px inset !important`}},span:{textAlign:"center",fontSize:e.typography.size.s1,fontWeight:e.typography.weight.bold,lineHeight:"1",cursor:"pointer",display:"inline-block",padding:"7px 15px",transition:"all 100ms ease-out",userSelect:"none",borderRadius:"3em",color:W(.5,e.color.defaultText),background:"transparent","&:hover":{boxShadow:`${Xt(.3,e.appBorderColor)} 0 0 0 1px inset`},"&:active":{boxShadow:`${Xt(.05,e.appBorderColor)} 0 0 0 2px inset`,color:Xt(1,e.appBorderColor)},"&:first-of-type":{paddingRight:8},"&:last-of-type":{paddingLeft:8}},"input:checked ~ span:last-of-type, input:not(:checked) ~ span:first-of-type":{background:e.boolean.selectedBackground,boxShadow:e.base==="light"?`${Xt(.1,e.appBorderColor)} 0 0 2px`:`${e.appBorderColor} 0 0 0 1px`,color:e.color.defaultText,padding:"7px 15px"}})),mf=i(e=>e==="true","parse"),ff=i(({name:e,value:t,onChange:r,onBlur:a,onFocus:o,argType:c})=>{let l=$(()=>r(!1),[r]),s=!!c?.table?.readonly;if(t===void 0)return n.createElement(ge,{variant:"outline",size:"medium",id:or(e),onClick:l,disabled:s},"Set boolean");let u=Oe(e),d=typeof t=="string"?mf(t):t;return n.createElement(pf,{"aria-disabled":s,htmlFor:u,"aria-label":e},n.createElement("input",{id:u,type:"checkbox",onChange:m=>r(m.target.checked),checked:d,role:"switch",disabled:s,name:e,onBlur:a,onFocus:o}),n.createElement("span",{"aria-hidden":"true"},"False"),n.createElement("span",{"aria-hidden":"true"},"True"))},"BooleanControl");je();var hf=i(e=>{let[t,r,a]=e.split("-"),o=new Date;return o.setFullYear(parseInt(t,10),parseInt(r,10)-1,parseInt(a,10)),o},"parseDate"),gf=i(e=>{let[t,r]=e.split(":"),a=new Date;return a.setHours(parseInt(t,10)),a.setMinutes(parseInt(r,10)),a},"parseTime"),bf=i(e=>{let t=new Date(e),r=`000${t.getFullYear()}`.slice(-4),a=`0${t.getMonth()+1}`.slice(-2),o=`0${t.getDate()}`.slice(-2);return`${r}-${a}-${o}`},"formatDate"),yf=i(e=>{let t=new Date(e),r=`0${t.getHours()}`.slice(-2),a=`0${t.getMinutes()}`.slice(-2);return`${r}:${a}`},"formatTime"),ei=b(de.Input)(({readOnly:e})=>({opacity:e?.5:1})),Ef=b.div(({theme:e})=>({flex:1,display:"flex",input:{marginLeft:10,flex:1,height:32,"&::-webkit-calendar-picker-indicator":{opacity:.5,height:12,filter:e.base==="light"?void 0:"invert(1)"}},"input:first-of-type":{marginLeft:0,flexGrow:4},"input:last-of-type":{flexGrow:3}})),vf=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,argType:c})=>{let[l,s]=R(!0),u=X(),d=X(),m=!!c?.table?.readonly;j(()=>{l!==!1&&(u&&u.current&&(u.current.value=t?bf(t):""),d&&d.current&&(d.current.value=t?yf(t):""))},[t]);let f=i(g=>{if(!g.target.value)return r();let E=hf(g.target.value),y=new Date(t??"");y.setFullYear(E.getFullYear(),E.getMonth(),E.getDate());let v=y.getTime();v&&r(v),s(!!v)},"onDateChange"),p=i(g=>{if(!g.target.value)return r();let E=gf(g.target.value),y=new Date(t??"");y.setHours(E.getHours()),y.setMinutes(E.getMinutes());let v=y.getTime();v&&r(v),s(!!v)},"onTimeChange"),h=Oe(e);return n.createElement(Ef,null,n.createElement(ei,{type:"date",max:"9999-12-31",ref:u,id:`${h}-date`,name:`${h}-date`,readOnly:m,onChange:f,onFocus:a,onBlur:o}),n.createElement(ei,{type:"time",id:`${h}-time`,name:`${h}-time`,ref:d,onChange:p,readOnly:m,onFocus:a,onBlur:o}),l?null:n.createElement("div",null,"invalid"))},"DateControl");je();var xf=b.label({display:"flex"}),Af=i(e=>{let t=parseFloat(e);return Number.isNaN(t)?void 0:t},"parse"),Cf=b(de.Input)(({readOnly:e})=>({opacity:e?.5:1})),Sf=i(({name:e,value:t,onChange:r,min:a,max:o,step:c,onBlur:l,onFocus:s,argType:u})=>{let[d,m]=R(typeof t=="number"?t:""),[f,p]=R(!1),[h,g]=R(null),E=!!u?.table?.readonly,y=$(w=>{m(w.target.value);let k=parseFloat(w.target.value);Number.isNaN(k)?g(new Error(`'${w.target.value}' is not a number`)):(r(k),g(null))},[r,g]),v=$(()=>{m("0"),r(0),p(!0)},[p]),C=X(null);return j(()=>{f&&C.current&&C.current.select()},[f]),j(()=>{let w=typeof t=="number"?t:"";d!==w&&m(w)},[t]),t===void 0?n.createElement(ge,{variant:"outline",size:"medium",id:or(e),onClick:v,disabled:E},"Set number"):n.createElement(xf,null,n.createElement(Cf,{ref:C,id:Oe(e),type:"number",onChange:y,size:"flex",placeholder:"Edit number...",value:d,valid:h?"error":void 0,autoFocus:f,readOnly:E,name:e,min:a,max:o,step:c,onFocus:s,onBlur:l}))},"NumberControl");je();var Au=i((e,t)=>{let r=t&&Object.entries(t).find(([a,o])=>o===e);return r?r[0]:void 0},"selectedKey"),ha=i((e,t)=>e&&t?Object.entries(t).filter(r=>e.includes(r[1])).map(r=>r[0]):[],"selectedKeys"),Cu=i((e,t)=>e&&t&&e.map(r=>t[r]),"selectedValues"),wf=b.div(({isInline:e})=>e?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},e=>{if(e["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),kf=b.span({"[aria-readonly=true] &":{opacity:.5}}),Of=b.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),ti=i(({name:e,options:t,value:r,onChange:a,isInline:o,argType:c})=>{if(!t)return tt.warn(`Checkbox with no options: ${e}`),n.createElement(n.Fragment,null,"-");let l=ha(r||[],t),[s,u]=R(l),d=!!c?.table?.readonly,m=i(p=>{let h=p.target.value,g=[...s];g.includes(h)?g.splice(g.indexOf(h),1):g.push(h),a(Cu(g,t)),u(g)},"handleChange");j(()=>{u(ha(r||[],t))},[r]);let f=Oe(e);return n.createElement(wf,{"aria-readonly":d,isInline:o},Object.keys(t).map((p,h)=>{let g=`${f}-${h}`;return n.createElement(Of,{key:g,htmlFor:g},n.createElement("input",{type:"checkbox",disabled:d,id:g,name:g,value:p,onChange:m,checked:s?.includes(p)}),n.createElement(kf,null,p))}))},"CheckboxControl");je();var Tf=b.div(({isInline:e})=>e?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},e=>{if(e["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),Df=b.span({"[aria-readonly=true] &":{opacity:.5}}),If=b.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),ri=i(({name:e,options:t,value:r,onChange:a,isInline:o,argType:c})=>{if(!t)return tt.warn(`Radio with no options: ${e}`),n.createElement(n.Fragment,null,"-");let l=Au(r,t),s=Oe(e),u=!!c?.table?.readonly;return n.createElement(Tf,{"aria-readonly":u,isInline:o},Object.keys(t).map((d,m)=>{let f=`${s}-${m}`;return n.createElement(If,{key:f,htmlFor:f},n.createElement("input",{type:"radio",id:f,name:s,disabled:u,value:d,onChange:p=>a(t[p.currentTarget.value]),checked:d===l}),n.createElement(Df,null,d))}))},"RadioControl");je();var _f={appearance:"none",border:"0 none",boxSizing:"inherit",display:" block",margin:" 0",background:"transparent",padding:0,fontSize:"inherit",position:"relative"},Su=b.select(_f,({theme:e})=>({boxSizing:"border-box",position:"relative",padding:"6px 10px",width:"100%",color:e.input.color||"inherit",background:e.input.background,borderRadius:e.input.borderRadius,boxShadow:`${e.input.border} 0 0 0 1px inset`,fontSize:e.typography.size.s2-1,lineHeight:"20px","&:focus":{boxShadow:`${e.color.secondary} 0 0 0 1px inset`,outline:"none"},"&[disabled]":{cursor:"not-allowed",opacity:.5},"::placeholder":{color:e.textMutedColor},"&[multiple]":{overflow:"auto",padding:0,option:{display:"block",padding:"6px 10px",marginLeft:1,marginRight:1}}})),wu=b.span(({theme:e})=>({display:"inline-block",lineHeight:"normal",overflow:"hidden",position:"relative",verticalAlign:"top",width:"100%",svg:{position:"absolute",zIndex:1,pointerEvents:"none",height:"12px",marginTop:"-6px",right:"12px",top:"50%",fill:e.textMutedColor,path:{fill:e.textMutedColor}}})),ni="Choose option...",Bf=i(({name:e,value:t,options:r,onChange:a,argType:o})=>{let c=i(d=>{a(r[d.currentTarget.value])},"handleChange"),l=Au(t,r)||ni,s=Oe(e),u=!!o?.table?.readonly;return n.createElement(wu,null,n.createElement(gr,null),n.createElement(Su,{disabled:u,id:s,value:l,onChange:c},n.createElement("option",{key:"no-selection",disabled:!0},ni),Object.keys(r).map(d=>n.createElement("option",{key:d,value:d},d))))},"SingleSelect"),Rf=i(({name:e,value:t,options:r,onChange:a,argType:o})=>{let c=i(d=>{let m=Array.from(d.currentTarget.options).filter(f=>f.selected).map(f=>f.value);a(Cu(m,r))},"handleChange"),l=ha(t,r),s=Oe(e),u=!!o?.table?.readonly;return n.createElement(wu,null,n.createElement(Su,{disabled:u,id:s,multiple:!0,value:l,onChange:c},Object.keys(r).map(d=>n.createElement("option",{key:d,value:d},d))))},"MultiSelect"),ai=i(e=>{let{name:t,options:r}=e;return r?e.isMulti?n.createElement(Rf,{...e}):n.createElement(Bf,{...e}):(tt.warn(`Select with no options: ${t}`),n.createElement(n.Fragment,null,"-"))},"SelectControl"),Nf=i((e,t)=>Array.isArray(e)?e.reduce((r,a)=>(r[t?.[a]||String(a)]=a,r),{}):e,"normalizeOptions"),Ff={check:ti,"inline-check":ti,radio:ri,"inline-radio":ri,select:ai,"multi-select":ai},Ot=i(e=>{let{type:t="select",labels:r,argType:a}=e,o={...e,argType:a,options:a?Nf(a.options,r):{},isInline:t.includes("inline"),isMulti:t.includes("multi")},c=Ff[t];if(c)return n.createElement(c,{...o});throw new Error(`Unknown options type: ${t}`)},"OptionsControl");an();je();var Lf="Error",Pf="Object",jf="Array",Mf="String",Uf="Number",$f="Boolean",Hf="Date",Vf="Null",zf="Undefined",qf="Function",Gf="Symbol",ku="ADD_DELTA_TYPE",Ou="REMOVE_DELTA_TYPE",Tu="UPDATE_DELTA_TYPE",Ga="value",Wf="key";function it(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)&&typeof e[Symbol.iterator]=="function"?"Iterable":Object.prototype.toString.call(e).slice(8,-1)}i(it,"getObjectType");function Wa(e,t){let r=it(e),a=it(t);return(r==="Function"||a==="Function")&&a!==r}i(Wa,"isComponentWillChange");var Du=class extends Re{constructor(t){super(t),this.state={inputRefKey:null,inputRefValue:null},this.refInputValue=this.refInputValue.bind(this),this.refInputKey=this.refInputKey.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onSubmit=this.onSubmit.bind(this)}componentDidMount(){let{inputRefKey:t,inputRefValue:r}=this.state,{onlyValue:a}=this.props;t&&typeof t.focus=="function"&&t.focus(),a&&r&&typeof r.focus=="function"&&r.focus(),document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.onSubmit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.props.handleCancel()))}onSubmit(){let{handleAdd:t,onlyValue:r,onSubmitValueParser:a,keyPath:o,deep:c}=this.props,{inputRefKey:l,inputRefValue:s}=this.state,u={};if(!r){if(!l.value)return;u.key=l.value}u.newValue=a(!1,o,c,u.key,s.value),t(u)}refInputKey(t){this.state.inputRefKey=t}refInputValue(t){this.state.inputRefValue=t}render(){let{handleCancel:t,onlyValue:r,addButtonElement:a,cancelButtonElement:o,inputElementGenerator:c,keyPath:l,deep:s}=this.props,u=a&&ae(a,{onClick:this.onSubmit}),d=o&&ae(o,{onClick:t}),m=c(Ga,l,s),f=ae(m,{placeholder:"Value",ref:this.refInputValue}),p=null;if(!r){let h=c(Wf,l,s);p=ae(h,{placeholder:"Key",ref:this.refInputKey})}return n.createElement("span",{className:"rejt-add-value-node"},p,f,d,u)}};i(Du,"JsonAddValue");var Ka=Du;Ka.defaultProps={onlyValue:!1,addButtonElement:n.createElement("button",null,"+"),cancelButtonElement:n.createElement("button",null,"c")};var Iu=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={data:t.data,name:t.name,keyPath:r,deep:t.deep??0,nextDeep:(t.deep??0)+1,collapsed:t.isCollapsed(r,t.deep??0,t.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveItem=this.handleRemoveItem.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}onChildUpdate(t,r){let{data:a,keyPath:o=[]}=this.state;a[t]=r,this.setState({data:a});let{onUpdate:c}=this.props,l=o.length;c(o[l-1],a)}handleAddMode(){this.setState({addFormVisible:!0})}handleCollapseMode(){this.setState(t=>({collapsed:!t.collapsed}))}handleRemoveItem(t){return()=>{let{beforeRemoveAction:r,logger:a}=this.props,{data:o,keyPath:c,nextDeep:l}=this.state,s=o[t];(r||Promise.resolve.bind(Promise))(t,c,l,s).then(()=>{let u={keyPath:c,deep:l,key:t,oldValue:s,type:Ou};o.splice(t,1),this.setState({data:o});let{onUpdate:d,onDeltaUpdate:m}=this.props;d(c[c.length-1],o),m(u)}).catch(a.error)}}handleAddValueAdd({key:t,newValue:r}){let{data:a,keyPath:o=[],nextDeep:c}=this.state,{beforeAddAction:l,logger:s}=this.props;(l||Promise.resolve.bind(Promise))(t,o,c,r).then(()=>{a[t]=r,this.setState({data:a}),this.handleAddValueCancel();let{onUpdate:u,onDeltaUpdate:d}=this.props;u(o[o.length-1],a),d({type:ku,keyPath:o,deep:c,key:t,newValue:r})}).catch(s.error)}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleEditValue({key:t,value:r}){return new Promise((a,o)=>{let{beforeUpdateAction:c}=this.props,{data:l,keyPath:s,nextDeep:u}=this.state,d=l[t];(c||Promise.resolve.bind(Promise))(t,s,u,d,r).then(()=>{l[t]=r,this.setState({data:l});let{onUpdate:m,onDeltaUpdate:f}=this.props;m(s[s.length-1],l),f({type:Tu,keyPath:s,deep:u,key:t,newValue:r,oldValue:d}),a(void 0)}).catch(o)})}renderCollapsed(){let{name:t,data:r,keyPath:a,deep:o}=this.state,{handleRemove:c,readOnly:l,getStyle:s,dataType:u,minusMenuElement:d}=this.props,{minus:m,collapsed:f}=s(t,r,a,o,u),p=l(t,r,a,o,u),h=d&&ae(d,{onClick:c,className:"rejt-minus-menu",style:m});return n.createElement("span",{className:"rejt-collapsed"},n.createElement("span",{className:"rejt-collapsed-text",style:f,onClick:this.handleCollapseMode},"[...] ",r.length," ",r.length===1?"item":"items"),!p&&h)}renderNotCollapsed(){let{name:t,data:r,keyPath:a,deep:o,addFormVisible:c,nextDeep:l}=this.state,{isCollapsed:s,handleRemove:u,onDeltaUpdate:d,readOnly:m,getStyle:f,dataType:p,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:D,beforeUpdateAction:T,logger:_,onSubmitValueParser:F}=this.props,{minus:M,plus:H,delimiter:z,ul:Q,addForm:ne}=f(t,r,a,o,p),A=m(t,r,a,o,p),x=w&&ae(w,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:H}),S=C&&ae(C,{onClick:u,className:"rejt-minus-menu",style:M});return n.createElement("span",{className:"rejt-not-collapsed"},n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:z},"["),!c&&x,n.createElement("ul",{className:"rejt-not-collapsed-list",style:Q},r.map((B,O)=>n.createElement(sn,{key:O,name:O.toString(),data:B,keyPath:a,deep:l,isCollapsed:s,handleRemove:this.handleRemoveItem(O),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:D,beforeUpdateAction:T,logger:_,onSubmitValueParser:F}))),!A&&c&&n.createElement("div",{className:"rejt-add-form",style:ne},n.createElement(Ka,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,onlyValue:!0,addButtonElement:h,cancelButtonElement:g,inputElementGenerator:y,keyPath:a,deep:o,onSubmitValueParser:F})),n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:z},"]"),!A&&S)}render(){let{name:t,collapsed:r,data:a,keyPath:o,deep:c}=this.state,{dataType:l,getStyle:s}=this.props,u=r?this.renderCollapsed():this.renderNotCollapsed(),d=s(t,a,o,c,l);return n.createElement("div",{className:"rejt-array-node"},n.createElement("span",{onClick:this.handleCollapseMode},n.createElement("span",{className:"rejt-name",style:d.name},t," :"," ")),u)}};i(Iu,"JsonArray");var _u=Iu;_u.defaultProps={keyPath:[],deep:0,minusMenuElement:n.createElement("span",null," - "),plusMenuElement:n.createElement("span",null," + ")};var Bu=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={value:t.value,name:t.name,keyPath:r,deep:t.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(t,r){return t.value!==r.value?{value:t.value}:null}componentDidUpdate(){let{editEnabled:t,inputRef:r,name:a,value:o,keyPath:c,deep:l}=this.state,{readOnly:s,dataType:u}=this.props,d=s(a,o,c,l,u);t&&!d&&typeof r.focus=="function"&&r.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.handleEdit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue:t,originalValue:r,logger:a,onSubmitValueParser:o,keyPath:c}=this.props,{inputRef:l,name:s,deep:u}=this.state;if(!l)return;let d=o(!0,c,u,s,l.value),m={value:d,key:s};(t||Promise.resolve.bind(Promise))(m).then(()=>{Wa(r,d)||this.handleCancelEdit()}).catch(a.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(t){this.state.inputRef=t}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:t,value:r,editEnabled:a,keyPath:o,deep:c}=this.state,{handleRemove:l,originalValue:s,readOnly:u,dataType:d,getStyle:m,editButtonElement:f,cancelButtonElement:p,textareaElementGenerator:h,minusMenuElement:g,keyPath:E}=this.props,y=m(t,s,o,c,d),v=null,C=null,w=u(t,s,o,c,d);if(a&&!w){let k=h(Ga,E,c,t,s,d),D=f&&ae(f,{onClick:this.handleEdit}),T=p&&ae(p,{onClick:this.handleCancelEdit}),_=ae(k,{ref:this.refInput,defaultValue:s});v=n.createElement("span",{className:"rejt-edit-form",style:y.editForm},_," ",T,D),C=null}else{v=n.createElement("span",{className:"rejt-value",style:y.value,onClick:w?void 0:this.handleEditMode},r);let k=g&&ae(g,{onClick:l,className:"rejt-minus-menu",style:y.minus});C=w?null:k}return n.createElement("li",{className:"rejt-function-value-node",style:y.li},n.createElement("span",{className:"rejt-name",style:y.name},t," :"," "),v,C)}};i(Bu,"JsonFunctionValue");var Ru=Bu;Ru.defaultProps={keyPath:[],deep:0,handleUpdateValue:i(()=>{},"handleUpdateValue"),editButtonElement:n.createElement("button",null,"e"),cancelButtonElement:n.createElement("button",null,"c"),minusMenuElement:n.createElement("span",null," - ")};var Nu=class extends Re{constructor(t){super(t),this.state={data:t.data,name:t.name,keyPath:t.keyPath,deep:t.deep}}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}render(){let{data:t,name:r,keyPath:a,deep:o}=this.state,{isCollapsed:c,handleRemove:l,handleUpdateValue:s,onUpdate:u,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:D,logger:T,onSubmitValueParser:_}=this.props,F=i(()=>!0,"readOnlyTrue"),M=it(t);switch(M){case Lf:return n.createElement(ga,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:u,onDeltaUpdate:d,readOnly:F,dataType:M,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:D,logger:T,onSubmitValueParser:_});case Pf:return n.createElement(ga,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:u,onDeltaUpdate:d,readOnly:m,dataType:M,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:D,logger:T,onSubmitValueParser:_});case jf:return n.createElement(_u,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:u,onDeltaUpdate:d,readOnly:m,dataType:M,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:D,logger:T,onSubmitValueParser:_});case Mf:return n.createElement(nt,{name:r,value:`"${t}"`,originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:_});case Uf:return n.createElement(nt,{name:r,value:t,originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:_});case $f:return n.createElement(nt,{name:r,value:t?"true":"false",originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:_});case Hf:return n.createElement(nt,{name:r,value:t.toISOString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:F,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:_});case Vf:return n.createElement(nt,{name:r,value:"null",originalValue:"null",keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:_});case zf:return n.createElement(nt,{name:r,value:"undefined",originalValue:"undefined",keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:_});case qf:return n.createElement(Ru,{name:r,value:t.toString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,textareaElementGenerator:y,minusMenuElement:v,logger:T,onSubmitValueParser:_});case Gf:return n.createElement(nt,{name:r,value:t.toString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:s,readOnly:F,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:_});default:return null}}};i(Nu,"JsonNode");var sn=Nu;sn.defaultProps={keyPath:[],deep:0};var Fu=class extends Re{constructor(t){super(t);let r=t.deep===-1?[]:[...t.keyPath||[],t.name];this.state={name:t.name,data:t.data,keyPath:r,deep:t.deep??0,nextDeep:(t.deep??0)+1,collapsed:t.isCollapsed(r,t.deep??0,t.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveValue=this.handleRemoveValue.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}onChildUpdate(t,r){let{data:a,keyPath:o=[]}=this.state;a[t]=r,this.setState({data:a});let{onUpdate:c}=this.props,l=o.length;c(o[l-1],a)}handleAddMode(){this.setState({addFormVisible:!0})}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleAddValueAdd({key:t,newValue:r}){let{data:a,keyPath:o=[],nextDeep:c}=this.state,{beforeAddAction:l,logger:s}=this.props;(l||Promise.resolve.bind(Promise))(t,o,c,r).then(()=>{a[t]=r,this.setState({data:a}),this.handleAddValueCancel();let{onUpdate:u,onDeltaUpdate:d}=this.props;u(o[o.length-1],a),d({type:ku,keyPath:o,deep:c,key:t,newValue:r})}).catch(s.error)}handleRemoveValue(t){return()=>{let{beforeRemoveAction:r,logger:a}=this.props,{data:o,keyPath:c=[],nextDeep:l}=this.state,s=o[t];(r||Promise.resolve.bind(Promise))(t,c,l,s).then(()=>{let u={keyPath:c,deep:l,key:t,oldValue:s,type:Ou};delete o[t],this.setState({data:o});let{onUpdate:d,onDeltaUpdate:m}=this.props;d(c[c.length-1],o),m(u)}).catch(a.error)}}handleCollapseMode(){this.setState(t=>({collapsed:!t.collapsed}))}handleEditValue({key:t,value:r}){return new Promise((a,o)=>{let{beforeUpdateAction:c}=this.props,{data:l,keyPath:s=[],nextDeep:u}=this.state,d=l[t];(c||Promise.resolve.bind(Promise))(t,s,u,d,r).then(()=>{l[t]=r,this.setState({data:l});let{onUpdate:m,onDeltaUpdate:f}=this.props;m(s[s.length-1],l),f({type:Tu,keyPath:s,deep:u,key:t,newValue:r,oldValue:d}),a()}).catch(o)})}renderCollapsed(){let{name:t,keyPath:r,deep:a,data:o}=this.state,{handleRemove:c,readOnly:l,dataType:s,getStyle:u,minusMenuElement:d}=this.props,{minus:m,collapsed:f}=u(t,o,r,a,s),p=Object.getOwnPropertyNames(o),h=l(t,o,r,a,s),g=d&&ae(d,{onClick:c,className:"rejt-minus-menu",style:m});return n.createElement("span",{className:"rejt-collapsed"},n.createElement("span",{className:"rejt-collapsed-text",style:f,onClick:this.handleCollapseMode},"{...}"," ",p.length," ",p.length===1?"key":"keys"),!h&&g)}renderNotCollapsed(){let{name:t,data:r,keyPath:a,deep:o,nextDeep:c,addFormVisible:l}=this.state,{isCollapsed:s,handleRemove:u,onDeltaUpdate:d,readOnly:m,getStyle:f,dataType:p,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:D,beforeUpdateAction:T,logger:_,onSubmitValueParser:F}=this.props,{minus:M,plus:H,addForm:z,ul:Q,delimiter:ne}=f(t,r,a,o,p),A=Object.getOwnPropertyNames(r),x=m(t,r,a,o,p),S=w&&ae(w,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:H}),B=C&&ae(C,{onClick:u,className:"rejt-minus-menu",style:M}),O=A.map(I=>n.createElement(sn,{key:I,name:I,data:r[I],keyPath:a,deep:c,isCollapsed:s,handleRemove:this.handleRemoveValue(I),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:D,beforeUpdateAction:T,logger:_,onSubmitValueParser:F}));return n.createElement("span",{className:"rejt-not-collapsed"},n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:ne},"{"),!x&&S,n.createElement("ul",{className:"rejt-not-collapsed-list",style:Q},O),!x&&l&&n.createElement("div",{className:"rejt-add-form",style:z},n.createElement(Ka,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,addButtonElement:h,cancelButtonElement:g,inputElementGenerator:y,keyPath:a,deep:o,onSubmitValueParser:F})),n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:ne},"}"),!x&&B)}render(){let{name:t,collapsed:r,data:a,keyPath:o,deep:c}=this.state,{getStyle:l,dataType:s}=this.props,u=r?this.renderCollapsed():this.renderNotCollapsed(),d=l(t,a,o,c,s);return n.createElement("div",{className:"rejt-object-node"},n.createElement("span",{onClick:this.handleCollapseMode},n.createElement("span",{className:"rejt-name",style:d.name},t," :"," ")),u)}};i(Fu,"JsonObject");var ga=Fu;ga.defaultProps={keyPath:[],deep:0,minusMenuElement:n.createElement("span",null," - "),plusMenuElement:n.createElement("span",null," + ")};var Lu=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={value:t.value,name:t.name,keyPath:r,deep:t.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(t,r){return t.value!==r.value?{value:t.value}:null}componentDidUpdate(){let{editEnabled:t,inputRef:r,name:a,value:o,keyPath:c,deep:l}=this.state,{readOnly:s,dataType:u}=this.props,d=s(a,o,c,l,u);t&&!d&&typeof r.focus=="function"&&r.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.handleEdit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue:t,originalValue:r,logger:a,onSubmitValueParser:o,keyPath:c}=this.props,{inputRef:l,name:s,deep:u}=this.state;if(!l)return;let d=o(!0,c,u,s,l.value),m={value:d,key:s};(t||Promise.resolve.bind(Promise))(m).then(()=>{Wa(r,d)||this.handleCancelEdit()}).catch(a.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(t){this.state.inputRef=t}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:t,value:r,editEnabled:a,keyPath:o,deep:c}=this.state,{handleRemove:l,originalValue:s,readOnly:u,dataType:d,getStyle:m,editButtonElement:f,cancelButtonElement:p,inputElementGenerator:h,minusMenuElement:g,keyPath:E}=this.props,y=m(t,s,o,c,d),v=u(t,s,o,c,d),C=a&&!v,w=h(Ga,E,c,t,s,d),k=f&&ae(f,{onClick:this.handleEdit}),D=p&&ae(p,{onClick:this.handleCancelEdit}),T=ae(w,{ref:this.refInput,defaultValue:JSON.stringify(s)}),_=g&&ae(g,{onClick:l,className:"rejt-minus-menu",style:y.minus});return n.createElement("li",{className:"rejt-value-node",style:y.li},n.createElement("span",{className:"rejt-name",style:y.name},t," : "),C?n.createElement("span",{className:"rejt-edit-form",style:y.editForm},T," ",D,k):n.createElement("span",{className:"rejt-value",style:y.value,onClick:v?void 0:this.handleEditMode},String(r)),!v&&!C&&_)}};i(Lu,"JsonValue");var nt=Lu;nt.defaultProps={keyPath:[],deep:0,handleUpdateValue:i(()=>Promise.resolve(),"handleUpdateValue"),editButtonElement:n.createElement("button",null,"e"),cancelButtonElement:n.createElement("button",null,"c"),minusMenuElement:n.createElement("span",null," - ")};function Pu(e){let t=e;if(t.indexOf("function")===0)return(0,eval)(`(${t})`);try{t=JSON.parse(e)}catch{}return t}i(Pu,"parse");var Kf={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},Yf={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},Jf={minus:{color:"red"},editForm:{},value:{color:"#7bba3d"},li:{minHeight:"22px",lineHeight:"22px",outline:"0px"},name:{color:"#2287CD"}},ju=class extends Re{constructor(t){super(t),this.state={data:t.data,rootName:t.rootName},this.onUpdate=this.onUpdate.bind(this),this.removeRoot=this.removeRoot.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data||t.rootName!==r.rootName?{data:t.data,rootName:t.rootName}:null}onUpdate(t,r){this.setState({data:r}),this.props.onFullyUpdate?.(r)}removeRoot(){this.onUpdate(null,null)}render(){let{data:t,rootName:r}=this.state,{isCollapsed:a,onDeltaUpdate:o,readOnly:c,getStyle:l,addButtonElement:s,cancelButtonElement:u,editButtonElement:d,inputElement:m,textareaElement:f,minusMenuElement:p,plusMenuElement:h,beforeRemoveAction:g,beforeAddAction:E,beforeUpdateAction:y,logger:v,onSubmitValueParser:C,fallback:w=null}=this.props,k=it(t),D=c;it(c)==="Boolean"&&(D=i(()=>c,"readOnlyFunction"));let T=m;m&&it(m)!=="Function"&&(T=i(()=>m,"inputElementFunction"));let _=f;return f&&it(f)!=="Function"&&(_=i(()=>f,"textareaElementFunction")),k==="Object"||k==="Array"?n.createElement("div",{className:"rejt-tree"},n.createElement(sn,{data:t,name:r||"root",deep:-1,isCollapsed:a??(()=>!1),onUpdate:this.onUpdate,onDeltaUpdate:o??(()=>{}),readOnly:D,getStyle:l??(()=>({})),addButtonElement:s,cancelButtonElement:u,editButtonElement:d,inputElementGenerator:T,textareaElementGenerator:_,minusMenuElement:p,plusMenuElement:h,handleRemove:this.removeRoot,beforeRemoveAction:g,beforeAddAction:E,beforeUpdateAction:y,logger:v??{},onSubmitValueParser:C??(F=>F)})):w}};i(ju,"JsonTree");var Mu=ju;Mu.defaultProps={rootName:"root",isCollapsed:i((e,t)=>t!==-1,"isCollapsed"),getStyle:i((e,t,r,a,o)=>{switch(o){case"Object":case"Error":return Kf;case"Array":return Yf;default:return Jf}},"getStyle"),readOnly:i(()=>!1,"readOnly"),onFullyUpdate:i(()=>{},"onFullyUpdate"),onDeltaUpdate:i(()=>{},"onDeltaUpdate"),beforeRemoveAction:i(()=>Promise.resolve(),"beforeRemoveAction"),beforeAddAction:i(()=>Promise.resolve(),"beforeAddAction"),beforeUpdateAction:i(()=>Promise.resolve(),"beforeUpdateAction"),logger:{error:i(()=>{},"error")},onSubmitValueParser:i((e,t,r,a,o)=>Pu(o),"onSubmitValueParser"),inputElement:i(()=>n.createElement("input",null),"inputElement"),textareaElement:i(()=>n.createElement("textarea",null),"textareaElement"),fallback:null};var{window:Xf}=globalThis,Zf=b.div(({theme:e})=>({position:"relative",display:"flex",'&[aria-readonly="true"]':{opacity:.5},".rejt-tree":{marginLeft:"1rem",fontSize:"13px"},".rejt-value-node, .rejt-object-node > .rejt-collapsed, .rejt-array-node > .rejt-collapsed, .rejt-object-node > .rejt-not-collapsed, .rejt-array-node > .rejt-not-collapsed":{"& > svg":{opacity:0,transition:"opacity 0.2s"}},".rejt-value-node:hover, .rejt-object-node:hover > .rejt-collapsed, .rejt-array-node:hover > .rejt-collapsed, .rejt-object-node:hover > .rejt-not-collapsed, .rejt-array-node:hover > .rejt-not-collapsed":{"& > svg":{opacity:1}},".rejt-edit-form button":{display:"none"},".rejt-add-form":{marginLeft:10},".rejt-add-value-node":{display:"inline-flex",alignItems:"center"},".rejt-name":{lineHeight:"22px"},".rejt-not-collapsed-delimiter":{lineHeight:"22px"},".rejt-plus-menu":{marginLeft:5},".rejt-object-node > span > *, .rejt-array-node > span > *":{position:"relative",zIndex:2},".rejt-object-node, .rejt-array-node":{position:"relative"},".rejt-object-node > span:first-of-type::after, .rejt-array-node > span:first-of-type::after, .rejt-collapsed::before, .rejt-not-collapsed::before":{content:'""',position:"absolute",top:0,display:"block",width:"100%",marginLeft:"-1rem",padding:"0 4px 0 1rem",height:22},".rejt-collapsed::before, .rejt-not-collapsed::before":{zIndex:1,background:"transparent",borderRadius:4,transition:"background 0.2s",pointerEvents:"none",opacity:.1},".rejt-object-node:hover, .rejt-array-node:hover":{"& > .rejt-collapsed::before, & > .rejt-not-collapsed::before":{background:e.color.secondary}},".rejt-collapsed::after, .rejt-not-collapsed::after":{content:'""',position:"absolute",display:"inline-block",pointerEvents:"none",width:0,height:0},".rejt-collapsed::after":{left:-8,top:8,borderTop:"3px solid transparent",borderBottom:"3px solid transparent",borderLeft:"3px solid rgba(153,153,153,0.6)"},".rejt-not-collapsed::after":{left:-10,top:10,borderTop:"3px solid rgba(153,153,153,0.6)",borderLeft:"3px solid transparent",borderRight:"3px solid transparent"},".rejt-value":{display:"inline-block",border:"1px solid transparent",borderRadius:4,margin:"1px 0",padding:"0 4px",cursor:"text",color:e.color.defaultText},".rejt-value-node:hover > .rejt-value":{background:e.color.lighter,borderColor:e.appBorderColor}})),zn=b.button(({theme:e,primary:t})=>({border:0,height:20,margin:1,borderRadius:4,background:t?e.color.secondary:"transparent",color:t?e.color.lightest:e.color.dark,fontWeight:t?"bold":"normal",cursor:"pointer",order:t?"initial":9})),Qf=b(fr)(({theme:e,disabled:t})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:t?"not-allowed":"pointer",color:e.textMutedColor,"&:hover":t?{}:{color:e.color.ancillary},"svg + &":{marginLeft:0}})),eh=b(Lo)(({theme:e,disabled:t})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:t?"not-allowed":"pointer",color:e.textMutedColor,"&:hover":t?{}:{color:e.color.negative},"svg + &":{marginLeft:0}})),oi=b.input(({theme:e,placeholder:t})=>({outline:0,margin:t?1:"1px 0",padding:"3px 4px",color:e.color.defaultText,background:e.background.app,border:`1px solid ${e.appBorderColor}`,borderRadius:4,lineHeight:"14px",width:t==="Key"?80:120,"&:focus":{border:`1px solid ${e.color.secondary}`}})),th=b(G)(({theme:e})=>({position:"absolute",zIndex:2,top:2,right:2,height:21,padding:"0 3px",background:e.background.bar,border:`1px solid ${e.appBorderColor}`,borderRadius:3,color:e.textMutedColor,fontSize:"9px",fontWeight:"bold",textDecoration:"none",span:{marginLeft:3,marginTop:1}})),rh=b(de.Textarea)(({theme:e})=>({flex:1,padding:"7px 6px",fontFamily:e.typography.fonts.mono,fontSize:"12px",lineHeight:"18px","&::placeholder":{fontFamily:e.typography.fonts.base,fontSize:"13px"},"&:placeholder-shown":{padding:"7px 10px"}})),nh={bubbles:!0,cancelable:!0,key:"Enter",code:"Enter",keyCode:13},ah=i(e=>{e.currentTarget.dispatchEvent(new Xf.KeyboardEvent("keydown",nh))},"dispatchEnterKey"),oh=i(e=>{e.currentTarget.select()},"selectValue"),lh=i(e=>()=>({name:{color:e.color.secondary},collapsed:{color:e.color.dark},ul:{listStyle:"none",margin:"0 0 0 1rem",padding:0},li:{outline:0}}),"getCustomStyleFunction"),li=i(({name:e,value:t,onChange:r,argType:a})=>{let o=De(),c=ce(()=>t&&os(t),[t]),l=c!=null,[s,u]=R(!l),[d,m]=R(null),f=!!a?.table?.readonly,p=$(w=>{try{w&&r(JSON.parse(w)),m(null)}catch(k){m(k)}},[r]),[h,g]=R(!1),E=$(()=>{r({}),g(!0)},[g]),y=X(null);if(j(()=>{h&&y.current&&y.current.select()},[h]),!l)return n.createElement(ge,{disabled:f,id:or(e),onClick:E},"Set object");let v=n.createElement(rh,{ref:y,id:Oe(e),name:e,defaultValue:t===null?"":JSON.stringify(t,null,2),onBlur:w=>p(w.target.value),placeholder:"Edit JSON string...",autoFocus:h,valid:d?"error":void 0,readOnly:f}),C=Array.isArray(t)||typeof t=="object"&&t?.constructor===Object;return n.createElement(Zf,{"aria-readonly":f},C&&n.createElement(th,{onClick:w=>{w.preventDefault(),u(k=>!k)}},s?n.createElement(Eo,null):n.createElement(vo,null),n.createElement("span",null,"RAW")),s?v:n.createElement(Mu,{readOnly:f||!C,isCollapsed:C?void 0:()=>!0,data:c,rootName:e,onFullyUpdate:r,getStyle:lh(o),cancelButtonElement:n.createElement(zn,{type:"button"},"Cancel"),editButtonElement:n.createElement(zn,{type:"submit"},"Save"),addButtonElement:n.createElement(zn,{type:"submit",primary:!0},"Save"),plusMenuElement:n.createElement(Qf,null),minusMenuElement:n.createElement(eh,null),inputElement:(w,k,D,T)=>T?n.createElement(oi,{onFocus:oh,onBlur:ah}):n.createElement(oi,null),fallback:v}))},"ObjectControl");je();var ih=b.input(({theme:e,min:t,max:r,value:a,disabled:o})=>({"&":{width:"100%",backgroundColor:"transparent",appearance:"none"},"&::-webkit-slider-runnable-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:o?"not-allowed":"pointer"},"&::-webkit-slider-thumb":{marginTop:"-6px",width:16,height:16,border:`1px solid ${Le(e.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${Le(e.appBorderColor,.2)}`,cursor:o?"not-allowed":"grab",appearance:"none",background:`${e.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${Ge(.05,e.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${e.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:o?"not-allowed":"grab"}},"&:focus":{outline:"none","&::-webkit-slider-runnable-track":{borderColor:Le(e.color.secondary,.4)},"&::-webkit-slider-thumb":{borderColor:e.color.secondary,boxShadow:`0 0px 5px 0px ${e.color.secondary}`}},"&::-moz-range-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:o?"not-allowed":"pointer",outline:"none"},"&::-moz-range-thumb":{width:16,height:16,border:`1px solid ${Le(e.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${Le(e.appBorderColor,.2)}`,cursor:o?"not-allowed":"grap",background:`${e.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${Ge(.05,e.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${e.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:"grabbing"}},"&::-ms-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,color:"transparent",width:"100%",height:"6px",cursor:"pointer"},"&::-ms-fill-lower":{borderRadius:6},"&::-ms-fill-upper":{borderRadius:6},"&::-ms-thumb":{width:16,height:16,background:`${e.input.background}`,border:`1px solid ${Le(e.appBorderColor,.2)}`,borderRadius:50,cursor:"grab",marginTop:0},"@supports (-ms-ime-align:auto)":{"input[type=range]":{margin:"0"}}})),Uu=b.span({paddingLeft:5,paddingRight:5,fontSize:12,whiteSpace:"nowrap",fontFeatureSettings:"tnum",fontVariantNumeric:"tabular-nums","[aria-readonly=true] &":{opacity:.5}}),sh=b(Uu)(({numberOFDecimalsPlaces:e,max:t})=>({width:`${e+t.toString().length*2+3}ch`,textAlign:"right",flexShrink:0})),uh=b.div({display:"flex",alignItems:"center",width:"100%"});function $u(e){let t=e.toString().match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0}i($u,"getNumberOfDecimalPlaces");var ch=i(({name:e,value:t,onChange:r,min:a=0,max:o=100,step:c=1,onBlur:l,onFocus:s,argType:u})=>{let d=i(h=>{r(Af(h.target.value))},"handleChange"),m=t!==void 0,f=ce(()=>$u(c),[c]),p=!!u?.table?.readonly;return n.createElement(uh,{"aria-readonly":p},n.createElement(Uu,null,a),n.createElement(ih,{id:Oe(e),type:"range",disabled:p,onChange:d,name:e,min:a,max:o,step:c,onFocus:s,onBlur:l,value:t??a}),n.createElement(sh,{numberOFDecimalsPlaces:f,max:o},m?t.toFixed(f):"--"," / ",o))},"RangeControl");je();var dh=b.label({display:"flex"}),ph=b.div(({isMaxed:e})=>({marginLeft:"0.75rem",paddingTop:"0.35rem",color:e?"red":void 0})),mh=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,maxLength:c,argType:l})=>{let s=i(h=>{r(h.target.value)},"handleChange"),u=!!l?.table?.readonly,[d,m]=R(!1),f=$(()=>{r(""),m(!0)},[m]);if(t===void 0)return n.createElement(ge,{variant:"outline",size:"medium",disabled:u,id:or(e),onClick:f},"Set string");let p=typeof t=="string";return n.createElement(dh,null,n.createElement(de.Textarea,{id:Oe(e),maxLength:c,onChange:s,disabled:u,size:"flex",placeholder:"Edit string...",autoFocus:d,valid:p?void 0:"error",name:e,value:p?t:"",onFocus:a,onBlur:o}),c&&n.createElement(ph,{isMaxed:t?.length===c},t?.length??0," / ",c))},"TextControl");je();var fh=b(de.Input)({padding:10});function Hu(e){e.forEach(t=>{t.startsWith("blob:")&&URL.revokeObjectURL(t)})}i(Hu,"revokeOldUrls");var hh=i(({onChange:e,name:t,accept:r="image/*",value:a,argType:o})=>{let c=X(null),l=o?.control?.readOnly;function s(u){if(!u.target.files)return;let d=Array.from(u.target.files).map(m=>URL.createObjectURL(m));e(d),Hu(a||[])}return i(s,"handleFileChange"),j(()=>{a==null&&c.current&&(c.current.value="")},[a,t]),n.createElement(fh,{ref:c,id:Oe(t),type:"file",name:t,multiple:!0,disabled:l,onChange:s,accept:r,size:"flex"})},"FilesControl"),gh=mo(()=>Promise.resolve().then(()=>(mp(),Cs))),bh=i(e=>n.createElement(co,{fallback:n.createElement("div",null)},n.createElement(gh,{...e})),"ColorControl"),yh={array:li,object:li,boolean:ff,color:bh,date:vf,number:Sf,check:Ot,"inline-check":Ot,radio:Ot,"inline-radio":Ot,select:Ot,"multi-select":Ot,range:ch,text:mh,file:hh},ii=i(()=>n.createElement(n.Fragment,null,"-"),"NoControl"),Eh=i(({row:e,arg:t,updateArgs:r,isHovered:a})=>{let{key:o,control:c}=e,[l,s]=R(!1),[u,d]=R({value:t});j(()=>{l||d({value:t})},[l,t]);let m=$(E=>(d({value:E}),r({[o]:E}),E),[r,o]),f=$(()=>s(!1),[]),p=$(()=>s(!0),[]);if(!c||c.disable){let E=c?.disable!==!0&&e?.type?.name!=="function";return a&&E?n.createElement(be,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},"Setup controls"):n.createElement(ii,null)}let h={name:o,argType:e,value:u.value,onChange:m,onBlur:f,onFocus:p},g=yh[c.type]||ii;return n.createElement(g,{...h,...c,controlType:c.type})},"ArgControl"),vh=b.table(({theme:e})=>({"&&":{borderCollapse:"collapse",borderSpacing:0,border:"none",tr:{border:"none !important",background:"none"},"td, th":{padding:0,border:"none",width:"auto!important"},marginTop:0,marginBottom:0,"th:first-of-type, td:first-of-type":{paddingLeft:0},"th:last-of-type, td:last-of-type":{paddingRight:0},td:{paddingTop:0,paddingBottom:4,"&:not(:first-of-type)":{paddingLeft:10,paddingRight:0}},tbody:{boxShadow:"none",border:"none"},code:Xe({theme:e}),div:{span:{fontWeight:"bold"}},"& code":{margin:0,display:"inline-block",fontSize:e.typography.size.s1}}})),xh=i(({tags:e})=>{let t=(e.params||[]).filter(c=>c.description),r=t.length!==0,a=e.deprecated!=null,o=e.returns!=null&&e.returns.description!=null;return!r&&!o&&!a?null:n.createElement(n.Fragment,null,n.createElement(vh,null,n.createElement("tbody",null,a&&n.createElement("tr",{key:"deprecated"},n.createElement("td",{colSpan:2},n.createElement("strong",null,"Deprecated"),": ",e.deprecated?.toString())),r&&t.map(c=>n.createElement("tr",{key:c.name},n.createElement("td",null,n.createElement("code",null,c.name)),n.createElement("td",null,c.description))),o&&n.createElement("tr",{key:"returns"},n.createElement("td",null,n.createElement("code",null,"Returns")),n.createElement("td",null,e.returns?.description)))))},"ArgJsDoc");an();var Ah=Ce(fp()),ba=8,si=b.div(({isExpanded:e})=>({display:"flex",flexDirection:e?"column":"row",flexWrap:"wrap",alignItems:"flex-start",marginBottom:"-4px",minWidth:100})),Ch=b.span(Xe,({theme:e,simple:t=!1})=>({flex:"0 0 auto",fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,wordBreak:"break-word",whiteSpace:"normal",maxWidth:"100%",margin:0,marginRight:"4px",marginBottom:"4px",paddingTop:"2px",paddingBottom:"2px",lineHeight:"13px",...t&&{background:"transparent",border:"0 none",paddingLeft:0}})),Sh=b.button(({theme:e})=>({fontFamily:e.typography.fonts.mono,color:e.color.secondary,marginBottom:"4px",background:"none",border:"none"})),wh=b.div(Xe,({theme:e})=>({fontFamily:e.typography.fonts.mono,color:e.color.secondary,fontSize:e.typography.size.s1,margin:0,whiteSpace:"nowrap",display:"flex",alignItems:"center"})),kh=b.div(({theme:e,width:t})=>({width:t,minWidth:200,maxWidth:800,padding:15,fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,boxSizing:"content-box","& code":{padding:"0 !important"}})),Oh=b(yo)({marginLeft:4}),Th=b(gr)({marginLeft:4}),Dh=i(()=>n.createElement("span",null,"-"),"EmptyArg"),Vu=i(({text:e,simple:t})=>n.createElement(Ch,{simple:t},e),"ArgText"),Ih=(0,Ah.default)(1e3)(e=>{let t=e.split(/\r?\n/);return`${Math.max(...t.map(r=>r.length))}ch`}),_h=i(e=>{if(!e)return[e];let t=e.split("|").map(r=>r.trim());return is(t)},"getSummaryItems"),ui=i((e,t=!0)=>{let r=e;return t||(r=e.slice(0,ba)),r.map(a=>n.createElement(Vu,{key:a,text:a===""?'""':a}))},"renderSummaryItems"),Bh=i(({value:e,initialExpandedArgs:t})=>{let{summary:r,detail:a}=e,[o,c]=R(!1),[l,s]=R(t||!1);if(r==null)return null;let u=typeof r.toString=="function"?r.toString():r;if(a==null){if(/[(){}[\]<>]/.test(u))return n.createElement(Vu,{text:u});let d=_h(u),m=d.length;return m>ba?n.createElement(si,{isExpanded:l},ui(d,l),n.createElement(Sh,{onClick:()=>s(!l)},l?"Show less...":`Show ${m-ba} more...`)):n.createElement(si,null,ui(d))}return n.createElement(yn,{closeOnOutsideClick:!0,placement:"bottom",visible:o,onVisibleChange:d=>{c(d)},tooltip:n.createElement(kh,{width:Ih(a)},n.createElement(Vt,{language:"jsx",format:!1},a))},n.createElement(wh,{className:"sbdocs-expandable"},n.createElement("span",null,u),o?n.createElement(Oh,null):n.createElement(Th,null)))},"ArgSummary"),qn=i(({value:e,initialExpandedArgs:t})=>e==null?n.createElement(Dh,null):n.createElement(Bh,{value:e,initialExpandedArgs:t}),"ArgValue"),Rh=b.span({fontWeight:"bold"}),Nh=b.span(({theme:e})=>({color:e.color.negative,fontFamily:e.typography.fonts.mono,cursor:"help"})),Fh=b.div(({theme:e})=>({"&&":{p:{margin:"0 0 10px 0"},a:{color:e.color.secondary}},code:{...Xe({theme:e}),fontSize:12,fontFamily:e.typography.fonts.mono},"& code":{margin:0,display:"inline-block"},"& pre > code":{whiteSpace:"pre-wrap"}})),Lh=b.div(({theme:e,hasDescription:t})=>({color:e.base==="light"?W(.1,e.color.defaultText):W(.2,e.color.defaultText),marginTop:t?4:0})),Ph=b.div(({theme:e,hasDescription:t})=>({color:e.base==="light"?W(.1,e.color.defaultText):W(.2,e.color.defaultText),marginTop:t?12:0,marginBottom:12})),jh=b.td(({expandable:e})=>({paddingLeft:e?"40px !important":"20px !important"})),Mh=i(e=>e&&{summary:typeof e=="string"?e:e.name},"toSummary"),Nr=i(e=>{let[t,r]=R(!1),{row:a,updateArgs:o,compact:c,expandable:l,initialExpandedArgs:s}=e,{name:u,description:d}=a,m=a.table||{},f=m.type||Mh(a.type),p=m.defaultValue||a.defaultValue,h=a.type?.required,g=d!=null&&d!=="";return n.createElement("tr",{onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},n.createElement(jh,{expandable:l??!1},n.createElement(Rh,null,u),h?n.createElement(Nh,{title:"Required"},"*"):null),c?null:n.createElement("td",null,g&&n.createElement(Fh,null,n.createElement(df,null,d)),m.jsDocTags!=null?n.createElement(n.Fragment,null,n.createElement(Ph,{hasDescription:g},n.createElement(qn,{value:f,initialExpandedArgs:s})),n.createElement(xh,{tags:m.jsDocTags})):n.createElement(Lh,{hasDescription:g},n.createElement(qn,{value:f,initialExpandedArgs:s}))),c?null:n.createElement("td",null,n.createElement(qn,{value:p,initialExpandedArgs:s})),o?n.createElement("td",null,n.createElement(Eh,{...e,isHovered:t})):null)},"ArgRow"),Uh=b.div(({inAddonPanel:e,theme:t})=>({height:e?"100%":"auto",display:"flex",border:e?"none":`1px solid ${t.appBorderColor}`,borderRadius:e?0:t.appBorderRadius,padding:e?0:40,alignItems:"center",justifyContent:"center",flexDirection:"column",gap:15,background:t.background.content})),$h=b.div(({theme:e})=>({display:"flex",fontSize:e.typography.size.s2-1,gap:25})),Hh=i(({inAddonPanel:e})=>{let[t,r]=R(!0);return j(()=>{let a=setTimeout(()=>{r(!1)},100);return()=>clearTimeout(a)},[]),t?null:n.createElement(Uh,{inAddonPanel:e},n.createElement(Ht,{title:e?"Interactive story playground":"Args table with interactive controls couldn't be auto-generated",description:n.createElement(n.Fragment,null,"Controls give you an easy to use interface to test your components. Set your story args and you'll see controls appearing here automatically."),footer:n.createElement($h,null,e&&n.createElement(n.Fragment,null,n.createElement(be,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},n.createElement(dt,null)," Read docs")),!e&&n.createElement(be,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},n.createElement(dt,null)," Learn how to set that up"))}))},"Empty"),Vh=b(go)(({theme:e})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:e.base==="light"?W(.25,e.color.defaultText):W(.3,e.color.defaultText),border:"none",display:"inline-block"})),zh=b(bo)(({theme:e})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:e.base==="light"?W(.25,e.color.defaultText):W(.3,e.color.defaultText),border:"none",display:"inline-block"})),qh=b.span(({theme:e})=>({display:"flex",lineHeight:"20px",alignItems:"center"})),Gh=b.td(({theme:e})=>({position:"relative",letterSpacing:"0.35em",textTransform:"uppercase",fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s1-1,color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText),background:`${e.background.app} !important`,"& ~ td":{background:`${e.background.app} !important`}})),Wh=b.td(({theme:e})=>({position:"relative",fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,background:e.background.app})),Kh=b.td({position:"relative"}),Yh=b.tr(({theme:e})=>({"&:hover > td":{backgroundColor:`${ht(.005,e.background.app)} !important`,boxShadow:`${e.color.mediumlight} 0 - 1px 0 0 inset`,cursor:"row-resize"}})),ci=b.button({background:"none",border:"none",padding:"0",font:"inherit",position:"absolute",top:0,bottom:0,left:0,right:0,height:"100%",width:"100%",color:"transparent",cursor:"row-resize !important"}),Gn=i(({level:e="section",label:t,children:r,initialExpanded:a=!0,colSpan:o=3})=>{let[c,l]=R(a),s=e==="subsection"?Wh:Gh,u=r?.length||0,d=e==="subsection"?`${u} item${u!==1?"s":""}`:"",m=`${c?"Hide":"Show"} ${e==="subsection"?u:t} item${u!==1?"s":""}`;return n.createElement(n.Fragment,null,n.createElement(Yh,{title:m},n.createElement(s,{colSpan:1},n.createElement(ci,{onClick:f=>l(!c),tabIndex:0},m),n.createElement(qh,null,c?n.createElement(Vh,null):n.createElement(zh,null),t)),n.createElement(Kh,{colSpan:o-1},n.createElement(ci,{onClick:f=>l(!c),tabIndex:-1,style:{outline:"none"}},m),c?null:d)),c?r:null)},"SectionRow"),Jh=b.div(({theme:e})=>({width:"100%",borderSpacing:0,color:e.color.defaultText})),Fr=b.div(({theme:e})=>({display:"flex",borderBottom:`1px solid ${e.appBorderColor}`,"&:last-child":{borderBottom:0}})),me=b.div(({position:e,theme:t})=>{let r={display:"flex",flexDirection:"column",gap:5,padding:"10px 15px",alignItems:"flex-start"};switch(e){case"first":return{...r,width:"25%",paddingLeft:20};case"second":return{...r,width:"35%"};case"third":return{...r,width:"15%"};case"last":return{...r,width:"25%",paddingRight:20}}}),se=b.div(({theme:e,width:t,height:r})=>({animation:`${e.animation.glow} 1.5s ease-in-out infinite`,background:e.appBorderColor,width:t||"100%",height:r||16,borderRadius:3})),Xh=i(()=>n.createElement(Jh,null,n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(se,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(se,{width:"60%"}))),n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(se,{width:"80%"}),n.createElement(se,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(se,{width:"60%"}))),n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(se,{width:"80%"}),n.createElement(se,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(se,{width:"60%"}))),n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(se,{width:"80%"}),n.createElement(se,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(se,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(se,{width:"60%"})))),"Skeleton"),Zh=b.table(({theme:e,compact:t,inAddonPanel:r})=>({"&&":{borderSpacing:0,color:e.color.defaultText,"td, th":{padding:0,border:"none",verticalAlign:"top",textOverflow:"ellipsis"},fontSize:e.typography.size.s2-1,lineHeight:"20px",textAlign:"left",width:"100%",marginTop:r?0:25,marginBottom:r?0:40,"thead th:first-of-type, td:first-of-type":{width:"25%"},"th:first-of-type, td:first-of-type":{paddingLeft:20},"th:nth-of-type(2), td:nth-of-type(2)":{...t?null:{width:"35%"}},"td:nth-of-type(3)":{...t?null:{width:"15%"}},"th:last-of-type, td:last-of-type":{paddingRight:20,...t?null:{width:"25%"}},th:{color:e.base==="light"?W(.25,e.color.defaultText):W(.45,e.color.defaultText),paddingTop:10,paddingBottom:10,paddingLeft:15,paddingRight:15},td:{paddingTop:"10px",paddingBottom:"10px","&:not(:first-of-type)":{paddingLeft:15,paddingRight:15},"&:last-of-type":{paddingRight:20}},marginLeft:r?0:1,marginRight:r?0:1,tbody:{...r?null:{filter:e.base==="light"?"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.10))":"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.20))"},"> tr > *":{background:e.background.content,borderTop:`1px solid ${e.appBorderColor}`},...r?null:{"> tr:first-of-type > *":{borderBlockStart:`1px solid ${e.appBorderColor}`},"> tr:last-of-type > *":{borderBlockEnd:`1px solid ${e.appBorderColor}`},"> tr > *:first-of-type":{borderInlineStart:`1px solid ${e.appBorderColor}`},"> tr > *:last-of-type":{borderInlineEnd:`1px solid ${e.appBorderColor}`},"> tr:first-of-type > td:first-of-type":{borderTopLeftRadius:e.appBorderRadius},"> tr:first-of-type > td:last-of-type":{borderTopRightRadius:e.appBorderRadius},"> tr:last-of-type > td:first-of-type":{borderBottomLeftRadius:e.appBorderRadius},"> tr:last-of-type > td:last-of-type":{borderBottomRightRadius:e.appBorderRadius}}}}})),Qh=b(G)(({theme:e})=>({margin:"-4px -12px -4px 0"})),e2=b.span({display:"flex",justifyContent:"space-between"}),t2={alpha:i((e,t)=>(e.name??"").localeCompare(t.name??""),"alpha"),requiredFirst:i((e,t)=>+!!t.type?.required-+!!e.type?.required||(e.name??"").localeCompare(t.name??""),"requiredFirst"),none:null},r2=i((e,t)=>{let r={ungrouped:[],ungroupedSubsections:{},sections:{}};if(!e)return r;Object.entries(e).forEach(([c,l])=>{let{category:s,subcategory:u}=l?.table||{};if(s){let d=r.sections[s]||{ungrouped:[],subsections:{}};if(!u)d.ungrouped.push({key:c,...l});else{let m=d.subsections[u]||[];m.push({key:c,...l}),d.subsections[u]=m}r.sections[s]=d}else if(u){let d=r.ungroupedSubsections[u]||[];d.push({key:c,...l}),r.ungroupedSubsections[u]=d}else r.ungrouped.push({key:c,...l})});let a=t2[t],o=i(c=>a?Object.keys(c).reduce((l,s)=>({...l,[s]:c[s].sort(a)}),{}):c,"sortSubsection");return{ungrouped:a?r.ungrouped.sort(a):r.ungrouped,ungroupedSubsections:o(r.ungroupedSubsections),sections:Object.keys(r.sections).reduce((c,l)=>({...c,[l]:{ungrouped:a?r.sections[l].ungrouped.sort(a):r.sections[l].ungrouped,subsections:o(r.sections[l].subsections)}}),{})}},"groupRows"),n2=i((e,t,r)=>{try{return sl(e,t,r)}catch(a){return nl.warn(a.message),!1}},"safeIncludeConditionalArg"),a2=i(e=>{let{updateArgs:t,resetArgs:r,compact:a,inAddonPanel:o,initialExpandedArgs:c,sort:l="none",isLoading:s}=e;if("error"in e){let{error:C}=e;return n.createElement(Qs,null,C,"\xA0",n.createElement(be,{href:"http://storybook.js.org/docs/",target:"_blank",withArrow:!0},n.createElement(dt,null)," Read the docs"))}if(s)return n.createElement(Xh,null);let{rows:u,args:d,globals:m}="rows"in e?e:{rows:void 0,args:void 0,globals:void 0},f=r2(ys(u||{},C=>!C?.table?.disable&&n2(C,d||{},m||{})),l),p=f.ungrouped.length===0,h=Object.entries(f.sections).length===0,g=Object.entries(f.ungroupedSubsections).length===0;if(p&&h&&g)return n.createElement(Hh,{inAddonPanel:o});let E=1;t&&(E+=1),a||(E+=2);let y=Object.keys(f.sections).length>0,v={updateArgs:t,compact:a,inAddonPanel:o,initialExpandedArgs:c};return n.createElement(hn,null,n.createElement(Zh,{compact:a,inAddonPanel:o,className:"docblock-argstable sb-unstyled"},n.createElement("thead",{className:"docblock-argstable-head"},n.createElement("tr",null,n.createElement("th",null,n.createElement("span",null,"Name")),a?null:n.createElement("th",null,n.createElement("span",null,"Description")),a?null:n.createElement("th",null,n.createElement("span",null,"Default")),t?n.createElement("th",null,n.createElement(e2,null,"Control"," ",!s&&r&&n.createElement(Qh,{onClick:()=>r(),title:"Reset controls"},n.createElement(Er,{"aria-hidden":!0})))):null)),n.createElement("tbody",{className:"docblock-argstable-body"},f.ungrouped.map(C=>n.createElement(Nr,{key:C.key,row:C,arg:d&&d[C.key],...v})),Object.entries(f.ungroupedSubsections).map(([C,w])=>n.createElement(Gn,{key:C,label:C,level:"subsection",colSpan:E},w.map(k=>n.createElement(Nr,{key:k.key,row:k,arg:d&&d[k.key],expandable:y,...v})))),Object.entries(f.sections).map(([C,w])=>n.createElement(Gn,{key:C,label:C,level:"section",colSpan:E},w.ungrouped.map(k=>n.createElement(Nr,{key:k.key,row:k,arg:d&&d[k.key],...v})),Object.entries(w.subsections).map(([k,D])=>n.createElement(Gn,{key:k,label:k,level:"subsection",colSpan:E},D.map(T=>n.createElement(Nr,{key:T.key,row:T,arg:d&&d[T.key],expandable:y,...v})))))))))},"ArgsTable"),ya="addon-controls",zu="controls",o2=xn({from:{transform:"translateY(40px)"},to:{transform:"translateY(0)"}}),l2=xn({from:{background:"var(--highlight-bg-color)"},to:{}}),i2=b.div({containerType:"size",position:"sticky",bottom:0,height:39,overflow:"hidden",zIndex:1}),s2=b($t)(({theme:e})=>({"--highlight-bg-color":e.base==="dark"?"#153B5B":"#E0F0FF",display:"flex",flexDirection:"row-reverse",alignItems:"center",justifyContent:"space-between",flexWrap:"wrap",gap:6,padding:"6px 10px",animation:`${o2} 300ms, ${l2} 2s`,background:e.background.bar,borderTop:`1px solid ${e.appBorderColor}`,fontSize:e.typography.size.s2,"@container (max-width: 799px)":{flexDirection:"row",justifyContent:"flex-end"}})),u2=b.div({display:"flex",flex:"99 0 auto",alignItems:"center",marginLeft:10,gap:6}),c2=b.div(({theme:e})=>({display:"flex",flex:"1 0 0",alignItems:"center",gap:2,color:e.color.mediumdark,fontSize:e.typography.size.s2})),Wn=b.div({"@container (max-width: 799px)":{lineHeight:0,textIndent:"-9999px","&::after":{content:"attr(data-short-label)",display:"block",lineHeight:"initial",textIndent:"0"}}}),d2=b(de.Input)(({theme:e})=>({"::placeholder":{color:e.color.mediumdark},"&:invalid:not(:placeholder-shown)":{boxShadow:`${e.color.negative} 0 0 0 1px inset`}})),p2=i(({saveStory:e,createStory:t,resetArgs:r,portalSelector:a})=>{let o=n.useRef(null),[c,l]=n.useState(!1),[s,u]=n.useState(!1),[d,m]=n.useState(""),[f,p]=n.useState(null),h=i(async()=>{c||(l(!0),await e().catch(()=>{}),l(!1))},"onSaveStory"),g=i(()=>{u(!0),m(""),setTimeout(()=>o.current?.focus(),0)},"onShowForm"),E=i(y=>{let v=y.target.value.replace(/^[^a-z]/i,"").replace(/[^a-z0-9-_ ]/gi,"").replaceAll(/([-_ ]+[a-z0-9])/gi,C=>C.toUpperCase().replace(/[-_ ]/g,""));m(v.charAt(0).toUpperCase()+v.slice(1))},"onChange");return n.createElement(i2,{id:"save-from-controls"},n.createElement(s2,null,n.createElement(c2,null,n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Save changes to story"})},n.createElement(G,{"aria-label":"Save changes to story",disabled:c,onClick:h},n.createElement(hr,null),n.createElement(Wn,{"data-short-label":"Save"},"Update story"))),n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Create new story with these settings"})},n.createElement(G,{"aria-label":"Create new story with these settings",onClick:g},n.createElement(fr,null),n.createElement(Wn,{"data-short-label":"New"},"Create new story"))),n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Reset changes"})},n.createElement(G,{"aria-label":"Reset changes",onClick:()=>r()},n.createElement(Er,null),n.createElement("span",null,"Reset")))),n.createElement(u2,null,n.createElement(Wn,{"data-short-label":"Unsaved changes"},"You modified this story. Do you want to save your changes?")),n.createElement(He,{width:350,open:s,onOpenChange:u,portalSelector:a},n.createElement(de,{onSubmit:i(async y=>{if(y.preventDefault(),!c)try{p(null),l(!0),await t(d.replace(/^[^a-z]/i,"").replaceAll(/[^a-z0-9]/gi,"")),u(!1),l(!1)}catch(v){p(v.message),l(!1)}},"onSubmitForm"),id:"create-new-story-form"},n.createElement(He.Content,null,n.createElement(He.Header,null,n.createElement(He.Title,null,"Create new story"),n.createElement(He.Description,null,"This will add a new story to your existing stories file.")),n.createElement(d2,{onChange:E,placeholder:"Story export name",readOnly:c,ref:o,value:d}),n.createElement(He.Actions,null,n.createElement(ge,{disabled:c||!d,size:"medium",type:"submit",variant:"solid"},"Create"),n.createElement(He.Dialog.Close,{asChild:!0},n.createElement(ge,{disabled:c,size:"medium",type:"reset"},"Cancel"))))),f&&n.createElement(He.Error,null,f))))},"SaveStory"),di=i(e=>Object.entries(e).reduce((t,[r,a])=>a!==void 0?Object.assign(t,{[r]:a}):t,{}),"clean"),m2=b.div({display:"grid",gridTemplateRows:"1fr 39px",height:"100%",maxHeight:"100vh",overflowY:"auto"}),f2=i(({saveStory:e,createStory:t})=>{let[r,a]=R(!0),[o,c,l,s]=Jo(),[u]=Ve(),d=xr(),{expanded:m,sort:f,presetColors:p,disableSaveFromUI:h=!1}=et(zu,{}),{path:g,previewInitialized:E}=Xo();j(()=>{E&&a(!1)},[E]);let y=Object.values(d).some(w=>w?.control),v=Object.entries(d).reduce((w,[k,D])=>{let T=D?.control;return typeof T!="object"||T?.type!=="color"||T?.presetColors?w[k]=D:w[k]={...D,control:{...T,presetColors:p}},w},{}),C=ce(()=>!!o&&!!s&&!st(di(o),di(s)),[o,s]);return n.createElement(m2,null,n.createElement(a2,{key:g,compact:!m&&y,rows:v,args:o,globals:u,updateArgs:c,resetArgs:l,inAddonPanel:!0,sort:f,isLoading:r}),y&&C&&Qe.CONFIG_TYPE==="DEVELOPMENT"&&h!==!0&&n.createElement(p2,{resetArgs:l,saveStory:e,createStory:t}))},"ControlsPanel");function qu(){let e=Ee().getSelectedPanel(),t=xr(),r=Object.values(t).filter(a=>a?.control&&!a?.table?.disable).length;return n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Controls"),r===0?null:n.createElement(ct,{compact:!0,status:e===ya?"active":"neutral"},r))}i(qu,"Title");var pi=i(e=>JSON.stringify(e,(t,r)=>typeof r=="function"?"__sb_empty_function_arg__":r),"stringifyArgs"),Fw=Z.register(ya,e=>{if(globalThis?.FEATURES?.controls){let t=Z.getChannel(),r=i(async()=>{let o=e.getCurrentStoryData();if(o.type!=="story")throw new Error("Not a story");try{let c=await Cn(t,Sn,Cr,{args:pi(Object.entries(o.args||{}).reduce((l,[s,u])=>(st(u,o.initialArgs?.[s])||(l[s]=u),l),{})),csfId:o.id,importPath:o.importPath});e.addNotification({id:"save-story-success",icon:n.createElement(vn,{color:vr.positive}),content:{headline:"Story saved",subHeadline:n.createElement(n.Fragment,null,"Updated story ",n.createElement("b",null,c.sourceStoryName),".")},duration:8e3})}catch(c){throw e.addNotification({id:"save-story-error",icon:n.createElement(xo,{color:vr.negative}),content:{headline:"Failed to save story",subHeadline:c?.message||"Check the Storybook process on the command line for more details."},duration:8e3}),c}},"saveStory"),a=i(async o=>{let c=e.getCurrentStoryData();if(c.type!=="story")throw new Error("Not a story");let l=await Cn(t,Sn,Cr,{args:c.args&&pi(c.args),csfId:c.id,importPath:c.importPath,name:o});e.addNotification({id:"save-story-success",icon:n.createElement(vn,{color:vr.positive}),content:{headline:"Story created",subHeadline:n.createElement(n.Fragment,null,"Added story ",n.createElement("b",null,l.newStoryName)," based on ",n.createElement("b",null,l.sourceStoryName),".")},duration:8e3,onClick:i(({onDismiss:s})=>{s(),e.selectStory(l.newStoryId)},"onClick")})},"createStory");Z.add(ya,{title:qu,type:ye.PANEL,paramKey:zu,render:i(({active:o})=>!o||!e.getCurrentStoryData()?null:n.createElement(Ut,{active:o},n.createElement(f2,{saveStory:r,createStory:a})),"render")}),t.on(Cr,o=>{if(!o.success)return;let c=e.getCurrentStoryData();c.type==="story"&&(e.resetStoryArgs(c),o.payload.newStoryId&&e.selectStory(o.payload.newStoryId))})}}),h2="actions",lr="storybook/actions",Gu=`${lr}/panel`,Ea=`${lr}/action-event`,Wu=`${lr}/action-clear`;function Ku(){let e=Ee().getSelectedPanel(),[{count:t},r]=St(lr,{count:0});return Ar({[Ea]:()=>{r(a=>({...a,count:a.count+1}))},[qt]:()=>{r(a=>({...a,count:0}))},[Wu]:()=>{r(a=>({...a,count:0}))}}),n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Actions"),t===0?null:n.createElement(ct,{compact:!0,status:e===Gu?"active":"neutral"},t))}i(Ku,"Title");var g2=Object.create,Ya=Object.defineProperty,b2=Object.getOwnPropertyDescriptor,Yu=Object.getOwnPropertyNames,y2=Object.getPrototypeOf,E2=Object.prototype.hasOwnProperty,Ja=i((e,t)=>i(function(){return t||(0,e[Yu(e)[0]])((t={exports:{}}).exports,t),t.exports},"__require"),"__commonJS"),v2=i((e,t)=>{for(var r in t)Ya(e,r,{get:t[r],enumerable:!0})},"__export"),x2=i((e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Yu(t))!E2.call(e,o)&&o!==r&&Ya(e,o,{get:i(()=>t[o],"get"),enumerable:!(a=b2(t,o))||a.enumerable});return e},"__copyProps"),A2=i((e,t,r)=>(r=e!=null?g2(y2(e)):{},x2(t||!e||!e.__esModule?Ya(r,"default",{value:e,enumerable:!0}):r,e)),"__toESM"),C2=Ja({"node_modules/is-object/index.js"(e,t){"use strict";t.exports=i(function(r){return typeof r=="object"&&r!==null},"isObject")}}),S2=Ja({"node_modules/is-window/index.js"(e,t){"use strict";t.exports=function(r){if(r==null)return!1;var a=Object(r);return a===a.window}}}),w2=Ja({"node_modules/is-dom/index.js"(e,t){var r=C2(),a=S2();function o(c){return!r(c)||!a(window)||typeof window.Node!="function"?!1:typeof c.nodeType=="number"&&typeof c.nodeName=="string"}i(o,"isNode"),t.exports=o}}),Xr={};v2(Xr,{chromeDark:i(()=>k2,"chromeDark"),chromeLight:i(()=>O2,"chromeLight")});var k2={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"rgb(36, 36, 36)",BASE_COLOR:"rgb(213, 213, 213)",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(227, 110, 236)",OBJECT_VALUE_NULL_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_REGEXP_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_STRING_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_NUMBER_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_BOOLEAN_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(85, 106, 242)",HTML_TAG_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(155, 187, 220)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(242, 151, 102)",HTML_COMMENT_COLOR:"rgb(137, 137, 137)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"rgb(145, 145, 145)",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"rgb(85, 85, 85)",TABLE_TH_BACKGROUND_COLOR:"rgb(44, 44, 44)",TABLE_TH_HOVER_COLOR:"rgb(48, 48, 48)",TABLE_SORT_ICON_COLOR:"black",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 50%, rgba(51, 139, 255, 0.0980392) 50%, rgba(51, 139, 255, 0.0980392))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},O2={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"white",BASE_COLOR:"black",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(136, 19, 145)",OBJECT_VALUE_NULL_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_REGEXP_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_STRING_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_NUMBER_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_BOOLEAN_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(13, 34, 170)",HTML_TAG_COLOR:"rgb(168, 148, 166)",HTML_TAGNAME_COLOR:"rgb(136, 18, 128)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(153, 69, 0)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(26, 26, 166)",HTML_COMMENT_COLOR:"rgb(35, 110, 37)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"#6e6e6e",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"#aaa",TABLE_TH_BACKGROUND_COLOR:"#eee",TABLE_TH_HOVER_COLOR:"hsla(0, 0%, 90%, 1)",TABLE_SORT_ICON_COLOR:"#6e6e6e",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(to bottom, white, white 50%, rgb(234, 243, 255) 50%, rgb(234, 243, 255))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},Ju=Ct([{},()=>{}]),Kn={WebkitTouchCallout:"none",WebkitUserSelect:"none",KhtmlUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",OUserSelect:"none",userSelect:"none"},qr=i(e=>({DOMNodePreview:{htmlOpenTag:{base:{color:e.HTML_TAG_COLOR},tagName:{color:e.HTML_TAGNAME_COLOR,textTransform:e.HTML_TAGNAME_TEXT_TRANSFORM},htmlAttributeName:{color:e.HTML_ATTRIBUTE_NAME_COLOR},htmlAttributeValue:{color:e.HTML_ATTRIBUTE_VALUE_COLOR}},htmlCloseTag:{base:{color:e.HTML_TAG_COLOR},offsetLeft:{marginLeft:-e.TREENODE_PADDING_LEFT},tagName:{color:e.HTML_TAGNAME_COLOR,textTransform:e.HTML_TAGNAME_TEXT_TRANSFORM}},htmlComment:{color:e.HTML_COMMENT_COLOR},htmlDoctype:{color:e.HTML_DOCTYPE_COLOR}},ObjectPreview:{objectDescription:{fontStyle:"italic"},preview:{fontStyle:"italic"},arrayMaxProperties:e.OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES,objectMaxProperties:e.OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES},ObjectName:{base:{color:e.OBJECT_NAME_COLOR},dimmed:{opacity:.6}},ObjectValue:{objectValueNull:{color:e.OBJECT_VALUE_NULL_COLOR},objectValueUndefined:{color:e.OBJECT_VALUE_UNDEFINED_COLOR},objectValueRegExp:{color:e.OBJECT_VALUE_REGEXP_COLOR},objectValueString:{color:e.OBJECT_VALUE_STRING_COLOR},objectValueSymbol:{color:e.OBJECT_VALUE_SYMBOL_COLOR},objectValueNumber:{color:e.OBJECT_VALUE_NUMBER_COLOR},objectValueBoolean:{color:e.OBJECT_VALUE_BOOLEAN_COLOR},objectValueFunctionPrefix:{color:e.OBJECT_VALUE_FUNCTION_PREFIX_COLOR,fontStyle:"italic"},objectValueFunctionName:{fontStyle:"italic"}},TreeView:{treeViewOutline:{padding:0,margin:0,listStyleType:"none"}},TreeNode:{treeNodeBase:{color:e.BASE_COLOR,backgroundColor:e.BASE_BACKGROUND_COLOR,lineHeight:e.TREENODE_LINE_HEIGHT,cursor:"default",boxSizing:"border-box",listStyle:"none",fontFamily:e.TREENODE_FONT_FAMILY,fontSize:e.TREENODE_FONT_SIZE},treeNodePreviewContainer:{},treeNodePlaceholder:{whiteSpace:"pre",fontSize:e.ARROW_FONT_SIZE,marginRight:e.ARROW_MARGIN_RIGHT,...Kn},treeNodeArrow:{base:{color:e.ARROW_COLOR,display:"inline-block",fontSize:e.ARROW_FONT_SIZE,marginRight:e.ARROW_MARGIN_RIGHT,...parseFloat(e.ARROW_ANIMATION_DURATION)>0?{transition:`transform ${e.ARROW_ANIMATION_DURATION} ease 0s`}:{},...Kn},expanded:{WebkitTransform:"rotateZ(90deg)",MozTransform:"rotateZ(90deg)",transform:"rotateZ(90deg)"},collapsed:{WebkitTransform:"rotateZ(0deg)",MozTransform:"rotateZ(0deg)",transform:"rotateZ(0deg)"}},treeNodeChildNodesContainer:{margin:0,paddingLeft:e.TREENODE_PADDING_LEFT}},TableInspector:{base:{color:e.BASE_COLOR,position:"relative",border:`1px solid ${e.TABLE_BORDER_COLOR}`,fontFamily:e.BASE_FONT_FAMILY,fontSize:e.BASE_FONT_SIZE,lineHeight:"120%",boxSizing:"border-box",cursor:"default"}},TableInspectorHeaderContainer:{base:{top:0,height:"17px",left:0,right:0,overflowX:"hidden"},table:{tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",height:"100%",width:"100%",margin:0}},TableInspectorDataContainer:{tr:{display:"table-row"},td:{boxSizing:"border-box",border:"none",height:"16px",verticalAlign:"top",padding:"1px 4px",WebkitUserSelect:"text",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px"},div:{position:"static",top:"17px",bottom:0,overflowY:"overlay",transform:"translateZ(0)",left:0,right:0,overflowX:"hidden"},table:{positon:"static",left:0,top:0,right:0,bottom:0,borderTop:"0 none transparent",margin:0,backgroundImage:e.TABLE_DATA_BACKGROUND_IMAGE,backgroundSize:e.TABLE_DATA_BACKGROUND_SIZE,tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",width:"100%",fontSize:e.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorTH:{base:{position:"relative",height:"auto",textAlign:"left",backgroundColor:e.TABLE_TH_BACKGROUND_COLOR,borderBottom:`1px solid ${e.TABLE_BORDER_COLOR}`,fontWeight:"normal",verticalAlign:"middle",padding:"0 4px",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px",":hover":{backgroundColor:e.TABLE_TH_HOVER_COLOR}},div:{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",fontSize:e.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorLeftBorder:{none:{borderLeft:"none"},solid:{borderLeft:`1px solid ${e.TABLE_BORDER_COLOR}`}},TableInspectorSortIcon:{display:"block",marginRight:3,width:8,height:7,marginTop:-7,color:e.TABLE_SORT_ICON_COLOR,fontSize:12,...Kn}}),"createTheme"),va="chromeLight",Xu=Ct(qr(Xr[va])),ke=i(e=>dr(Xu)[e],"useStyles"),Xa=i(e=>i(({theme:t=va,...r})=>{let a=ce(()=>{switch(Object.prototype.toString.call(t)){case"[object String]":return qr(Xr[t]);case"[object Object]":return qr(t);default:return qr(Xr[va])}},[t]);return n.createElement(Xu.Provider,{value:a},n.createElement(e,{...r}))},"ThemeAcceptor"),"themeAcceptor"),T2=i(({expanded:e,styles:t})=>n.createElement("span",{style:{...t.base,...e?t.expanded:t.collapsed}},"\u25B6"),"Arrow"),D2=he(e=>{e={expanded:!0,nodeRenderer:i(({name:m})=>n.createElement("span",null,m),"nodeRenderer"),onClick:i(()=>{},"onClick"),shouldShowArrow:!1,shouldShowPlaceholder:!0,...e};let{expanded:t,onClick:r,children:a,nodeRenderer:o,title:c,shouldShowArrow:l,shouldShowPlaceholder:s}=e,u=ke("TreeNode"),d=o;return n.createElement("li",{"aria-expanded":t,role:"treeitem",style:u.treeNodeBase,title:c},n.createElement("div",{style:u.treeNodePreviewContainer,onClick:r},l||cr.count(a)>0?n.createElement(T2,{expanded:t,styles:u.treeNodeArrow}):s&&n.createElement("span",{style:u.treeNodePlaceholder},"\xA0"),n.createElement(d,{...e})),n.createElement("ol",{role:"group",style:u.treeNodeChildNodesContainer},t?a:void 0))}),Zr="$",mi="*";function Qt(e,t){return!t(e).next().done}i(Qt,"hasChildNodes");var I2=i(e=>Array.from({length:e},(t,r)=>[Zr].concat(Array.from({length:r},()=>"*")).join(".")),"wildcardPathsFromLevel"),_2=i((e,t,r,a,o)=>{let c=[].concat(I2(a)).concat(r).filter(s=>typeof s=="string"),l=[];return c.forEach(s=>{let u=s.split("."),d=i((m,f,p)=>{if(p===u.length){l.push(f);return}let h=u[p];if(p===0)Qt(m,t)&&(h===Zr||h===mi)&&d(m,Zr,p+1);else if(h===mi)for(let{name:g,data:E}of t(m))Qt(E,t)&&d(E,`${f}.${g}`,p+1);else{let g=m[h];Qt(g,t)&&d(g,`${f}.${h}`,p+1)}},"populatePaths");d(e,"",0)}),l.reduce((s,u)=>(s[u]=!0,s),{...o})},"getExpandedPaths"),Zu=he(e=>{let{data:t,dataIterator:r,path:a,depth:o,nodeRenderer:c}=e,[l,s]=dr(Ju),u=Qt(t,r),d=!!l[a],m=$(()=>u&&s(f=>({...f,[a]:!d})),[u,s,a,d]);return n.createElement(D2,{expanded:d,onClick:m,shouldShowArrow:u,shouldShowPlaceholder:o>0,nodeRenderer:c,...e},d?[...r(t)].map(({name:f,data:p,...h})=>n.createElement(Zu,{name:f,data:p,depth:o+1,path:`${a}.${f}`,key:f,dataIterator:r,nodeRenderer:c,...h})):null)}),Qu=he(({name:e,data:t,dataIterator:r,nodeRenderer:a,expandPaths:o,expandLevel:c})=>{let l=ke("TreeView"),s=R({}),[,u]=s;return pr(()=>u(d=>_2(t,r,o,c,d)),[t,r,o,c]),n.createElement(Ju.Provider,{value:s},n.createElement("ol",{role:"tree",style:l.treeViewOutline},n.createElement(Zu,{name:e,data:t,dataIterator:r,depth:0,path:Zr,nodeRenderer:a})))}),Za=i(({name:e,dimmed:t=!1,styles:r={}})=>{let a=ke("ObjectName"),o={...a.base,...t?a.dimmed:{},...r};return n.createElement("span",{style:o},e)},"ObjectName"),er=i(({object:e,styles:t})=>{let r=ke("ObjectValue"),a=i(o=>({...r[o],...t}),"mkStyle");switch(typeof e){case"bigint":return n.createElement("span",{style:a("objectValueNumber")},String(e),"n");case"number":return n.createElement("span",{style:a("objectValueNumber")},String(e));case"string":return n.createElement("span",{style:a("objectValueString")},'"',e,'"');case"boolean":return n.createElement("span",{style:a("objectValueBoolean")},String(e));case"undefined":return n.createElement("span",{style:a("objectValueUndefined")},"undefined");case"object":return e===null?n.createElement("span",{style:a("objectValueNull")},"null"):e instanceof Date?n.createElement("span",null,e.toString()):e instanceof RegExp?n.createElement("span",{style:a("objectValueRegExp")},e.toString()):Array.isArray(e)?n.createElement("span",null,`Array(${e.length})`):e.constructor?typeof e.constructor.isBuffer=="function"&&e.constructor.isBuffer(e)?n.createElement("span",null,`Buffer[${e.length}]`):n.createElement("span",null,e.constructor.name):n.createElement("span",null,"Object");case"function":return n.createElement("span",null,n.createElement("span",{style:a("objectValueFunctionPrefix")},"\u0192\xA0"),n.createElement("span",{style:a("objectValueFunctionName")},e.name,"()"));case"symbol":return n.createElement("span",{style:a("objectValueSymbol")},e.toString());default:return n.createElement("span",null)}},"ObjectValue"),ec=Object.prototype.hasOwnProperty,B2=Object.prototype.propertyIsEnumerable;function Qr(e,t){let r=Object.getOwnPropertyDescriptor(e,t);if(r.get)try{return r.get()}catch{return r.get}return e[t]}i(Qr,"getPropertyValue");function xa(e,t){return e.length===0?[]:e.slice(1).reduce((r,a)=>r.concat([t,a]),[e[0]])}i(xa,"intersperse");var Aa=i(({data:e})=>{let t=ke("ObjectPreview"),r=e;if(typeof r!="object"||r===null||r instanceof Date||r instanceof RegExp)return n.createElement(er,{object:r});if(Array.isArray(r)){let a=t.arrayMaxProperties,o=r.slice(0,a).map((l,s)=>n.createElement(er,{key:s,object:l}));r.length>a&&o.push(n.createElement("span",{key:"ellipsis"},"\u2026"));let c=r.length;return n.createElement(n.Fragment,null,n.createElement("span",{style:t.objectDescription},c===0?"":`(${c})\xA0`),n.createElement("span",{style:t.preview},"[",xa(o,", "),"]"))}else{let a=t.objectMaxProperties,o=[];for(let l in r)if(ec.call(r,l)){let s;o.length===a-1&&Object.keys(r).length>a&&(s=n.createElement("span",{key:"ellipsis"},"\u2026"));let u=Qr(r,l);if(o.push(n.createElement("span",{key:l},n.createElement(Za,{name:l||'""'}),":\xA0",n.createElement(er,{object:u}),s)),s)break}let c=r.constructor?r.constructor.name:"Object";return n.createElement(n.Fragment,null,n.createElement("span",{style:t.objectDescription},c==="Object"?"":`${c} `),n.createElement("span",{style:t.preview},"{",xa(o,", "),"}"))}},"ObjectPreview"),R2=i(({name:e,data:t})=>typeof e=="string"?n.createElement("span",null,n.createElement(Za,{name:e}),n.createElement("span",null,": "),n.createElement(Aa,{data:t})):n.createElement(Aa,{data:t}),"ObjectRootLabel"),N2=i(({name:e,data:t,isNonenumerable:r=!1})=>{let a=t;return n.createElement("span",null,typeof e=="string"?n.createElement(Za,{name:e,dimmed:r}):n.createElement(Aa,{data:e}),n.createElement("span",null,": "),n.createElement(er,{object:a}))},"ObjectLabel"),F2=i((e,t)=>i(function*(r){if(!(typeof r=="object"&&r!==null||typeof r=="function"))return;let a=Array.isArray(r);if(!a&&r[Symbol.iterator]){let o=0;for(let c of r){if(Array.isArray(c)&&c.length===2){let[l,s]=c;yield{name:l,data:s}}else yield{name:o.toString(),data:c};o++}}else{let o=Object.getOwnPropertyNames(r);t===!0&&!a?o.sort():typeof t=="function"&&o.sort(t);for(let c of o)if(B2.call(r,c)){let l=Qr(r,c);yield{name:c||'""',data:l}}else if(e){let l;try{l=Qr(r,c)}catch{}l!==void 0&&(yield{name:c,data:l,isNonenumerable:!0})}e&&r!==Object.prototype&&(yield{name:"__proto__",data:Object.getPrototypeOf(r),isNonenumerable:!0})}},"objectIterator"),"createIterator"),L2=i(({depth:e,name:t,data:r,isNonenumerable:a})=>e===0?n.createElement(R2,{name:t,data:r}):n.createElement(N2,{name:t,data:r,isNonenumerable:a}),"defaultNodeRenderer"),P2=i(({showNonenumerable:e=!1,sortObjectKeys:t,nodeRenderer:r,...a})=>{let o=F2(e,t),c=r||L2;return n.createElement(Qu,{nodeRenderer:c,dataIterator:o,...a})},"ObjectInspector"),j2=Xa(P2);function tc(e){if(typeof e=="object"){let t=[];if(Array.isArray(e)){let a=e.length;t=[...Array(a).keys()]}else e!==null&&(t=Object.keys(e));let r=t.reduce((a,o)=>{let c=e[o];return typeof c=="object"&&c!==null&&Object.keys(c).reduce((l,s)=>(l.includes(s)||l.push(s),l),a),a},[]);return{rowHeaders:t,colHeaders:r}}}i(tc,"getHeaders");var M2=i(({rows:e,columns:t,rowsData:r})=>{let a=ke("TableInspectorDataContainer"),o=ke("TableInspectorLeftBorder");return n.createElement("div",{style:a.div},n.createElement("table",{style:a.table},n.createElement("colgroup",null),n.createElement("tbody",null,e.map((c,l)=>n.createElement("tr",{key:c,style:a.tr},n.createElement("td",{style:{...a.td,...o.none}},c),t.map(s=>{let u=r[l];return typeof u=="object"&&u!==null&&ec.call(u,s)?n.createElement("td",{key:s,style:{...a.td,...o.solid}},n.createElement(er,{object:u[s]})):n.createElement("td",{key:s,style:{...a.td,...o.solid}})}))))))},"DataContainer"),U2=i(e=>n.createElement("div",{style:{position:"absolute",top:1,right:0,bottom:1,display:"flex",alignItems:"center"}},e.children),"SortIconContainer"),$2=i(({sortAscending:e})=>{let t=ke("TableInspectorSortIcon"),r=e?"\u25B2":"\u25BC";return n.createElement("div",{style:t},r)},"SortIcon"),fi=i(({sortAscending:e=!1,sorted:t=!1,onClick:r=void 0,borderStyle:a={},children:o,...c})=>{let l=ke("TableInspectorTH"),[s,u]=R(!1),d=$(()=>u(!0),[]),m=$(()=>u(!1),[]);return n.createElement("th",{...c,style:{...l.base,...a,...s?l.base[":hover"]:{}},onMouseEnter:d,onMouseLeave:m,onClick:r},n.createElement("div",{style:l.div},o),t&&n.createElement(U2,null,n.createElement($2,{sortAscending:e})))},"TH"),H2=i(({indexColumnText:e="(index)",columns:t=[],sorted:r,sortIndexColumn:a,sortColumn:o,sortAscending:c,onTHClick:l,onIndexTHClick:s})=>{let u=ke("TableInspectorHeaderContainer"),d=ke("TableInspectorLeftBorder");return n.createElement("div",{style:u.base},n.createElement("table",{style:u.table},n.createElement("tbody",null,n.createElement("tr",null,n.createElement(fi,{borderStyle:d.none,sorted:r&&a,sortAscending:c,onClick:s},e),t.map(m=>n.createElement(fi,{borderStyle:d.solid,key:m,sorted:r&&o===m,sortAscending:c,onClick:l.bind(null,m)},m))))))},"HeaderContainer"),V2=i(({data:e,columns:t})=>{let r=ke("TableInspector"),[{sorted:a,sortIndexColumn:o,sortColumn:c,sortAscending:l},s]=R({sorted:!1,sortIndexColumn:!1,sortColumn:void 0,sortAscending:!1}),u=$(()=>{s(({sortIndexColumn:g,sortAscending:E})=>({sorted:!0,sortIndexColumn:!0,sortColumn:void 0,sortAscending:g?!E:!0}))},[]),d=$(g=>{s(({sortColumn:E,sortAscending:y})=>({sorted:!0,sortIndexColumn:!1,sortColumn:g,sortAscending:g===E?!y:!0}))},[]);if(typeof e!="object"||e===null)return n.createElement("div",null);let{rowHeaders:m,colHeaders:f}=tc(e);t!==void 0&&(f=t);let p=m.map(g=>e[g]),h;if(c!==void 0?h=p.map((g,E)=>typeof g=="object"&&g!==null?[g[c],E]:[void 0,E]):o&&(h=m.map((g,E)=>[m[E],E])),h!==void 0){let g=i((y,v)=>(C,w)=>{let k=y(C),D=y(w),T=typeof k,_=typeof D,F=i((H,z)=>H<z?-1:H>z?1:0,"lt"),M;if(T===_)M=F(k,D);else{let H={string:0,number:1,object:2,symbol:3,boolean:4,undefined:5,function:6};M=F(H[T],H[_])}return v||(M=-M),M},"comparator"),E=h.sort(g(y=>y[0],l)).map(y=>y[1]);m=E.map(y=>m[y]),p=E.map(y=>p[y])}return n.createElement("div",{style:r.base},n.createElement(H2,{columns:f,sorted:a,sortIndexColumn:o,sortColumn:c,sortAscending:l,onTHClick:d,onIndexTHClick:u}),n.createElement(M2,{rows:m,columns:f,rowsData:p}))},"TableInspector"),z2=Xa(V2),q2=80,rc=i(e=>e.childNodes.length===0||e.childNodes.length===1&&e.childNodes[0].nodeType===Node.TEXT_NODE&&e.textContent.length<q2,"shouldInline"),G2=i(({tagName:e,attributes:t,styles:r})=>n.createElement("span",{style:r.base},"<",n.createElement("span",{style:r.tagName},e),(()=>{if(t){let a=[];for(let o=0;o<t.length;o++){let c=t[o];a.push(n.createElement("span",{key:o}," ",n.createElement("span",{style:r.htmlAttributeName},c.name),'="',n.createElement("span",{style:r.htmlAttributeValue},c.value),'"'))}return a}})(),">"),"OpenTag"),hi=i(({tagName:e,isChildNode:t=!1,styles:r})=>n.createElement("span",{style:Object.assign({},r.base,t&&r.offsetLeft)},"</",n.createElement("span",{style:r.tagName},e),">"),"CloseTag"),W2={1:"ELEMENT_NODE",3:"TEXT_NODE",7:"PROCESSING_INSTRUCTION_NODE",8:"COMMENT_NODE",9:"DOCUMENT_NODE",10:"DOCUMENT_TYPE_NODE",11:"DOCUMENT_FRAGMENT_NODE"},K2=i(({isCloseTag:e,data:t,expanded:r})=>{let a=ke("DOMNodePreview");if(e)return n.createElement(hi,{styles:a.htmlCloseTag,isChildNode:!0,tagName:t.tagName});switch(t.nodeType){case Node.ELEMENT_NODE:return n.createElement("span",null,n.createElement(G2,{tagName:t.tagName,attributes:t.attributes,styles:a.htmlOpenTag}),rc(t)?t.textContent:!r&&"\u2026",!r&&n.createElement(hi,{tagName:t.tagName,styles:a.htmlCloseTag}));case Node.TEXT_NODE:return n.createElement("span",null,t.textContent);case Node.CDATA_SECTION_NODE:return n.createElement("span",null,"<![CDATA["+t.textContent+"]]>");case Node.COMMENT_NODE:return n.createElement("span",{style:a.htmlComment},"<!--",t.textContent,"-->");case Node.PROCESSING_INSTRUCTION_NODE:return n.createElement("span",null,t.nodeName);case Node.DOCUMENT_TYPE_NODE:return n.createElement("span",{style:a.htmlDoctype},"<!DOCTYPE ",t.name,t.publicId?` PUBLIC "${t.publicId}"`:"",!t.publicId&&t.systemId?" SYSTEM":"",t.systemId?` "${t.systemId}"`:"",">");case Node.DOCUMENT_NODE:return n.createElement("span",null,t.nodeName);case Node.DOCUMENT_FRAGMENT_NODE:return n.createElement("span",null,t.nodeName);default:return n.createElement("span",null,W2[t.nodeType])}},"DOMNodePreview"),Y2=i(function*(e){if(e&&e.childNodes){if(rc(e))return;for(let t=0;t<e.childNodes.length;t++){let r=e.childNodes[t];r.nodeType===Node.TEXT_NODE&&r.textContent.trim().length===0||(yield{name:`${r.tagName}[${t}]`,data:r})}e.tagName&&(yield{name:"CLOSE_TAG",data:{tagName:e.tagName},isCloseTag:!0})}},"domIterator"),J2=i(e=>n.createElement(Qu,{nodeRenderer:K2,dataIterator:Y2,...e}),"DOMInspector"),X2=Xa(J2),Z2=A2(w2()),Q2=i(({table:e=!1,data:t,...r})=>e?n.createElement(z2,{data:t,...r}):(0,Z2.default)(t)?n.createElement(X2,{data:t,...r}):n.createElement(j2,{data:t,...r}),"Inspector"),e0=b.div({display:"flex",padding:0,borderLeft:"5px solid transparent",borderBottom:"1px solid transparent",transition:"all 0.1s",alignItems:"flex-start",whiteSpace:"pre"}),t0=b.div(({theme:e})=>({backgroundColor:Xt(.5,e.appBorderColor),color:e.color.inverseText,fontSize:e.typography.size.s1,fontWeight:e.typography.weight.bold,lineHeight:1,padding:"1px 5px",borderRadius:20,margin:"2px 0px"})),r0=b.div({flex:1,padding:"0 0 0 5px"}),nc=po(({children:e,className:t},r)=>n.createElement(gn,{ref:r,horizontal:!0,vertical:!0,className:t},e));nc.displayName="UnstyledWrapped";var n0=b(nc)({margin:0,padding:"10px 5px 20px"}),a0=Wo(({theme:e,...t})=>n.createElement(Q2,{theme:e.addonActionsTheme||"chromeLight",table:!1,...t})),o0=i(({actions:e,onClear:t})=>{let r=X(null),a=r.current,o=a&&a.scrollHeight-a.scrollTop===a.clientHeight;return j(()=>{o&&(r.current.scrollTop=r.current.scrollHeight)},[o,e.length]),n.createElement(Te,null,n.createElement(n0,{ref:r},e.map(c=>n.createElement(e0,{key:c.id},c.count>1&&n.createElement(t0,null,c.count),n.createElement(r0,null,n.createElement(a0,{sortObjectKeys:!0,showNonenumerable:!1,name:c.data.name,data:c.data.args??c.data}))))),n.createElement(Mt,{actionItems:[{title:"Clear",onClick:t}]}))},"ActionLogger"),l0=i((e,t)=>{try{return st(e,t)}catch{return!1}},"safeDeepEqual"),ac=class extends Re{constructor(t){super(t),this.handleStoryChange=i(()=>{let{actions:r}=this.state;r.length>0&&r[0].options.clearOnStoryChange&&this.clearActions()},"handleStoryChange"),this.addAction=i(r=>{this.setState(a=>{let o=[...a.actions],c=o.length&&o[o.length-1];return c&&l0(c.data,r.data)?c.count++:(r.count=1,o.push(r)),{actions:o.slice(0,r.options.limit)}})},"addAction"),this.clearActions=i(()=>{let{api:r}=this.props;r.emit(Wu),this.setState({actions:[]})},"clearActions"),this.mounted=!1,this.state={actions:[]}}componentDidMount(){this.mounted=!0;let{api:t}=this.props;t.on(Ea,this.addAction),t.on(qt,this.handleStoryChange)}componentWillUnmount(){this.mounted=!1;let{api:t}=this.props;t.off(qt,this.handleStoryChange),t.off(Ea,this.addAction)}render(){let{actions:t=[]}=this.state,{active:r}=this.props,a={actions:t,onClear:this.clearActions};return r?n.createElement(o0,{...a}):null}};i(ac,"ActionLogger");var i0=ac,pk=Z.register(lr,e=>{globalThis?.FEATURES?.actions&&Z.add(Gu,{title:Ku,type:ye.PANEL,render:i(({active:t})=>n.createElement(i0,{api:e,active:!!t}),"render"),paramKey:h2})}),un="storybook/interactions",Qa=`${un}/panel`,s0="writing-tests/integrations/vitest-addon",u0=`${s0}#what-happens-when-there-are-different-test-results-in-multiple-environments`,c0="writing-stories/play-function#writing-stories-with-the-play-function",Ie="internal_render_call",xt="storybook/a11y",vk=`${xt}/panel`,xk=`${xt}/result`,Ak=`${xt}/request`,Ck=`${xt}/running`,Sk=`${xt}/error`,wk=`${xt}/manual`,kk=`${xt}/select`,d0="writing-tests/accessibility-testing",Ok=`${d0}#why-are-my-tests-failing-in-different-environments`,oc="storybook/test",Tk=`${oc}/test-provider`,p0="STORYBOOK_ADDON_TEST_CHANNEL",m0="writing-tests/integrations/vitest-addon",Dk=`${m0}#what-happens-if-vitest-itself-has-an-error`,f0={id:oc,initialState:{config:{coverage:!1,a11y:!1},watching:!1,cancelling:!1,fatalError:void 0,indexUrl:void 0,previewAnnotations:[],currentRun:{triggeredBy:void 0,config:{coverage:!1,a11y:!1},componentTestCount:{success:0,error:0},a11yCount:{success:0,warning:0,error:0},storyIds:void 0,totalTestCount:void 0,startedAt:void 0,finishedAt:void 0,unhandledErrors:[],coverageSummary:void 0}}},Ik=`UNIVERSAL_STORE:${f0.id}`,h0="storybook/component-test",ft={CALL:"storybook/instrumenter/call",SYNC:"storybook/instrumenter/sync",START:"storybook/instrumenter/start",BACK:"storybook/instrumenter/back",GOTO:"storybook/instrumenter/goto",NEXT:"storybook/instrumenter/next",END:"storybook/instrumenter/end"},g0=Ce(Ep(),1);function lc({onlyFirst:e=!1}={}){let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}i(lc,"ansiRegex");var b0=lc();function ic(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(b0,"")}i(ic,"stripAnsi");function sc(e){return eo(e)||to(e)}i(sc,"isTestAssertionError");function eo(e){return e&&typeof e=="object"&&"name"in e&&typeof e.name=="string"&&e.name==="AssertionError"}i(eo,"isChaiError");function to(e){return e&&typeof e=="object"&&"message"in e&&typeof e.message=="string"&&ic(e.message).startsWith("expect(")}i(to,"isJestError");function uc(e){return new g0.default({escapeXML:!0,fg:e.color.defaultText,bg:e.background.content})}i(uc,"createAnsiToHtmlFilter");function cn(){let e=De();return uc(e)}i(cn,"useAnsiToHtmlFilter");var y0=b.div(({theme:{color:e,typography:t,background:r}})=>({textAlign:"start",padding:"11px 15px",fontSize:`${t.size.s2-1}px`,fontWeight:t.weight.regular,lineHeight:"1rem",background:r.app,borderBottom:`1px solid ${e.border}`,color:e.defaultText,backgroundClip:"padding-box",position:"relative"})),E0=i(({storyUrl:e})=>n.createElement(y0,null,"Debugger controls are not available on composed Storybooks."," ",n.createElement(be,{href:`${e}&addonPanel=${Qa}`,target:"_blank",rel:"noopener noreferrer",withArrow:!0},"Open in external Storybook")),"DetachedDebuggerMessage"),v0=b.div(({theme:e})=>({display:"flex",fontSize:e.typography.size.s2-1,gap:25})),x0=i(()=>{let[e,t]=R(!0),r=Ee().getDocsUrl({subpath:c0,versioned:!0,renderer:!0});return j(()=>{let a=setTimeout(()=>{t(!1)},100);return()=>clearTimeout(a)},[]),e?null:n.createElement("div",null,n.createElement(Ht,{title:"Interactions",description:n.createElement(n.Fragment,null,"Interactions allow you to verify the functional aspects of UIs. Write a play function for your story and you'll see it run here."),footer:n.createElement(v0,null,n.createElement(be,{href:r,target:"_blank",withArrow:!0},n.createElement(dt,null)," Read docs"))}))},"Empty"),A0=Ce(Fa()),C0=Ce(La());function en(e){var t,r,a="";if(e)if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=en(e[t]))&&(a&&(a+=" "),a+=r);else for(t in e)e[t]&&(r=en(t))&&(a&&(a+=" "),a+=r);else typeof e!="boolean"&&!e.call&&(a&&(a+=" "),a+=e);return a}i(en,"toVal");function Pe(){for(var e=0,t,r="";e<arguments.length;)(t=en(arguments[e++]))&&(r&&(r+=" "),r+=t);return r}i(Pe,"default");var ro=i(e=>Array.isArray(e)||ArrayBuffer.isView(e)&&!(e instanceof DataView),"isArray"),cc=i(e=>e!==null&&typeof e=="object"&&!ro(e)&&!(e instanceof Date)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof WeakMap)&&!(e instanceof WeakSet),"isObject"),S0=i(e=>cc(e)||ro(e)||typeof e=="function"||e instanceof Promise,"isKnownObject"),dc=i(e=>{let t=/unique/;return Promise.race([e,t]).then(r=>r===t?["pending"]:["fulfilled",r],r=>["rejected",r])},"getPromiseState"),qe=i(async(e,t,r,a,o,c)=>{let l={key:e,depth:r,value:t,type:"value",parent:void 0};if(t&&S0(t)&&r<100){let s=[],u="object";if(ro(t)){for(let d=0;d<t.length;d++)s.push(async()=>{let m=await qe(d.toString(),t[d],r+1,a);return m.parent=l,m});u="array"}else{let d=Object.getOwnPropertyNames(t);a&&d.sort();for(let m=0;m<d.length;m++){let f;try{f=t[d[m]]}catch{}s.push(async()=>{let p=await qe(d[m],f,r+1,a);return p.parent=l,p})}if(typeof t=="function"&&(u="function"),t instanceof Promise){let[m,f]=await dc(t);s.push(async()=>{let p=await qe("<state>",m,r+1,a);return p.parent=l,p}),m!=="pending"&&s.push(async()=>{let p=await qe("<value>",f,r+1,a);return p.parent=l,p}),u="promise"}if(t instanceof Map){let m=Array.from(t.entries()).map(f=>{let[p,h]=f;return{"<key>":p,"<value>":h}});s.push(async()=>{let f=await qe("<entries>",m,r+1,a);return f.parent=l,f}),s.push(async()=>{let f=await qe("size",t.size,r+1,a);return f.parent=l,f}),u="map"}if(t instanceof Set){let m=Array.from(t.entries()).map(f=>f[1]);s.push(async()=>{let f=await qe("<entries>",m,r+1,a);return f.parent=l,f}),s.push(async()=>{let f=await qe("size",t.size,r+1,a);return f.parent=l,f}),u="set"}}t!==Object.prototype&&c&&s.push(async()=>{let d=await qe("<prototype>",Object.getPrototypeOf(t),r+1,a,!0);return d.parent=l,d}),l.type=u,l.children=s,l.isPrototype=o}return l},"buildAST"),w0=i((e,t,r)=>qe("root",e,0,t===!1?t:!0,void 0,r===!1?r:!0),"parse"),gi=Ce(Ap()),k0=Ce(Sp()),O0=["children"],Ca=n.createContext({theme:"chrome",colorScheme:"light"}),T0=i(e=>{let{children:t}=e,r=(0,k0.default)(e,O0),a=n.useContext(Ca);return n.createElement(Ca.Provider,{value:(0,gi.default)((0,gi.default)({},a),r)},t)},"ThemeProvider"),dn=i((e,t={})=>{let r=n.useContext(Ca),a=e.theme||r.theme||"chrome",o=e.colorScheme||r.colorScheme||"light",c=Pe(t[a],t[o]);return{currentColorScheme:o,currentTheme:a,themeClass:c}},"useTheme"),bi=Ce(kp()),Yn=Ce(Op()),D0=Ce(Dp()),I0=n.createContext({isChild:!1,depth:0,hasHover:!0}),Jn=I0,ve={tree:"Tree-tree-fbbbe38",item:"Tree-item-353d6f3",group:"Tree-group-d3c3d8a",label:"Tree-label-d819155",focusWhite:"Tree-focusWhite-f1e00c2",arrow:"Tree-arrow-03ab2e7",hover:"Tree-hover-3cc4e5d",open:"Tree-open-3f1a336",dark:"Tree-dark-1b4aa00",chrome:"Tree-chrome-bcbcac6",light:"Tree-light-09174ee"},_0=["theme","hover","colorScheme","children","label","className","onUpdate","onSelect","open"],tn=i(e=>{let{theme:t,hover:r,colorScheme:a,children:o,label:c,className:l,onUpdate:s,onSelect:u,open:d}=e,m=(0,D0.default)(e,_0),{themeClass:f,currentTheme:p}=dn({theme:t,colorScheme:a},ve),[h,g]=R(d);j(()=>{g(d)},[d]);let E=i(O=>{g(O),s&&s(O)},"updateState"),y=n.Children.count(o)>0,v=i((O,I)=>{if(O.isSameNode(I||null))return;O.querySelector('[tabindex="-1"]')?.focus(),O.setAttribute("aria-selected","true"),I?.removeAttribute("aria-selected")},"updateFocus"),C=i((O,I)=>{let N=O;for(;N&&N.parentElement;){if(N.getAttribute("role")===I)return N;N=N.parentElement}return null},"getParent"),w=i(O=>{let I=C(O,"tree");return I?Array.from(I.querySelectorAll("li")):[]},"getListElements"),k=i(O=>{let I=C(O,"group"),N=I?.previousElementSibling;if(N&&N.getAttribute("tabindex")==="-1"){let V=N.parentElement,J=O.parentElement;v(V,J)}},"moveBack"),D=i((O,I)=>{let N=w(O);N.forEach(V=>{V.removeAttribute("aria-selected")}),I==="start"&&N[0]&&v(N[0]),I==="end"&&N[N.length-1]&&v(N[N.length-1])},"moveHome"),T=i((O,I)=>{let N=w(O)||[];for(let V=0;V<N.length;V++){let J=N[V];if(J.getAttribute("aria-selected")==="true"){I==="up"&&N[V-1]?v(N[V-1],J):I==="down"&&N[V+1]&&v(N[V+1],J);return}}v(N[0])},"moveFocusAdjacent"),_=i((O,I)=>{let N=O.target;(O.key==="Enter"||O.key===" ")&&E(!h),O.key==="ArrowRight"&&h&&!I?T(N,"down"):O.key==="ArrowRight"&&E(!0),O.key==="ArrowLeft"&&(!h||I)?k(N):O.key==="ArrowLeft"&&E(!1),O.key==="ArrowDown"&&T(N,"down"),O.key==="ArrowUp"&&T(N,"up"),O.key==="Home"&&D(N,"start"),O.key==="End"&&D(N,"end")},"handleKeypress"),F=i((O,I)=>{let N=O.target,V=C(N,"treeitem"),J=w(N)||[],Lt=!1;for(let At=0;At<J.length;At++){let Pt=J[At];if(Pt.getAttribute("aria-selected")==="true"){V&&(Lt=!0,v(V,Pt));break}}!Lt&&V&&v(V),I||E(!h)},"handleClick"),M=i(O=>{let I=O.currentTarget;!I.contains(document.activeElement)&&I.getAttribute("role")==="tree"&&I.setAttribute("tabindex","0")},"handleBlur"),H=i(O=>{let I=O.target;if(I.getAttribute("role")==="tree"){let N=I.querySelector('[aria-selected="true"]');N?v(N):T(I,"down"),I.setAttribute("tabindex","-1")}},"handleFocus"),z=i(()=>{u?.()},"handleButtonFocus"),Q=i(O=>{let I=O*.9+.3;return{paddingLeft:`${I}em`,width:`calc(100% - ${I}em)`}},"getPaddingStyles"),{isChild:ne,depth:A,hasHover:x}=n.useContext(Jn),S=x?r:!1;if(!ne)return n.createElement("ul",(0,Yn.default)({role:"tree",tabIndex:0,className:Pe(ve.tree,ve.group,f,l),onFocus:H,onBlur:M},m),n.createElement(Jn.Provider,{value:{isChild:!0,depth:0,hasHover:S}},n.createElement(tn,e)));if(!y)return n.createElement("li",(0,Yn.default)({role:"treeitem",className:ve.item},m),n.createElement("div",{role:"button",className:Pe(ve.label,{[ve.hover]:S,[ve.focusWhite]:p==="firefox"}),tabIndex:-1,style:Q(A),onKeyDown:i(O=>{_(O,ne)},"onKeyDown"),onClick:i(O=>F(O,!0),"onClick"),onFocus:z},n.createElement("span",null,c)));let B=Pe(ve.arrow,{[ve.open]:h});return n.createElement("li",{role:"treeitem","aria-expanded":h,className:ve.item},n.createElement("div",{role:"button",tabIndex:-1,className:Pe(ve.label,{[ve.hover]:S,[ve.focusWhite]:p==="firefox"}),style:Q(A),onClick:i(O=>F(O),"onClick"),onKeyDown:i(O=>_(O),"onKeyDown"),onFocus:z},n.createElement("span",null,n.createElement("span",{"aria-hidden":!0,className:B}),n.createElement("span",null,c))),n.createElement("ul",(0,Yn.default)({role:"group",className:Pe(l,ve.group)},m),h&&n.Children.map(o,O=>n.createElement(Jn.Provider,{value:{isChild:!0,depth:A+1,hasHover:S}},O))))},"Tree");tn.defaultProps={open:!1,hover:!0};var B0=Ce(Fa()),R0=Ce(La()),K={"object-inspector":"ObjectInspector-object-inspector-0c33e82",objectInspector:"ObjectInspector-object-inspector-0c33e82","object-label":"ObjectInspector-object-label-b81482b",objectLabel:"ObjectInspector-object-label-b81482b",text:"ObjectInspector-text-25f57f3",key:"ObjectInspector-key-4f712bb",value:"ObjectInspector-value-f7ec2e5",string:"ObjectInspector-string-c496000",regex:"ObjectInspector-regex-59d45a3",error:"ObjectInspector-error-b818698",boolean:"ObjectInspector-boolean-2dd1642",number:"ObjectInspector-number-a6daabb",undefined:"ObjectInspector-undefined-3a68263",null:"ObjectInspector-null-74acb50",function:"ObjectInspector-function-07bbdcd","function-decorator":"ObjectInspector-function-decorator-3d22c24",functionDecorator:"ObjectInspector-function-decorator-3d22c24",prototype:"ObjectInspector-prototype-f2449ee",dark:"ObjectInspector-dark-0c96c97",chrome:"ObjectInspector-chrome-2f3ca98",light:"ObjectInspector-light-78bef54"},N0=["ast","theme","showKey","colorScheme","className"],xe=i((e,t,r,a,o)=>{let c=e.includes("-")?`"${e}"`:e,l=o<=0;return n.createElement("span",{className:K.text},!l&&a&&n.createElement(n.Fragment,null,n.createElement("span",{className:K.key},c),n.createElement("span",null,":\xA0")),n.createElement("span",{className:r},t))},"buildValue"),pc=i(e=>{let{ast:t,theme:r,showKey:a,colorScheme:o,className:c}=e,l=(0,R0.default)(e,N0),{themeClass:s}=dn({theme:r,colorScheme:o},K),[u,d]=R(n.createElement("span",null)),m=n.createElement("span",null);return j(()=>{t.value instanceof Promise&&i(async f=>{d(xe(t.key,`Promise { "${await dc(f)}" }`,K.key,a,t.depth))},"waitForPromiseResult")(t.value)},[t,a]),typeof t.value=="number"||typeof t.value=="bigint"?m=xe(t.key,String(t.value),K.number,a,t.depth):typeof t.value=="boolean"?m=xe(t.key,String(t.value),K.boolean,a,t.depth):typeof t.value=="string"?m=xe(t.key,`"${t.value}"`,K.string,a,t.depth):typeof t.value>"u"?m=xe(t.key,"undefined",K.undefined,a,t.depth):typeof t.value=="symbol"?m=xe(t.key,t.value.toString(),K.string,a,t.depth):typeof t.value=="function"?m=xe(t.key,`${t.value.name}()`,K.key,a,t.depth):typeof t.value=="object"&&(t.value===null?m=xe(t.key,"null",K.null,a,t.depth):Array.isArray(t.value)?m=xe(t.key,`Array(${t.value.length})`,K.key,a,t.depth):t.value instanceof Date?m=xe(t.key,`Date ${t.value.toString()}`,K.value,a,t.depth):t.value instanceof RegExp?m=xe(t.key,t.value.toString(),K.regex,a,t.depth):t.value instanceof Error?m=xe(t.key,t.value.toString(),K.error,a,t.depth):cc(t.value)?m=xe(t.key,"{\u2026}",K.key,a,t.depth):m=xe(t.key,t.value.constructor.name,K.key,a,t.depth)),n.createElement("span",(0,B0.default)({className:Pe(s,c)},l),u,m)},"ObjectValue");pc.defaultProps={showKey:!0};var mc=pc,Tt=Ce(Fa()),F0=Ce(La()),L0=["ast","theme","previewMax","open","colorScheme","className"],ir=i((e,t,r)=>{let a=[];for(let o=0;o<e.length;o++){let c=e[o];if(c.isPrototype||(a.push(n.createElement(mc,{key:c.key,ast:c,showKey:r})),o<e.length-1?a.push(", "):a.push(" ")),c.isPrototype&&o===e.length-1&&(a.pop(),a.push(" ")),o===t-1&&e.length>t){a.push("\u2026 ");break}}return a},"buildPreview"),P0=i((e,t,r,a)=>{let o=e.value.length;return t?n.createElement("span",null,"Array(",o,")"):n.createElement(n.Fragment,null,n.createElement("span",null,`${a==="firefox"?"Array":""}(${o}) [ `),ir(e.children,r,!1),n.createElement("span",null,"]"))},"getArrayLabel"),j0=i((e,t,r,a)=>e.isPrototype?n.createElement("span",null,`Object ${a==="firefox"?"{ \u2026 }":""}`):t?n.createElement("span",null,"{\u2026}"):n.createElement(n.Fragment,null,n.createElement("span",null,`${a==="firefox"?"Object ":""}{ `),ir(e.children,r,!0),n.createElement("span",null,"}")),"getObjectLabel"),M0=i((e,t,r)=>t?n.createElement("span",null,`Promise { "${String(e.children[0].value)}" }`):n.createElement(n.Fragment,null,n.createElement("span",null,"Promise { "),ir(e.children,r,!0),n.createElement("span",null,"}")),"getPromiseLabel"),U0=i((e,t,r,a)=>{let{size:o}=e.value;return t?n.createElement("span",null,`Map(${o})`):n.createElement(n.Fragment,null,n.createElement("span",null,`Map${a==="chrome"?`(${o})`:""} { `),ir(e.children,r,!0),n.createElement("span",null,"}"))},"getMapLabel"),$0=i((e,t,r)=>{let{size:a}=e.value;return t?n.createElement("span",null,"Set(",a,")"):n.createElement(n.Fragment,null,n.createElement("span",null,`Set(${e.value.size}) {`),ir(e.children,r,!0),n.createElement("span",null,"}"))},"getSetLabel"),fc=i(e=>{let{ast:t,theme:r,previewMax:a,open:o,colorScheme:c,className:l}=e,s=(0,F0.default)(e,L0),{themeClass:u,currentTheme:d}=dn({theme:r,colorScheme:c},K),m=t.isPrototype||!1,f=Pe(K.objectLabel,u,l,{[K.prototype]:m}),p=t.depth<=0,h=i(()=>n.createElement("span",{className:m?K.prototype:K.key},p?"":`${t.key}: `),"Key");return t.type==="array"?n.createElement("span",(0,Tt.default)({className:f},s),n.createElement(h,null),P0(t,o,a,d)):t.type==="function"?n.createElement("span",(0,Tt.default)({className:f},s),n.createElement(h,null),d==="chrome"&&n.createElement("span",{className:K.functionDecorator},"\u0192 "),n.createElement("span",{className:Pe({[K.function]:!m})},`${t.value.name}()`)):t.type==="promise"?n.createElement("span",(0,Tt.default)({className:f},s),n.createElement(h,null),M0(t,o,a)):t.type==="map"?n.createElement("span",(0,Tt.default)({className:f},s),n.createElement(h,null),U0(t,o,a,d)):t.type==="set"?n.createElement("span",(0,Tt.default)({className:f},s),n.createElement(h,null),$0(t,o,a)):n.createElement("span",(0,Tt.default)({className:f},s),n.createElement(h,null),j0(t,o,a,d))},"ObjectLabel");fc.defaultProps={previewMax:8,open:!1};var H0=fc,no=i(e=>{let{ast:t,expandLevel:r,depth:a}=e,[o,c]=R(),[l,s]=R(a<r);return j(()=>{i(async()=>{if(t.type!=="value"){let u=t.children.map(f=>f()),d=await Promise.all(u),m=(0,bi.default)((0,bi.default)({},t),{},{children:d});c(m)}},"resolve")()},[t]),o?n.createElement(tn,{hover:!1,open:l,label:n.createElement(H0,{open:l,ast:o}),onSelect:i(()=>{var u;(u=e.onSelect)===null||u===void 0||u.call(e,t)},"onSelect"),onUpdate:i(u=>{s(u)},"onUpdate")},o.children.map(u=>n.createElement(no,{key:u.key,ast:u,depth:a+1,expandLevel:r,onSelect:e.onSelect}))):n.createElement(tn,{hover:!1,label:n.createElement(mc,{ast:t}),onSelect:i(()=>{var u;(u=e.onSelect)===null||u===void 0||u.call(e,t)},"onSelect")})},"ObjectInspectorItem");no.defaultProps={expandLevel:0,depth:0};var V0=no,z0=["data","expandLevel","sortKeys","includePrototypes","className","theme","colorScheme","onSelect"],hc=i(e=>{let{data:t,expandLevel:r,sortKeys:a,includePrototypes:o,className:c,theme:l,colorScheme:s,onSelect:u}=e,d=(0,C0.default)(e,z0),[m,f]=R(void 0),{themeClass:p,currentTheme:h,currentColorScheme:g}=dn({theme:l,colorScheme:s},K);return j(()=>{i(async()=>{f(await w0(t,a,o))},"runParser")()},[t,a,o]),n.createElement("div",(0,A0.default)({className:Pe(K.objectInspector,c,p)},d),m&&n.createElement(T0,{theme:h,colorScheme:g},n.createElement(V0,{ast:m,expandLevel:r,onSelect:u})))},"ObjectInspector");hc.defaultProps={expandLevel:0,sortKeys:!0,includePrototypes:!0};var q0={base:"#444",nullish:"#7D99AA",string:"#16B242",number:"#5D40D0",boolean:"#f41840",objectkey:"#698394",instance:"#A15C20",function:"#EA7509",muted:"#7D99AA",tag:{name:"#6F2CAC",suffix:"#1F99E5"},date:"#459D9C",error:{name:"#D43900",message:"#444"},regex:{source:"#A15C20",flags:"#EA7509"},meta:"#EA7509",method:"#0271B6"},G0={base:"#eee",nullish:"#aaa",string:"#5FE584",number:"#6ba5ff",boolean:"#ff4191",objectkey:"#accfe6",instance:"#E3B551",function:"#E3B551",muted:"#aaa",tag:{name:"#f57bff",suffix:"#8EB5FF"},date:"#70D4D3",error:{name:"#f40",message:"#eee"},regex:{source:"#FAD483",flags:"#E3B551"},meta:"#FAD483",method:"#5EC1FF"},ue=i(()=>{let{base:e}=De();return e==="dark"?G0:q0},"useThemeColors"),W0=/[^A-Z0-9]/i,yi=/[\s.,…]+$/gm,gc=i((e,t)=>{if(e.length<=t)return e;for(let r=t-1;r>=0;r-=1)if(W0.test(e[r])&&r>10)return`${e.slice(0,r).replace(yi,"")}\u2026`;return`${e.slice(0,t).replace(yi,"")}\u2026`},"ellipsize"),K0=i(e=>{try{return JSON.stringify(e,null,1)}catch{return String(e)}},"stringify"),bc=i((e,t)=>e.flatMap((r,a)=>a===e.length-1?[r]:[r,n.cloneElement(t,{key:`sep${a}`})]),"interleave"),Et=i(({value:e,nested:t,showObjectInspector:r,callsById:a,...o})=>{switch(!0){case e===null:return n.createElement(Y0,{...o});case e===void 0:return n.createElement(J0,{...o});case Array.isArray(e):return n.createElement(eg,{...o,value:e,callsById:a});case typeof e=="string":return n.createElement(X0,{...o,value:e});case typeof e=="number":return n.createElement(Z0,{...o,value:e});case typeof e=="boolean":return n.createElement(Q0,{...o,value:e});case Object.prototype.hasOwnProperty.call(e,"__date__"):return n.createElement(og,{...o,...e.__date__});case Object.prototype.hasOwnProperty.call(e,"__error__"):return n.createElement(lg,{...o,...e.__error__});case Object.prototype.hasOwnProperty.call(e,"__regexp__"):return n.createElement(ig,{...o,...e.__regexp__});case Object.prototype.hasOwnProperty.call(e,"__function__"):return n.createElement(ng,{...o,...e.__function__});case Object.prototype.hasOwnProperty.call(e,"__symbol__"):return n.createElement(sg,{...o,...e.__symbol__});case Object.prototype.hasOwnProperty.call(e,"__element__"):return n.createElement(ag,{...o,...e.__element__});case Object.prototype.hasOwnProperty.call(e,"__class__"):return n.createElement(rg,{...o,...e.__class__});case Object.prototype.hasOwnProperty.call(e,"__callId__"):return n.createElement(ao,{call:a?.get(e.__callId__),callsById:a});case Object.prototype.toString.call(e)==="[object Object]":return n.createElement(tg,{value:e,showInspector:r,callsById:a,...o});default:return n.createElement(ug,{value:e,...o})}},"Node"),Y0=i(e=>{let t=ue();return n.createElement("span",{style:{color:t.nullish},...e},"null")},"NullNode"),J0=i(e=>{let t=ue();return n.createElement("span",{style:{color:t.nullish},...e},"undefined")},"UndefinedNode"),X0=i(({value:e,...t})=>{let r=ue();return n.createElement("span",{style:{color:r.string},...t},JSON.stringify(gc(e,50)))},"StringNode"),Z0=i(({value:e,...t})=>{let r=ue();return n.createElement("span",{style:{color:r.number},...t},e)},"NumberNode"),Q0=i(({value:e,...t})=>{let r=ue();return n.createElement("span",{style:{color:r.boolean},...t},String(e))},"BooleanNode"),eg=i(({value:e,nested:t=!1,callsById:r})=>{let a=ue();if(t)return n.createElement("span",{style:{color:a.base}},"[\u2026]");let o=e.slice(0,3).map((l,s)=>n.createElement(Et,{key:`${s}--${JSON.stringify(l)}`,value:l,nested:!0,callsById:r})),c=bc(o,n.createElement("span",null,", "));return e.length<=3?n.createElement("span",{style:{color:a.base}},"[",c,"]"):n.createElement("span",{style:{color:a.base}},"(",e.length,") [",c,", \u2026]")},"ArrayNode"),tg=i(({showInspector:e,value:t,callsById:r,nested:a=!1})=>{let o=De().base==="dark",c=ue();if(e)return n.createElement(n.Fragment,null,n.createElement(hc,{id:"interactions-object-inspector",data:t,includePrototypes:!1,colorScheme:o?"dark":"light"}));if(a)return n.createElement("span",{style:{color:c.base}},"{\u2026}");let l=bc(Object.entries(t).slice(0,2).map(([s,u])=>n.createElement(Te,{key:s},n.createElement("span",{style:{color:c.objectkey}},s,": "),n.createElement(Et,{value:u,callsById:r,nested:!0}))),n.createElement("span",null,", "));return Object.keys(t).length<=2?n.createElement("span",{style:{color:c.base}},"{ ",l," }"):n.createElement("span",{style:{color:c.base}},"(",Object.keys(t).length,") ","{ ",l,", \u2026 }")},"ObjectNode"),rg=i(({name:e})=>{let t=ue();return n.createElement("span",{style:{color:t.instance}},e)},"ClassNode"),ng=i(({name:e})=>{let t=ue();return e?n.createElement("span",{style:{color:t.function}},e):n.createElement("span",{style:{color:t.nullish,fontStyle:"italic"}},"anonymous")},"FunctionNode"),ag=i(({prefix:e,localName:t,id:r,classNames:a=[],innerText:o})=>{let c=e?`${e}:${t}`:t,l=ue();return n.createElement("span",{style:{wordBreak:"keep-all"}},n.createElement("span",{key:`${c}_lt`,style:{color:l.muted}},"<"),n.createElement("span",{key:`${c}_tag`,style:{color:l.tag.name}},c),n.createElement("span",{key:`${c}_suffix`,style:{color:l.tag.suffix}},r?`#${r}`:a.reduce((s,u)=>`${s}.${u}`,"")),n.createElement("span",{key:`${c}_gt`,style:{color:l.muted}},">"),!r&&a.length===0&&o&&n.createElement(n.Fragment,null,n.createElement("span",{key:`${c}_text`},o),n.createElement("span",{key:`${c}_close_lt`,style:{color:l.muted}},"<"),n.createElement("span",{key:`${c}_close_tag`,style:{color:l.tag.name}},"/",c),n.createElement("span",{key:`${c}_close_gt`,style:{color:l.muted}},">")))},"ElementNode"),og=i(({value:e})=>{let t=new Date(e);isNaN(Number(t))&&(tt.warn("Invalid date value:",e),t=null);let r=ue();if(!t)return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.date}},"Invalid date");let[a,o,c]=t.toISOString().split(/[T.Z]/);return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.date}},a,n.createElement("span",{style:{opacity:.7}},"T"),o==="00:00:00"?n.createElement("span",{style:{opacity:.7}},o):o,c==="000"?n.createElement("span",{style:{opacity:.7}},".",c):`.${c}`,n.createElement("span",{style:{opacity:.7}},"Z"))},"DateNode"),lg=i(({name:e,message:t})=>{let r=ue();return n.createElement("span",{style:{color:r.error.name}},e,t&&": ",t&&n.createElement("span",{style:{color:r.error.message},title:t.length>50?t:""},gc(t,50)))},"ErrorNode"),ig=i(({flags:e,source:t})=>{let r=ue();return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.regex.flags}},"/",n.createElement("span",{style:{color:r.regex.source}},t),"/",e)},"RegExpNode"),sg=i(({description:e})=>{let t=ue();return n.createElement("span",{style:{whiteSpace:"nowrap",color:t.instance}},"Symbol(",e&&n.createElement("span",{style:{color:t.meta}},'"',e,'"'),")")},"SymbolNode"),ug=i(({value:e})=>{let t=ue();return n.createElement("span",{style:{color:t.meta}},K0(e))},"OtherNode"),cg=i(({label:e})=>{let t=ue(),{typography:r}=De();return n.createElement("span",{style:{color:t.base,fontFamily:r.fonts.base,fontSize:r.size.s2-1}},e)},"StepNode"),ao=i(({call:e,callsById:t})=>{if(!e)return null;if(e.method==="step"&&e.path?.length===0)return n.createElement(cg,{label:e.args[0]});let r=e.path?.flatMap((c,l)=>{let s=c.__callId__;return[s?n.createElement(ao,{key:`elem${l}`,call:t?.get(s),callsById:t}):n.createElement("span",{key:`elem${l}`},c),n.createElement("wbr",{key:`wbr${l}`}),n.createElement("span",{key:`dot${l}`},".")]}),a=e.args?.flatMap((c,l,s)=>{let u=n.createElement(Et,{key:`node${l}`,value:c,callsById:t});return l<s.length-1?[u,n.createElement("span",{key:`comma${l}`},",\xA0"),n.createElement("wbr",{key:`wbr${l}`})]:[u]}),o=ue();return n.createElement(n.Fragment,null,n.createElement("span",{style:{color:o.base}},r),n.createElement("span",{style:{color:o.method}},e.method),n.createElement("span",{style:{color:o.base}},"(",n.createElement("wbr",null),a,n.createElement("wbr",null),")"))},"MethodCall"),Ei=i((e,t=0)=>{for(let r=t,a=1;r<e.length;r+=1)if(e[r]==="("?a+=1:e[r]===")"&&(a-=1),a===0)return e.slice(t,r);return""},"getParams"),Xn=i(e=>{try{return e==="undefined"?void 0:JSON.parse(e)}catch{return e}},"parseValue"),dg=b.span(({theme:e})=>({color:e.base==="light"?e.color.positiveText:e.color.positive})),pg=b.span(({theme:e})=>({color:e.base==="light"?e.color.negativeText:e.color.negative})),Zn=i(({value:e,parsed:t})=>t?n.createElement(Et,{showObjectInspector:!0,value:e,style:{color:"#D43900"}}):n.createElement(pg,null,e),"Received"),Qn=i(({value:e,parsed:t})=>t?typeof e=="string"&&e.startsWith("called with")?n.createElement(n.Fragment,null,e):n.createElement(Et,{showObjectInspector:!0,value:e,style:{color:"#16B242"}}):n.createElement(dg,null,e),"Expected"),vi=i(({message:e,style:t={}})=>{let r=cn(),a=e.split(`
`);return n.createElement("pre",{style:{margin:0,padding:"8px 10px 8px 36px",fontSize:Fe.size.s1,...t}},a.flatMap((o,c)=>{if(o.startsWith("expect(")){let f=Ei(o,7),p=f?7+f.length:0,h=f&&o.slice(p).match(/\.(to|last|nth)[A-Z]\w+\(/);if(h){let g=p+(h.index??0)+h[0].length,E=Ei(o,g);if(E)return["expect(",n.createElement(Zn,{key:`received_${f}`,value:f}),o.slice(p,g),n.createElement(Qn,{key:`expected_${E}`,value:E}),o.slice(g+E.length),n.createElement("br",{key:`br${c}`})]}}if(o.match(/^\s*- /))return[n.createElement(Qn,{key:o+c,value:o}),n.createElement("br",{key:`br${c}`})];if(o.match(/^\s*\+ /)||o.match(/^Received: $/))return[n.createElement(Zn,{key:o+c,value:o}),n.createElement("br",{key:`br${c}`})];let[,l,s]=o.match(/^(Expected|Received): (.*)$/)||[];if(l&&s)return l==="Expected"?["Expected: ",n.createElement(Qn,{key:o+c,value:Xn(s),parsed:!0}),n.createElement("br",{key:`br${c}`})]:["Received: ",n.createElement(Zn,{key:o+c,value:Xn(s),parsed:!0}),n.createElement("br",{key:`br${c}`})];let[,u,d]=o.match(/(Expected number|Received number|Number) of calls: (\d+)$/i)||[];if(u&&d)return[`${u} of calls: `,n.createElement(Et,{key:o+c,value:Number(d)}),n.createElement("br",{key:`br${c}`})];let[,m]=o.match(/^Received has value: (.+)$/)||[];return m?["Received has value: ",n.createElement(Et,{key:o+c,value:Xn(m)}),n.createElement("br",{key:`br${c}`})]:[n.createElement("span",{key:o+c,dangerouslySetInnerHTML:{__html:r.toHtml(o)}}),n.createElement("br",{key:`br${c}`})]}))},"MatcherResult"),mg=b.div({width:14,height:14,display:"flex",alignItems:"center",justifyContent:"center"}),yc=i(({status:e})=>{let t=De();switch(e){case"done":return n.createElement(hr,{color:t.color.positive,"data-testid":"icon-done"});case"error":return n.createElement(Fo,{color:t.color.negative,"data-testid":"icon-error"});case"active":return n.createElement(_o,{color:t.color.secondary,"data-testid":"icon-active"});case"waiting":return n.createElement(mg,{"data-testid":"icon-waiting"},n.createElement(br,{color:W(.5,"#CCCCCC"),size:6}));default:return null}},"StatusIcon"),fg=b.div({fontFamily:Fe.fonts.mono,fontSize:Fe.size.s1,overflowWrap:"break-word",inlineSize:"calc( 100% - 40px )"}),hg=b("div",{shouldForwardProp:i(e=>!["call","pausedAt"].includes(e.toString()),"shouldForwardProp")})(({theme:e,call:t})=>({position:"relative",display:"flex",flexDirection:"column",borderBottom:`1px solid ${e.appBorderColor}`,fontFamily:Fe.fonts.base,fontSize:13,...t.status==="error"&&{backgroundColor:e.base==="dark"?W(.93,e.color.negative):e.background.warning},paddingLeft:(t.ancestors?.length??0)*20}),({theme:e,call:t,pausedAt:r})=>r===t.id&&{"&::before":{content:'""',position:"absolute",top:-5,zIndex:1,borderTop:"4.5px solid transparent",borderLeft:`7px solid ${e.color.warning}`,borderBottom:"4.5px solid transparent"},"&::after":{content:'""',position:"absolute",top:-1,zIndex:1,width:"100%",borderTop:`1.5px solid ${e.color.warning}`}}),gg=b.div(({theme:e,isInteractive:t})=>({display:"flex","&:hover":t?{}:{background:e.background.hoverable}})),bg=b("button",{shouldForwardProp:i(e=>!["call"].includes(e.toString()),"shouldForwardProp")})(({theme:e,disabled:t,call:r})=>({flex:1,display:"grid",background:"none",border:0,gridTemplateColumns:"15px 1fr",alignItems:"center",minHeight:40,margin:0,padding:"8px 15px",textAlign:"start",cursor:t||r.status==="error"?"default":"pointer","&:focus-visible":{outline:0,boxShadow:`inset 3px 0 0 0 ${r.status==="error"?e.color.warning:e.color.secondary}`,background:r.status==="error"?"transparent":e.background.hoverable},"& > div":{opacity:r.status==="waiting"?.5:1}})),yg=b.div({display:"flex",alignItems:"center",padding:6}),Eg=b(G)(({theme:e})=>({color:e.textMutedColor,margin:"0 3px"})),vg=b(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),ea=b("div")(({theme:e})=>({padding:"8px 10px 8px 36px",fontSize:Fe.size.s1,color:e.color.defaultText,pre:{margin:0,padding:0}})),xg=b.span(({theme:e})=>({color:e.base==="dark"?"#5EC1FF":"#0271B6"})),Ag=b.span(({theme:e})=>({color:e.base==="dark"?"#eee":"#444"})),Cg=b.p(({theme:e})=>({color:e.base==="dark"?e.color.negative:e.color.negativeText,fontSize:e.typography.size.s2,maxWidth:500,textWrap:"balance"})),Sg=i(({exception:e})=>{let t=cn();if(!e)return null;if(e.callId===Ie)return P(ea,null,P("pre",null,P(xg,null,e.name,":")," ",P(Ag,null,e.message)),P(Cg,null,"The component failed to render properly. Automated component tests will not run until this is resolved. Check the full error message in Storybook\u2019s canvas to debug."));if(to(e))return P(vi,{...e});if(eo(e))return P(ea,null,P(vi,{message:`${e.message}${e.diff?`

${e.diff}`:""}`,style:{padding:0}}),P("p",null,"See the full stack trace in the browser console."));let r=e.message.split(`

`),a=r.length>1;return P(ea,null,P("pre",{dangerouslySetInnerHTML:{__html:t.toHtml(r[0])}}),a&&P("p",null,"See the full stack trace in the browser console."))},"Exception"),wg=i(({call:e,callsById:t,controls:r,controlStates:a,childCallIds:o,isHidden:c,isCollapsed:l,toggleCollapsed:s,pausedAt:u})=>{let[d,m]=R(!1),f=!a.goto||!e.interceptable||!!e.ancestors?.length;return c||e.id===Ie?null:P(hg,{call:e,pausedAt:u},P(gg,{isInteractive:f},P(bg,{"aria-label":"Interaction step",call:e,onClick:()=>r.goto(e.id),disabled:f,onMouseEnter:()=>a.goto&&m(!0),onMouseLeave:()=>a.goto&&m(!1)},P(yc,{status:d?"active":e.status}),P(fg,{style:{marginLeft:6,marginBottom:1}},P(ao,{call:e,callsById:t}))),P(yg,null,(o?.length??0)>0&&P(oe,{hasChrome:!1,tooltip:P(vg,{note:`${l?"Show":"Hide"} interactions`})},P(Eg,{onClick:s},P(wo,null))))),e.status==="error"&&e.exception?.callId===e.id&&P(Sg,{exception:e.exception}))},"Interaction"),kg={done:"positive",error:"negative",active:"warning",waiting:"warning"},Og=b.div(({theme:e,status:t})=>({padding:"4px 6px 4px 8px",borderRadius:"4px",backgroundColor:e.color[kg[t]],color:"white",fontFamily:Fe.fonts.base,textTransform:"uppercase",fontSize:Fe.size.s1,letterSpacing:3,fontWeight:Fe.weight.bold,width:65,textAlign:"center"})),Tg={done:"Pass",error:"Fail",active:"Runs",waiting:"Runs"},Dg=i(({status:e})=>{let t=Tg[e];return n.createElement(Og,{"aria-label":"Status of the test run",status:e},t)},"StatusBadge"),Ig=b.div(({theme:e})=>({boxShadow:`${e.appBorderColor} 0 -1px 0 0 inset`,background:e.background.app,position:"sticky",top:0,zIndex:1})),_g=b.nav(({theme:e})=>({height:40,display:"flex",alignItems:"center",justifyContent:"space-between",paddingLeft:15})),Bg=b(ge)(({theme:e})=>({borderRadius:4,padding:6,color:e.textMutedColor,"&:not(:disabled)":{"&:hover,&:focus-visible":{color:e.color.secondary}}})),Jt=b(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),tr=b(G)(({theme:e})=>({color:e.textMutedColor,margin:"0 3px"})),Rg=b(bn)({marginTop:0}),Ng=b(fn)(({theme:e})=>({color:e.textMutedColor,justifyContent:"flex-end",textAlign:"right",whiteSpace:"nowrap",marginTop:"auto",marginBottom:1,paddingRight:15,fontSize:13})),xi=b.div({display:"flex",alignItems:"center"}),Fg=b(tr)({marginLeft:9}),Lg=b(Bg)({marginLeft:9,marginRight:9,marginBottom:1,lineHeight:"12px"}),Pg=b(tr)(({theme:e,animating:t,disabled:r})=>({opacity:r?.5:1,svg:{animation:t?`${e.animation.rotate360} 200ms ease-out`:void 0}})),jg=i(({controls:e,controlStates:t,status:r,storyFileName:a,onScrollToEnd:o})=>{let c=r==="error"?"Scroll to error":"Scroll to end";return n.createElement(Ig,null,n.createElement($t,null,n.createElement(_g,{"aria-label":"Component tests toolbar"},n.createElement(xi,null,n.createElement(Dg,{status:r}),n.createElement(Lg,{onClick:o,disabled:!o},c),n.createElement(Rg,null),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go to start"})},n.createElement(Fg,{"aria-label":"Go to start",onClick:e.start,disabled:!t.start},n.createElement(Ro,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go back"})},n.createElement(tr,{"aria-label":"Go back",onClick:e.back,disabled:!t.back},n.createElement(Io,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go forward"})},n.createElement(tr,{"aria-label":"Go forward",onClick:e.next,disabled:!t.next},n.createElement(Bo,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go to end"})},n.createElement(tr,{"aria-label":"Go to end",onClick:e.end,disabled:!t.end},n.createElement(Ao,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Rerun"})},n.createElement(Pg,{"aria-label":"Rerun",onClick:e.rerun},n.createElement(Po,null)))),a&&n.createElement(xi,null,n.createElement(Ng,null,a)))))},"Subnav"),Mg=b.div(({theme:{color:e,typography:t,background:r}})=>({textAlign:"start",padding:"11px 15px",fontSize:`${t.size.s2-1}px`,fontWeight:t.weight.regular,lineHeight:"1rem",background:r.app,borderBottom:`1px solid ${e.border}`,color:e.defaultText,backgroundClip:"padding-box",position:"relative",code:{fontSize:`${t.size.s1-1}px`,color:"inherit",margin:"0 0.2em",padding:"0 0.2em",background:"rgba(255, 255, 255, 0.8)",borderRadius:"2px",boxShadow:"0 0 0 1px rgba(0, 0, 0, 0.1)"}})),Ug=i(({browserTestStatus:e})=>{let t=Ee().getDocsUrl({subpath:u0,versioned:!0,renderer:!0}),[r,a]=e==="error"?["the CLI","this browser"]:["this browser","the CLI"];return n.createElement(Mg,null,"This interaction test passed in ",r,", but the tests failed in ",a,"."," ",n.createElement(be,{href:t,target:"_blank",withArrow:!0},"Learn what could cause this"))},"TestDiscrepancyMessage"),$g=b.div(({theme:e})=>({height:"100%",background:e.background.content})),Ai=b.div(({theme:e})=>({borderBottom:`1px solid ${e.appBorderColor}`,backgroundColor:e.base==="dark"?W(.93,e.color.negative):e.background.warning,padding:15,fontSize:e.typography.size.s2-1,lineHeight:"19px"})),ta=b.code(({theme:e})=>({margin:"0 1px",padding:3,fontSize:e.typography.size.s1-1,lineHeight:1,verticalAlign:"top",background:"rgba(0, 0, 0, 0.05)",border:`1px solid ${e.appBorderColor}`,borderRadius:3})),Ci=b.div({paddingBottom:4,fontWeight:"bold"}),Hg=b.p({margin:0,padding:"0 0 20px"}),Si=b.pre(({theme:e})=>({margin:0,padding:0,"&:not(:last-child)":{paddingBottom:16},fontSize:e.typography.size.s1-1})),Vg=he(i(function({storyUrl:e,calls:t,controls:r,controlStates:a,interactions:o,fileName:c,hasException:l,caughtException:s,unhandledErrors:u,isPlaying:d,pausedAt:m,onScrollToEnd:f,endRef:p,hasResultMismatch:h,browserTestStatus:g}){let E=cn(),y=o.some(v=>v.id!==Ie);return P($g,null,h&&P(Ug,{browserTestStatus:g}),a.detached&&(y||l)&&P(E0,{storyUrl:e}),(o.length>0||l)&&P(jg,{controls:r,controlStates:a,status:g,storyFileName:c,onScrollToEnd:f}),P("div",{"aria-label":"Interactions list"},o.map(v=>P(wg,{key:v.id,call:v,callsById:t,controls:r,controlStates:a,childCallIds:v.childCallIds,isHidden:v.isHidden,isCollapsed:v.isCollapsed,toggleCollapsed:v.toggleCollapsed,pausedAt:m}))),s&&!sc(s)&&P(Ai,null,P(Ci,null,"Caught exception in ",P(ta,null,"play")," function"),P(Si,{"data-chromatic":"ignore",dangerouslySetInnerHTML:{__html:E.toHtml(Sa(s))}})),u&&P(Ai,null,P(Ci,null,"Unhandled Errors"),P(Hg,null,"Found ",u.length," unhandled error",u.length>1?"s":""," ","while running the play function. This might cause false positive assertions. Resolve unhandled errors or ignore unhandled errors with setting the",P(ta,null,"test.dangerouslyIgnoreUnhandledErrors")," ","parameter to ",P(ta,null,"true"),"."),u.map((v,C)=>P(Si,{key:C,"data-chromatic":"ignore"},Sa(v)))),P("div",{ref:p}),!d&&!s&&!y&&P(x0,null))},"InteractionsPanel"));function Sa(e){return e.stack||`${e.name}: ${e.message}`}i(Sa,"printSerializedError");var Lr={detached:!1,start:!1,back:!1,goto:!1,next:!1,end:!1},zg={done:"status-value:success",error:"status-value:error",active:"status-value:pending",waiting:"status-value:pending"},Pr=i(({log:e,calls:t,collapsed:r,setCollapsed:a})=>{let o=new Map,c=new Map;return e.map(({callId:l,ancestors:s,status:u})=>{let d=!1;return s.forEach(m=>{r.has(m)&&(d=!0),c.set(m,(c.get(m)||[]).concat(l))}),{...t.get(l),status:u,isHidden:d}}).map(l=>{let s=l.status==="error"&&l.ancestors&&o.get(l.ancestors.slice(-1)[0])?.status==="active"?"active":l.status;return o.set(l.id,{...l,status:s}),{...l,status:s,childCallIds:c.get(l.id),isCollapsed:r.has(l.id),toggleCollapsed:i(()=>a(u=>(u.has(l.id)?u.delete(l.id):u.add(l.id),new Set(u))),"toggleCollapsed")}})},"getInteractions"),ra=i((e,t)=>({id:Ie,method:"render",args:[],cursor:0,storyId:e,ancestors:[],path:[],interceptable:!0,retain:!1,exception:t}),"getInternalRenderCall"),jr=i(e=>({callId:Ie,status:e,ancestors:[]}),"getInternalRenderLogItem"),qg=he(i(function({refId:e,storyId:t,storyUrl:r}){let{statusValue:a,testRunId:o}=Yo(x=>{let S=e?void 0:x[t]?.[h0];return{statusValue:S?.value,testRunId:S?.data?.testRunId}}),[c,l]=St(un,{controlStates:Lr,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0}),[s,u]=R(void 0),[d,m]=R(new Set),[f,p]=R(!1),{controlStates:h=Lr,isErrored:g=!1,pausedAt:E=void 0,interactions:y=[],isPlaying:v=!1,caughtException:C=void 0,unhandledErrors:w=void 0}=c,k=X([jr("active")]),D=X(new Map([[Ie,ra(t)]])),T=i(({status:x,...S})=>D.current.set(S.id,S),"setCall"),_=X();j(()=>{let x;return Qe.IntersectionObserver&&(x=new Qe.IntersectionObserver(([S])=>u(S.isIntersecting?void 0:S.target),{root:Qe.document.querySelector("#panel-tab-content")}),_.current&&x.observe(_.current)),()=>x?.disconnect()},[]);let F=Ar({[ft.CALL]:T,[ft.SYNC]:x=>{k.current=[jr("done"),...x.logItems],l(S=>{let B=Pr({log:k.current,calls:D.current,collapsed:d,setCollapsed:m}),O=B.filter(({id:I,method:N})=>I!==Ie&&N!=="step").length;return{...S,controlStates:x.controlStates,pausedAt:x.pausedAt,interactions:B,interactionsCount:O}})},[el]:x=>{if(x.newPhase==="preparing")k.current=[jr("active")],D.current.set(Ie,ra(t)),l({controlStates:Lr,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0});else{let S=Pr({log:k.current,calls:D.current,collapsed:d,setCollapsed:m}),B=S.filter(({id:O,method:I})=>O!==Ie&&I!=="step").length;l(O=>({...O,interactions:S,interactionsCount:B,isPlaying:x.newPhase==="playing",pausedAt:void 0}))}},[tl]:x=>{k.current=[jr("error")],D.current.set(Ie,ra(t,{...x,callId:Ie}));let S=Pr({log:k.current,calls:D.current,collapsed:d,setCollapsed:m});l(B=>({...B,isErrored:!0,hasException:!0,caughtException:void 0,controlStates:Lr,pausedAt:void 0,interactions:S,interactionsCount:0}))},[Qo]:x=>{l(S=>({...S,caughtException:x,hasException:!0}))},[rl]:x=>{l(S=>({...S,unhandledErrors:x,hasException:!0}))}},[d]);j(()=>{l(x=>{let S=Pr({log:k.current,calls:D.current,collapsed:d,setCollapsed:m}),B=S.filter(({id:O,method:I})=>O!==Ie&&I!=="step").length;return{...x,interactions:S,interactionsCount:B}})},[l,d]);let M=ce(()=>({start:i(()=>F(ft.START,{storyId:t}),"start"),back:i(()=>F(ft.BACK,{storyId:t}),"back"),goto:i(x=>F(ft.GOTO,{storyId:t,callId:x}),"goto"),next:i(()=>F(ft.NEXT,{storyId:t}),"next"),end:i(()=>F(ft.END,{storyId:t}),"end"),rerun:i(()=>{F(Zo,{storyId:t})},"rerun")}),[F,t]),H=et("fileName",""),[z]=H.toString().split("/").slice(-1),Q=i(()=>s?.scrollIntoView({behavior:"smooth",block:"end"}),"scrollToTarget"),ne=!!C||!!w||y.some(x=>x.status==="error"),A=ce(()=>!v&&(y.length>0||ne)?ne?"error":"done":v?"active":void 0,[v,y,ne]);return j(()=>{if(A&&a&&a!=="status-value:pending"&&a!==zg[A]){let x=setTimeout(()=>p(S=>(S||F(p0,{type:"test-discrepancy",payload:{browserStatus:A==="done"?"PASS":"FAIL",cliStatus:A==="done"?"FAIL":"PASS",storyId:t,testRunId:o}}),!0)),2e3);return()=>clearTimeout(x)}else p(!1)},[F,A,a,t,o]),n.createElement(Te,{key:"component-tests"},n.createElement(Vg,{storyUrl:r,hasResultMismatch:f,browserTestStatus:A,calls:D.current,controls:M,controlStates:{...h,detached:!!e||h.detached},interactions:y,fileName:z,hasException:ne,caughtException:C,unhandledErrors:w,isErrored:g,isPlaying:v,pausedAt:E,endRef:_,onScrollToEnd:s&&Q}))},"PanelMemoized"));function Ec(){let e=Ee().getSelectedPanel(),[t={}]=St(un),{isErrored:r,hasException:a,interactionsCount:o}=t;return n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Interactions"),o&&!r&&!a?n.createElement(ct,{compact:!0,status:e===Qa?"active":"neutral"},o):null,r||a?n.createElement(yc,{status:"error"}):null)}i(Ec,"PanelTitle");var y3=Z.register(un,()=>{if(globalThis?.FEATURES?.interactions){let e=i(({state:t})=>{let r=t.refId&&t.refs[t.refId]?.url||document.location.origin,{pathname:a,search:o=""}=t.location,c=a+(t.refId?o.replace(`/${t.refId}_`,"/"):o);return{refId:t.refId,storyId:t.storyId,storyUrl:r+c}},"filter");Z.add(Qa,{type:ye.PANEL,title:i(()=>n.createElement(Ec,null),"title"),match:i(({viewMode:t})=>t==="story","match"),render:i(({active:t})=>n.createElement(Ut,{active:!!t},n.createElement(Ko,{filter:e},r=>n.createElement(qg,{...r}))),"render")})}}),wa="storybook/background",Gr="backgrounds",w3={UPDATE:`${wa}/update`},Gg={light:{name:"light",value:"#F8F8F8"},dark:{name:"dark",value:"#333"}},Wg=he(i(function(){let e=et(Gr),[t,r,a]=Ve(),[o,c]=R(!1),{options:l=Gg,disable:s=!0}=e||{};if(s)return null;let u=t[Gr]||{},d=u.value,m=u.grid||!1,f=l[d],p=!!a?.[Gr],h=Object.keys(l).length;return n.createElement(Kg,{length:h,backgroundMap:l,item:f,updateGlobals:r,backgroundName:d,setIsTooltipVisible:c,isLocked:p,isGridActive:m,isTooltipVisible:o})},"BackgroundSelector")),Kg=he(i(function(e){let{item:t,length:r,updateGlobals:a,setIsTooltipVisible:o,backgroundMap:c,backgroundName:l,isLocked:s,isGridActive:u,isTooltipVisible:d}=e,m=$(f=>{a({[Gr]:f})},[a]);return n.createElement(Te,null,n.createElement(G,{key:"grid",active:u,disabled:s,title:"Apply a grid to the preview",onClick:()=>m({value:l,grid:!u})},n.createElement(Co,null)),r>0?n.createElement(oe,{key:"background",placement:"top",closeOnOutsideClick:!0,tooltip:({onHide:f})=>n.createElement(zt,{links:[...t?[{id:"reset",title:"Reset background",icon:n.createElement(yr,null),onClick:i(()=>{m(void 0),f()},"onClick")}]:[],...Object.entries(c).map(([p,h])=>({id:p,title:h.name,icon:n.createElement(br,{color:h?.value||"grey"}),active:p===l,onClick:i(()=>{m({value:p,grid:u}),f()},"onClick")}))].flat()}),onVisibleChange:o},n.createElement(G,{disabled:s,key:"background",title:"Change the background of the preview",active:!!t||d},n.createElement(Do,null))):null)},"PureTool")),k3=Z.register(wa,()=>{globalThis?.FEATURES?.backgrounds&&Z.add(wa,{title:"Backgrounds",type:ye.TOOL,match:i(({viewMode:e,tabId:t})=>!!(e&&e.match(/^(story|docs)$/))&&!t,"match"),render:i(()=>n.createElement(Wg,null),"render")})}),_t="storybook/measure-addon",vc=`${_t}/tool`,R3={RESULT:`${_t}/result`,REQUEST:`${_t}/request`,CLEAR:`${_t}/clear`},Yg=i(()=>{let[e,t]=Ve(),{measureEnabled:r}=e||{},a=Ee(),o=$(()=>t({measureEnabled:!r}),[t,r]);return j(()=>{a.setAddonShortcut(_t,{label:"Toggle Measure [M]",defaultShortcut:["M"],actionName:"measure",showInMenu:!1,action:o})},[o,a]),n.createElement(G,{key:vc,active:r,title:"Enable measure",onClick:o},n.createElement(No,null))},"Tool"),N3=Z.register(_t,()=>{globalThis?.FEATURES?.measure&&Z.add(vc,{type:ye.TOOL,title:"Measure",match:i(({viewMode:e,tabId:t})=>e==="story"&&!t,"match"),render:i(()=>n.createElement(Yg,null),"render")})}),ka="storybook/outline",wi="outline",Jg=he(i(function(){let[e,t]=Ve(),r=Ee(),a=[!0,"true"].includes(e[wi]),o=$(()=>t({[wi]:!a}),[a]);return j(()=>{r.setAddonShortcut(ka,{label:"Toggle Outline",defaultShortcut:["alt","O"],actionName:"outline",showInMenu:!1,action:o})},[o,r]),n.createElement(G,{key:"outline",active:a,title:"Apply outlines to the preview",onClick:o},n.createElement(To,null))},"OutlineSelector")),$3=Z.register(ka,()=>{globalThis?.FEATURES?.outline&&Z.add(ka,{title:"Outline",type:ye.TOOL,match:i(({viewMode:e,tabId:t})=>!!(e&&e.match(/^(story|docs)$/))&&!t,"match"),render:i(()=>n.createElement(Jg,null),"render")})}),Bt="storybook/viewport",Wr="viewport",K3=`${Bt}/panel`,Xg=`${Bt}/tool`,Zg={mobile1:{name:"Small mobile",styles:{height:"568px",width:"320px"},type:"mobile"},mobile2:{name:"Large mobile",styles:{height:"896px",width:"414px"},type:"mobile"},tablet:{name:"Tablet",styles:{height:"1112px",width:"834px"},type:"tablet"},desktop:{name:"Desktop",styles:{height:"1024px",width:"1280px"},type:"desktop"}},rr={name:"Reset viewport",styles:{height:"100%",width:"100%"},type:"desktop"},xc=i((e,t)=>e.indexOf(t),"getCurrentViewportIndex"),Qg=i((e,t)=>{let r=xc(e,t);return r===e.length-1?e[0]:e[r+1]},"getNextViewport"),eb=i((e,t)=>{let r=xc(e,t);return r<1?e[e.length-1]:e[r-1]},"getPreviousViewport"),tb=i(async(e,t,r,a)=>{await e.setAddonShortcut(Bt,{label:"Previous viewport",defaultShortcut:["alt","shift","V"],actionName:"previous",action:i(()=>{r({viewport:eb(a,t)})},"action")}),await e.setAddonShortcut(Bt,{label:"Next viewport",defaultShortcut:["alt","V"],actionName:"next",action:i(()=>{r({viewport:Qg(a,t)})},"action")}),await e.setAddonShortcut(Bt,{label:"Reset viewport",defaultShortcut:["alt","control","V"],actionName:"reset",action:i(()=>{r({viewport:{value:void 0,isRotated:!1}})},"action")})},"registerShortcuts"),rb=b.div({display:"inline-flex",alignItems:"center"}),ki=b.div(({theme:e})=>({display:"inline-block",textDecoration:"none",padding:10,fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,lineHeight:"1",height:40,border:"none",borderTop:"3px solid transparent",borderBottom:"3px solid transparent",background:"transparent"})),nb=b(G)(()=>({display:"inline-flex",alignItems:"center"})),ab=b.div(({theme:e})=>({fontSize:e.typography.size.s2-1,marginLeft:10})),ob={desktop:n.createElement(ho,null),mobile:n.createElement(Oo,null),tablet:n.createElement(jo,null),other:n.createElement(Te,null)},lb=i(({api:e})=>{let t=et(Wr),[r,a,o]=Ve(),[c,l]=R(!1),{options:s=Zg,disable:u}=t||{},d=r?.[Wr]||{},m=typeof d=="string"?d:d.value,f=typeof d=="string"?!1:d.isRotated,p=s[m]||rr,h=c||p!==rr,g=Wr in o,E=Object.keys(s).length;if(j(()=>{tb(e,m,a,Object.keys(s))},[s,m,a,e]),p.styles===null||!s||E<1)return null;if(typeof p.styles=="function")return console.warn("Addon Viewport no longer supports dynamic styles using a function, use css calc() instead"),null;let y=f?p.styles.height:p.styles.width,v=f?p.styles.width:p.styles.height;return u?null:n.createElement(ib,{item:p,updateGlobals:a,viewportMap:s,viewportName:m,isRotated:f,setIsTooltipVisible:l,isLocked:g,isActive:h,width:y,height:v})},"ViewportTool"),ib=n.memo(i(function(e){let{item:t,viewportMap:r,viewportName:a,isRotated:o,updateGlobals:c,setIsTooltipVisible:l,isLocked:s,isActive:u,width:d,height:m}=e,f=$(p=>c({[Wr]:p}),[c]);return n.createElement(Te,null,n.createElement(oe,{placement:"bottom",tooltip:({onHide:p})=>n.createElement(zt,{links:[...length>0&&t!==rr?[{id:"reset",title:"Reset viewport",icon:n.createElement(yr,null),onClick:i(()=>{f(void 0),p()},"onClick")}]:[],...Object.entries(r).map(([h,g])=>({id:h,title:g.name,icon:ob[g.type],active:h===a,onClick:i(()=>{f({value:h,isRotated:!1}),p()},"onClick")}))].flat()}),closeOnOutsideClick:!0,onVisibleChange:l},n.createElement(nb,{disabled:s,key:"viewport",title:"Change the size of the preview",active:u,onDoubleClick:()=>{f({value:void 0,isRotated:!1})}},n.createElement(So,null),t!==rr?n.createElement(ab,null,t.name," ",o?"(L)":"(P)"):null)),n.createElement(Vo,{styles:{'iframe[data-is-storybook="true"]':{width:d,height:m}}}),t!==rr?n.createElement(rb,null,n.createElement(ki,{title:"Viewport width"},d.replace("px","")),s?"/":n.createElement(G,{key:"viewport-rotate",title:"Rotate viewport",onClick:()=>{f({value:a,isRotated:!o})}},n.createElement(Mo,null)),n.createElement(ki,{title:"Viewport height"},m.replace("px",""))):null)},"PureTool")),Q3=Z.register(Bt,e=>{globalThis?.FEATURES?.viewport&&Z.add(Xg,{title:"viewport / media-queries",type:ye.TOOL,match:i(({viewMode:t,tabId:r})=>t==="story"&&!r,"match"),render:i(()=>P(lb,{api:e}),"render")})}),sb="tag-filters",ub="static-filter",eO=Z.register(sb,e=>{let t=Object.entries(Qe.TAGS_OPTIONS??{}).reduce((r,a)=>{let[o,c]=a;return c.excludeFromSidebar&&(r[o]=!0),r},{});e.experimental_setFilter(ub,r=>{let a=r.tags??[];return(a.includes("dev")||r.type==="docs")&&a.filter(o=>t[o]).length===0})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
