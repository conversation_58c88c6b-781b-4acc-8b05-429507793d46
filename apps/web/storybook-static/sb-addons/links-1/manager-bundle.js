try{
(()=>{var S=__STORYBOOK_API__,{ActiveTabs:y,Consumer:v,ManagerContext:h,Provider:k,RequestResponseError:T,addons:n,combineParameters:j,controlOrMetaKey:g,controlOrMetaSymbol:E,eventMatchesShortcut:O,eventToShortcut:U,experimental_MockUniversalStore:f,experimental_UniversalStore:D,experimental_getStatusStore:N,experimental_getTestProviderStore:A,experimental_requestResponse:P,experimental_useStatusStore:x,experimental_useTestProviderStore:R,experimental_useUniversalStore:I,internal_fullStatusStore:W,internal_fullTestProviderStore:C,internal_universalStatusStore:M,internal_universalTestProviderStore:B,isMacLike:K,isShortcutTaken:V,keyToSymbol:q,merge:G,mockChannel:L,optionOrAltSymbol:Y,shortcutMatchesShortcut:$,shortcutToHumanString:H,types:Q,useAddonState:w,useArgTypes:z,useArgs:F,useChannel:J,useGlobalTypes:X,useGlobals:Z,useParameter:ee,useSharedState:oe,useStoryPrepared:te,useStorybookApi:se,useStorybookState:re}=__STORYBOOK_API__;var e="storybook/links",d={NAVIGATE:`${e}/navigate`,REQUEST:`${e}/request`,RECEIVE:`${e}/receive`};n.register(e,o=>{o.on(d.REQUEST,({kind:l,name:u})=>{let m=o.storyId(l,u);o.emit(d.RECEIVE,m)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
