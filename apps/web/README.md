# NovelWebsite Frontend

## 🎨 技術棧

- **React 18** - UI 框架
- **TypeScript** - 類型安全
- **Material-UI (MUI)** - 組件庫
- **Tailwind CSS** - 實用優先的 CSS 框架
- **styled-components** - CSS-in-JS 解決方案
- **Storybook** - 組件開發環境
- **React Router** - 路由管理
- **Axios** - HTTP 客戶端

## 🚀 快速開始

### 安裝依賴

```bash
npm install
```

### 開發模式

```bash
npm start
```

應用將在 http://localhost:3000 啟動。

### Storybook 開發

```bash
npm run storybook
```

Storybook 將在 http://localhost:6006 啟動。

## 📁 項目結構

```
frontend/
├── public/             # 靜態資源
├── src/
│   ├── components/     # 可重用組件
│   ├── pages/          # 頁面組件
│   ├── contexts/       # React Context
│   ├── services/       # API 服務
│   ├── types/          # TypeScript 類型
│   ├── utils/          # 工具函數
│   └── index.tsx       # 應用入口
├── .storybook/         # Storybook 配置
└── docs/               # 文檔
    ├── STYLING_GUIDE.md    # 樣式指南
    └── STORYBOOK_GUIDE.md  # Storybook 使用指南
```

## 🎨 樣式方案

我們採用混合樣式方案，詳見 [樣式指南](./docs/STYLING_GUIDE.md)：

1. **MUI** - 組件和主題系統
2. **Tailwind CSS** - 快速樣式和響應式設計
3. **styled-components** - 複雜動態樣式

## 📱 響應式設計

### 斷點

```javascript
// MUI 斷點
xs: 0px
sm: 600px
md: 900px
lg: 1200px
xl: 1536px

// Tailwind 斷點
sm: 640px
md: 768px
lg: 1024px
xl: 1280px
2xl: 1536px
```

### 移動優先

所有組件都遵循移動優先的設計原則。

## 🧪 測試

### 單元測試

```bash
npm test
```

### 集成測試

```bash
npm run test:integration
```

### E2E 測試

```bash
npm run test:e2e
```

## 📦 構建

```bash
npm run build
```

構建產物將在 `build/` 目錄中。

## 🔧 可用腳本

- `npm start` - 啟動開發服務器
- `npm run build` - 構建生產版本
- `npm test` - 運行測試
- `npm run lint` - 運行 ESLint
- `npm run type-check` - TypeScript 類型檢查
- `npm run storybook` - 啟動 Storybook
- `npm run build-storybook` - 構建 Storybook

## 🌐 環境變數

創建 `.env.local` 文件：

```env
REACT_APP_API_URL=http://localhost:8000/api
```

## 📚 相關文檔

- [樣式指南](./docs/STYLING_GUIDE.md)
- [Storybook 指南](./docs/STORYBOOK_GUIDE.md)
- [API 文檔](../backend/docs/api/)

## 🤝 貢獻

請遵循以下步驟：

1. 在 Storybook 中開發組件
2. 編寫測試
3. 確保通過 lint 和類型檢查
4. 提交 PR

## 📄 許可證

MIT
