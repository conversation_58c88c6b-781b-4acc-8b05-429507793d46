# 📚 Storybook 開發指南

## 🎯 概述

Storybook 是我們的組件開發和文檔工具，它提供了一個獨立的環境來開發、測試和展示 UI 組件。

## 🚀 快速開始

### 啟動 Storybook

```bash
cd frontend
npm run storybook
```

Storybook 將在 http://localhost:6006 啟動。

### 構建 Storybook

```bash
npm run build-storybook
```

## 📝 創建 Story

### 基本結構

每個組件都應該有對應的 `.stories.tsx` 文件：

```tsx
import type { Meta, StoryObj } from '@storybook/react';
import MyComponent from './MyComponent';

const meta = {
  title: 'Category/ComponentName',
  component: MyComponent,
  parameters: {
    layout: 'centered', // 'centered' | 'fullscreen' | 'padded'
  },
  tags: ['autodocs'], // 自動生成文檔
  argTypes: {
    // 定義可控制的屬性
    onClick: { action: 'clicked' },
  },
} satisfies Meta<typeof MyComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // 默認屬性
  },
};
```

### 組件分類

使用斜線來組織組件層級：

- `Components/Button` - 基礎組件
- `Features/NovelList` - 功能組件
- `Pages/Home` - 頁面組件
- `Layouts/Header` - 布局組件

## 🎨 測試不同狀態

### 響應式設計

```tsx
export const Mobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

export const Tablet: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
};
```

### 主題切換

```tsx
export const DarkMode: Story = {
  globals: {
    theme: 'dark',
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
};
```

### 交互狀態

```tsx
export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Error: Story = {
  args: {
    error: 'Something went wrong',
  },
};
```

## 🧪 進階功能

### 使用 Decorators

在 `.storybook/preview.js` 中添加全局 decorators：

```javascript
export const decorators = [
  Story => (
    <div style={{ margin: '3em' }}>
      <Story />
    </div>
  ),
];
```

### 使用 Play 函數測試交互

```tsx
export const UserClick: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const button = await canvas.getByRole('button');
    await userEvent.click(button);
  },
};
```

### 組合多個組件

```tsx
export const WithLayout: Story = {
  decorators: [
    Story => (
      <Layout>
        <Story />
      </Layout>
    ),
  ],
};
```

## 📋 最佳實踐

### 1. 完整覆蓋

為每個組件創建 stories，包括：

- 默認狀態
- 所有 props 變化
- 邊界情況
- 錯誤狀態
- 加載狀態

### 2. 使用真實數據

```tsx
import { mockNovelData } from '../mocks/novelData';

export const WithRealData: Story = {
  args: {
    novels: mockNovelData,
  },
};
```

### 3. 文檔化

使用 JSDoc 註釋來生成文檔：

```tsx
interface Props {
  /** 小說標題 */
  title: string;
  /** 點擊回調 */
  onClick?: () => void;
}
```

### 4. 組織結構

```
src/
├── components/
│   ├── Button/
│   │   ├── Button.tsx
│   │   ├── Button.stories.tsx
│   │   └── Button.test.tsx
│   └── NovelCard/
│       ├── NovelCard.tsx
│       ├── NovelCard.stories.tsx
│       └── NovelCard.test.tsx
```

## 🔧 配置說明

### main.js

- `stories`: Story 文件的位置
- `addons`: 啟用的插件
- `framework`: 使用的框架
- `webpackFinal`: 自定義 webpack 配置

### preview.js

- `parameters`: 全局參數設置
- `globalTypes`: 全局工具欄設置
- `decorators`: 全局裝飾器

## 🎯 整合到開發流程

1. **開發新組件時**：先在 Storybook 中開發
2. **代碼審查**：通過 Storybook 展示組件功能
3. **測試**：使用 Storybook 進行視覺測試
4. **文檔**：自動生成組件文檔

## 🔗 相關資源

- [Storybook 官方文檔](https://storybook.js.org/docs/react/get-started/introduction)
- [MUI + Storybook 整合指南](https://mui.com/material-ui/guides/interoperability/#storybook)
- [Tailwind CSS + Storybook](https://storybook.js.org/recipes/tailwindcss)
