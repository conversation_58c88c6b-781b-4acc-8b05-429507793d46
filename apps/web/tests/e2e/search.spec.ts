import { test, expect } from '@playwright/test';

test('search novels flow', async ({ page }) => {
  await page.route('**/api/novels/search*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        novels: [
          {
            id: '2',
            slug: 'search-novel',
            title: 'Search Novel',
            author: 'Author',
            cover: '/cover.jpg',
            description: 'A search result',
          },
        ],
      }),
    });
  });

  await page.goto('/search');
  await page.fill('input[type="text"]', 'search');
  await page.click('button[type="submit"]');
  await expect(page.getByText('Search Novel')).toBeVisible();
});
