# Django and Web Framework
Django==4.2.22
djangorestframework==3.15.2  # Security fix: Prevent XSS vulnerabilities
django-cors-headers==4.3.1
django-environ==0.11.2
django-filter==23.5

# Database (PostgreSQL) - 統一配置
psycopg2-binary==2.9.10  # 修復：使用支援 Python 3.13 的版本
dj-database-url==2.1.0  # DATABASE_URL解析
asyncpg==0.30.0  # 異步PostgreSQL客戶端，已確認Python 3.13兼容

# Testing
pytest==8.1.1
pytest-django==4.8.0
coverage==7.4.4
factory-boy==3.3.0
requests-mock==1.11.0

# Crawler Dependencies
scrapy==2.13.2
scrapy-djangoitem==1.1.1
scrapy-redis==0.9.1  # 修復：使用正確的最新版本
scrapy-playwright==0.0.33
playwright==1.52.0
beautifulsoup4==4.12.3
lxml==5.4.0
requests==2.32.4  # Security fix: CVE-2024-35195 - Fix .netrc credentials leak
fonttools==4.58.2

# Redis and Caching - Python 3.13兼容方案
redis==5.2.0  # 包含redis.asyncio模塊，完美支援Python 3.13
cachetools==6.0.0  # 本地緩存工具

# Async and Concurrency
aiohttp==3.12.11
aiofiles==24.1.0
asyncio-throttle==1.0.2
tenacity==9.1.2

# Testing Dependencies
aioresponses==0.7.6

# Utilities
python-dotenv==1.0.1
pillow==10.4.0
pytz==2024.1
python-dateutil==2.9.0
pydantic-settings==2.3.4

# Development Tools
black==24.3.0
flake8==7.0.0
isort==5.13.2
mypy==1.9.0

# Security
defusedxml==0.7.1  # Safe XML parsing to prevent XML vulnerabilities

# Monitoring and Logging
sentry-sdk==1.45.1  # Security fix: Prevent environment variable exposure to subprocesses
prometheus-client==0.22.1

# API Documentation
drf-spectacular==0.27.2
