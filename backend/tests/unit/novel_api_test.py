import os
import pytest
import django
from rest_framework.test import APIRequestFactory

pytest.skip("API view tests skipped", allow_module_level=True)

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "novel.settings")
django.setup()

from novel.api.views import NovelViewSet  # noqa: E402
from novel.models import Novel  # noqa: E402


@pytest.mark.django_db
class TestNovelViewSet:
    def test_search_returns_paginated_results(self):
        # 創建測試數據
        Novel.objects.create(
            title="Test Novel 1",
            author="Author 1",
            source="t1",
            source_id="1",
            source_url="http://n1",
        )
        Novel.objects.create(
            title="Test Novel 2",
            author="Author 2",
            source="t2",
            source_id="2",
            source_url="http://n2",
        )

        # 創建請求
        factory = APIRequestFactory()
        request = factory.get(
            "/api/novels/search/", {"query": "Test", "page": "1", "limit": "20"}
        )

        # 調用搜索方法
        viewset = NovelViewSet()
        response = viewset.search(request)

        # 驗證結果
        assert response.status_code == 200
        assert len(response.data["novels"]) == 2
        assert response.data["total"] == 2
        assert response.data["page"] == 1
        assert response.data["total_pages"] == 1

    def test_search_returns_empty_for_no_match(self):
        # 創建請求
        factory = APIRequestFactory()
        request = factory.get(
            "/api/novels/search/", {"query": "NonExisting", "page": "1", "limit": "20"}
        )

        # 調用搜索方法
        viewset = NovelViewSet()
        response = viewset.search(request)

        # 驗證結果
        assert response.status_code == 200
        assert len(response.data["novels"]) == 0
        assert response.data["total"] == 0
        assert response.data["page"] == 1
        assert response.data["total_pages"] == 0

    def test_fuzzy_search(self):
        # 創建測試數據
        Novel.objects.create(
            title="The Great Novel",
            author="John Doe",
            source="t3",
            source_id="3",
            source_url="http://n3",
        )

        # 創建請求
        factory = APIRequestFactory()
        request = factory.get(
            "/api/novels/search/", {"query": "Great", "page": "1", "limit": "20"}
        )

        # 調用搜索方法
        viewset = NovelViewSet()
        response = viewset.search(request)

        # 驗證結果
        assert response.status_code == 200
        assert len(response.data["novels"]) == 1
        assert response.data["novels"][0]["title"] == "The Great Novel"
