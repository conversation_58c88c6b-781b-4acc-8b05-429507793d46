import logging
from typing import Dict, List
from asgiref.sync import sync_to_async
import django
from django.conf import settings

# 確保 Django 設置已初始化
if not settings.configured:
    import os

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "novel.settings")
    django.setup()

from apps.catalog.models import Novel, Chapter

logger = logging.getLogger(__name__)


@sync_to_async
def get_last_chapter_number(novel_id: str) -> int:
    """取得此 novel_id 最後已下載的章節編號"""
    try:
        # 使用 source_id 查找小說
        novel = Novel.objects.filter(source_id=novel_id).first()
        if not novel:
            return 0

        last_chapter = (
            Chapter.objects.filter(novel=novel).order_by("-chapter_number").first()
        )
        if last_chapter:
            return last_chapter.chapter_number
        return 0
    except Exception as e:
        logger.error(f"獲取最後章節號失敗: {e}")
        return 0


@sync_to_async
def insert_or_update_novel(
    novel_id: str,
    title: str,
    author: str,
    intro: str,
    source: str,
    source_url: str,
    total_chapters: int,
) -> str:
    """寫入或更新小說基本信息"""
    try:
        novel, created = Novel.objects.update_or_create(
            source_id=novel_id,
            defaults={
                "title": title,
                "author": author,
                "description": intro,
                "source": source,
                "source_url": source_url,
                "total_chapters": total_chapters,
            },
        )
        if created:
            logger.info(f"創建新小說: {title}")
        else:
            logger.info(f"更新小說信息: {title}")
        return novel_id
    except Exception as e:
        logger.error(f"插入/更新小說信息失敗: {e}")
        return novel_id


@sync_to_async
def partial_save_chapters(novel_id: str, chapters: List[Dict]) -> None:
    """批次保存章節內容"""
    if not chapters:
        return

    try:
        # 使用 source_id 查找小說
        novel = Novel.objects.filter(source_id=novel_id).first()
        if not novel:
            logger.error(f"未找到小說 {novel_id}")
            return

        # 批量創建或更新章節
        for c in chapters:
            if c.get("chapter_number") is None:
                continue

            chapter_data = {
                "novel": novel,
                "chapter_number": c["chapter_number"],
                "title": c["chapter_title"],
                "content": c.get("chapter_content", ""),
                "source_url": c.get("chapter_url", ""),
            }

            # 嘗試獲取已存在的章節
            existing_chapter = Chapter.objects.filter(
                novel=novel, chapter_number=c["chapter_number"]
            ).first()

            if existing_chapter:
                # 更新現有章節
                for key, value in chapter_data.items():
                    if key != "novel":  # novel 是外鍵，不能直接設置
                        setattr(existing_chapter, key, value)
                existing_chapter.save()
            else:
                # 創建新章節
                Chapter.objects.create(**chapter_data)

        logger.info(f"成功保存 {len(chapters)} 個章節")
    except Exception as e:
        logger.error(f"批次保存章節失敗: {e}")
