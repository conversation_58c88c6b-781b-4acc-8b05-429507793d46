# Novel Crawler

一個用於爬取小說的異步爬蟲框架。

## 功能特點

- 異步爬取，提高效率
- 模塊化設計，易於擴展
- 完整的緩存機制
- 自動增量更新
- 詳細的日誌記錄

## 項目結構

```
├── crawler/
│   ├── spiders/          # 爬蟲實現
│   │   ├── base_spider.py
│   │   └── ttkan_spider.py
│   ├── db/              # 數據庫操作
│   │   └── novel_writer.py
│   ├── utils/           # 工具類
│   │   ├── cache.py
│   │   └── logger.py
│   └── config/          # 配置文件
│       └── settings.py
├── tests/               # 測試文件
│   ├── unit/
│   └── integration/
├── requirements.txt     # 項目依賴
└── README.md           # 項目說明
```

## 環境要求

- Python 3.8+
- PostgreSQL
- Redis

## 安裝

1. 克隆項目：

```bash
git clone <repository_url>
```

2. 安裝依賴：

```bash
pip install -r requirements.txt
```

3. 配置環境變量：

```bash
cp .env.example .env
# 編輯 .env 文件，設置必要的環境變量
```

## 使用方法

1. 運行爬蟲：

```bash
python -m crawler.main <novel_url>
```

2. 運行測試：

```bash
pytest tests/
```

## 添加新的爬蟲源

1. 在 `spiders` 目錄下創建新的爬蟲類：

```python
from .base_spider import NovelSpider

class NewSpider(NovelSpider):
    async def parse_novel_info(self) -> bool:
        # 實現解析邏輯
        pass

    async def parse_chapter_content(self, chapter: Dict) -> Optional[Dict]:
        # 實現解析邏輯
        pass
```

2. 在 `main.py` 中註冊新的爬蟲：

```python
SPIDERS = {
    'ttkan': TtkanSpider,
    'newsite': NewSpider
}
```

## 配置說明

主要配置項在 `config/settings.py` 中：

- `DATABASE_CONFIG`: 數據庫配置
- `REDIS_CONFIG`: Redis 配置
- `CRAWLER_CONFIG`: 爬蟲配置
- `LOG_CONFIG`: 日誌配置

## 可用爬蟲

- `TtkanSpider`：使用 `aiohttp` 進行網頁抓取，可快速下載章節資料。
- `TtkanNovelCrawler`：透過 `crawl4ai` 搭配無頭瀏覽器，適用需要執行 JavaScript 的站點。

更詳細的說明與指令可參考 `docs/crawlers.md`。

## 貢獻指南

1. Fork 項目
2. 創建特性分支
3. 提交更改
4. 推送到分支
5. 創建 Pull Request

## 許可證

MIT License
