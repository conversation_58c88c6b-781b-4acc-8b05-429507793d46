# flake8: noqa
from .base import *

# 測試環境設置
DEBUG = False
ALLOWED_HOSTS = ["test.example.com", "localhost", "127.0.0.1"]

# 數據庫設置
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.getenv("DB_NAME", "novel_test_db"),
        "USER": os.getenv("DB_USER", "postgres"),
        "PASSWORD": os.getenv("DB_PASSWORD"),
        "HOST": os.getenv("DB_HOST", "localhost"),
        "PORT": os.getenv("DB_PORT", "5432"),
    }
}

# 緩存設置
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": os.getenv("REDIS_URL", "redis://localhost:6379/1"),
    }
}

# 靜態文件設置
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
MEDIA_ROOT = os.path.join(BASE_DIR, "mediafiles")

# 安全設置
SECURE_SSL_REDIRECT = False  # 測試環境暫時不強制 HTTPS
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# 日誌設置
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "test.log"),
            "formatter": "verbose",
        },
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["file", "console"],
            "level": "INFO",
            "propagate": True,
        },
        "novel": {
            "handlers": ["file", "console"],
            "level": "INFO",
            "propagate": True,
        },
    },
}

# 爬蟲設置
CRAWLER_SETTINGS = {
    "USER_AGENT": os.getenv("CRAWLER_USER_AGENT", "NovelWebsite-Test-Bot"),
    "CONCURRENT_REQUESTS": int(os.getenv("CRAWLER_CONCURRENT_REQUESTS", 8)),
    "DOWNLOAD_DELAY": int(os.getenv("CRAWLER_DOWNLOAD_DELAY", 2)),
}

# 廣告設置
ADS_ENABLED = True
ADS_REFRESH_INTERVAL = 300  # 5分鐘

# API 設置
REST_FRAMEWORK["DEFAULT_THROTTLE_RATES"] = {
    "anon": "100/hour",
    "user": "1000/hour",
    "burst": "20/minute",
}
