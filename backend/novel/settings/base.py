import os
import dj_database_url
from dotenv import load_dotenv

# 載入 .env 文件
load_dotenv()

# PostgreSQL 數據庫配置
if os.getenv("DATABASE_URL"):
    # 使用 DATABASE_URL (適用於 Heroku 等雲平台)
    DATABASES = {"default": dj_database_url.parse(os.getenv("DATABASE_URL"))}
else:
    # 本地開發環境配置
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": os.getenv("POSTGRES_DB", "novelwebsite"),
            "USER": os.getenv("POSTGRES_USER", "novel_user"),
            "PASSWORD": os.getenv("POSTGRES_PASSWORD", "password"),
            "HOST": os.getenv("POSTGRES_HOST", "localhost"),
            "PORT": os.getenv("POSTGRES_PORT", "5432"),
            "OPTIONS": {
                "client_encoding": "UTF8",
            },
            "TEST": {
                "NAME": "test_" + os.getenv("POSTGRES_DB", "novelwebsite"),
            },
        }
    }
