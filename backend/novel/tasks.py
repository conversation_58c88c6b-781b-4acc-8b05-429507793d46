from celery import shared_task
from django.core.management import call_command
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


@shared_task
def crawl_yuepiao_task():
    """定時爬取起點月票榜任務"""
    try:
        logger.info(f"開始執行月票榜爬取任務 - {timezone.now()}")
        call_command("crawl_yuepiao", pages=3)
        logger.info(f"月票榜爬取任務完成 - {timezone.now()}")
        return True
    except Exception as e:
        logger.error(f"月票榜爬取任務失敗: {str(e)}")
        return False
