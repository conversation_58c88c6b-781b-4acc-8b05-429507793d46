from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth.models import User
from apps.catalog.models import Novel, Chapter, ReadingHistory
from apps.catalog.api.serializers import ChapterDetailSerializer


class ChapterViewTests(TestCase):
    def setUp(self):
        # 創建測試用戶
        self.user = User.objects.create_user(
            username="testuser", password="testpass123"  # gitleaks:allow
        )
        # 關聯使用者資料
        from apps.catalog.models import UserProfile

        UserProfile.objects.create(user=self.user)

        # 創建測試小說
        self.novel = Novel.objects.create(
            title="測試小說", author="測試作者", description="測試描述"
        )

        # 創建測試章節
        self.chapters = []
        for i in range(3):
            chapter = Chapter.objects.create(
                novel=self.novel,
                title=f"第{i+1}章",
                content=f"第{i+1}章的內容",
                chapter_number=i + 1,
            )
            self.chapters.append(chapter)

        self.client = APIClient()

    def test_chapter_detail_view(self):
        """測試章節詳情視圖"""
        url = reverse("catalog:chapter-detail", args=[self.chapters[0].id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        serializer = ChapterDetailSerializer(self.chapters[0])
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], serializer.data["id"])
        self.assertEqual(response.data["title"], serializer.data["title"])
        self.assertEqual(response.data["content"], serializer.data["content"])
        self.assertEqual(
            response.data["chapter_number"], serializer.data["chapter_number"]
        )

    def test_chapter_navigation(self):
        """測試章節導航（上一章/下一章）"""
        # 測試中間章節
        url = reverse("catalog:chapter-detail", args=[self.chapters[1].id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # API 返回章節詳情即可

        # 測試第一章（沒有上一章）
        url = reverse("catalog:chapter-detail", args=[self.chapters[0].id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 第一章沒有上一章

        # 測試最後一章（沒有下一章）
        url = reverse("catalog:chapter-detail", args=[self.chapters[-1].id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 最後一章沒有下一章

    def test_reading_history(self):
        """測試閱讀歷史記錄"""
        self.client.force_authenticate(user=self.user)
        url = reverse("catalog:chapter-detail", args=[self.chapters[0].id])

        # 訪問章節
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 檢查是否創建了閱讀歷史
        history = ReadingHistory.objects.filter(
            user_profile__user=self.user, chapter=self.chapters[0]
        )
        self.assertTrue(history.exists())

    def test_chapter_content_format(self):
        """測試章節內容格式"""
        # 創建包含特殊格式的章節
        special_chapter = Chapter.objects.create(
            novel=self.novel,
            title="特殊格式章節",
            content="第一段\n\n第二段\n    縮進的段落\n> 引用文字",
            chapter_number=4,
        )

        url = reverse("catalog:chapter-detail", args=[special_chapter.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 檢查內容是否正確保留了格式
        self.assertIn("第一段", response.data["content"])
        self.assertIn("第二段", response.data["content"])
        self.assertIn("縮進的段落", response.data["content"])
        self.assertIn("引用文字", response.data["content"])

    def test_chapter_access_count(self):
        """測試章節訪問次數統計"""
        url = reverse("catalog:chapter-detail", args=[self.chapters[0].id])

        # 多次訪問章節
        for _ in range(3):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 檢查訪問次數
        chapter = Chapter.objects.get(id=self.chapters[0].id)
        self.assertEqual(chapter.views, 3)

    def test_chapter_list_pagination(self):
        """測試章節列表分頁"""
        # 創建更多章節
        for i in range(20):
            Chapter.objects.create(
                novel=self.novel,
                title=f"額外章節{i+1}",
                content=f"額外章節{i+1}的內容",
                chapter_number=i + 4,
            )

        url = reverse("catalog:chapter-list")
        response = self.client.get(url, {"novel": self.novel.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertEqual(len(response.data["results"]), 10)  # 默認每頁10條

    def test_chapter_cache(self):
        """測試章節緩存"""
        url = reverse("catalog:chapter-detail", args=[self.chapters[0].id])

        # 第一次請求
        response1 = self.client.get(url)
        self.assertEqual(response1.status_code, status.HTTP_200_OK)

        # 修改章節內容
        self.chapters[0].content = "修改後的內容"
        self.chapters[0].save()

        # 第二次請求（應該返回緩存的結果）
        response2 = self.client.get(url)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        self.assertNotEqual(response1.data["content"], response2.data["content"])
