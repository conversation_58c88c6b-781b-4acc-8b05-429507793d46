from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth.models import User
from unittest.mock import patch

from apps.catalog.models import Novel, UserProfile
from apps.catalog.api.views import NovelViewSet


class NovelFavoriteTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="favoriteuser", password="testpass123"  # gitleaks:allow
        )
        UserProfile.objects.create(user=self.user)
        self.novel = Novel.objects.create(
            title="收藏小說",
            author="作者",
            description="描述",
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

    def test_favorite_toggle(self):
        url = reverse("catalog:novel-favorite", kwargs={"pk": self.novel.pk})
        with patch.object(NovelViewSet, "get_object", return_value=self.novel):
            response = self.client.post(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.novel.refresh_from_db()
            self.assertEqual(self.novel.favorites, 1)
            self.assertTrue(
                self.user.catalog_profile.favorite_novels.filter(
                    pk=self.novel.pk
                ).exists(),
                "Novel should be added to user's favorite_novels M2M list "
                "after favoriting.",
            )

            response = self.client.post(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.novel.refresh_from_db()
            self.assertEqual(self.novel.favorites, 0)
            self.assertFalse(
                self.user.catalog_profile.favorite_novels.filter(
                    pk=self.novel.pk
                ).exists(),
                "Novel should be removed from user's favorite_novels M2M list "
                "after unfavoriting.",
            )
