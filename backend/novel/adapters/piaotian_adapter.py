"""Piaotian adapter using YAML selectors."""

from __future__ import annotations

import re
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse

import yaml
from bs4 import BeautifulSoup

from .base_adapter import BaseAdapter, ChapterInfo, NovelInfo

logger = logging.getLogger(__name__)


class PiaotianAdapter(BaseAdapter):
    """Adapter for piaotian55.net like websites."""

    def __init__(self) -> None:
        super().__init__(base_url="https://piaotian55.net", site_name="Piaotian")
        selector_path = Path(__file__).with_name("selectors") / "piaotian.yaml"
        try:
            with selector_path.open("r", encoding="utf-8") as f:
                self.selectors: Dict[str, str] = yaml.safe_load(f) or {}
        except OSError as exc:
            logger.error("Unable to load selector file %s: %s", selector_path, exc)
            self.selectors = {}

    # helper methods
    def _select_text(self, soup: BeautifulSoup, key: str) -> str:
        sel = self.selectors.get(key, "")
        if not sel:
            return ""
        el = soup.select_one(sel)
        return el.get_text(strip=True) if el else ""

    def _select_all(self, soup: BeautifulSoup, key: str) -> List[Any]:
        sel = self.selectors.get(key, "")
        return soup.select(sel) if sel else []

    def extract_novel_info(self, novel_url: str) -> Optional[NovelInfo]:
        soup = self.get_soup(novel_url)
        if not soup:
            return None

        title = self._select_text(soup, "novel_title") or "未知小說"
        author = self._select_text(soup, "author") or "未知作者"
        description = self._select_text(soup, "description")
        chapter_links = self._select_all(soup, "chapter_list")
        total_chapters = len(chapter_links) if chapter_links else None

        return NovelInfo(
            title=title,
            author=author,
            description=description or f"來自{self.site_name}的小說《{title}》",
            status="連載中",
            categories=[],
            tags=[],
            source_site=self.site_name,
            total_chapters=total_chapters,
        )

    def extract_chapter_list(self, novel_url: str) -> List[Dict[str, Any]]:
        soup = self.get_soup(novel_url)
        if not soup:
            return []

        chapters = []
        for idx, link in enumerate(self._select_all(soup, "chapter_list"), start=1):
            chapter_url = urljoin(self.base_url, link.get("href", ""))
            chapter_title = link.get_text(strip=True)
            chapters.append(
                {
                    "title": chapter_title,
                    "url": chapter_url,
                    "chapter_number": idx,
                }
            )
        return chapters

    def extract_chapter_content(self, chapter_url: str) -> Optional[ChapterInfo]:
        soup = self.get_soup(chapter_url)
        if not soup:
            return None

        title = self._select_text(soup, "chapter_title") or "未知章節"
        content_el = soup.select_one(self.selectors.get("content", ""))
        content = content_el.get_text(separator="\n", strip=True) if content_el else ""

        # derive novel id and chapter number from url
        url_parts = urlparse(chapter_url).path.strip("/").split("/")
        novel_id = url_parts[1] if len(url_parts) > 1 else ""
        match = re.search(r"(\d+)", url_parts[-1])
        chapter_number = int(match.group(1)) if match else 1

        next_link = soup.select_one(self.selectors.get("next_chapter", ""))
        prev_link = soup.select_one(self.selectors.get("previous_chapter", ""))

        next_url = urljoin(chapter_url, next_link.get("href")) if next_link else None
        prev_url = urljoin(chapter_url, prev_link.get("href")) if prev_link else None

        return ChapterInfo(
            title=title,
            content=content,
            chapter_number=chapter_number,
            url=chapter_url,
            novel_id=novel_id,
            next_chapter_url=next_url,
            previous_chapter_url=prev_url,
        )

    def search_novels(self, keyword: str) -> List[Dict[str, Any]]:  # pragma: no cover
        logger.warning("Piaotian does not support search in this adapter")
        return []

    # Scrapy-optimized methods that work with response objects instead of new requests
    def extract_novel_info_from_response(self, response) -> Optional[NovelInfo]:
        """從Scrapy response物件提取小說資訊，避免重複HTTP請求"""
        soup = BeautifulSoup(response.text, "html.parser")
        if not soup:
            return None

        title = self._select_text(soup, "novel_title") or "未知小說"
        author = self._select_text(soup, "author") or "未知作者"
        description = self._select_text(soup, "description")
        chapter_links = self._select_all(soup, "chapter_list")
        total_chapters = len(chapter_links) if chapter_links else None

        return NovelInfo(
            title=title,
            author=author,
            description=description or f"來自{self.site_name}的小說《{title}》",
            status="連載中",
            categories=[],
            tags=[],
            source_site=self.site_name,
            total_chapters=total_chapters,
        )

    def extract_chapter_list_from_response(self, response) -> List[Dict[str, Any]]:
        """從Scrapy response物件提取章節列表，避免重複HTTP請求"""
        soup = BeautifulSoup(response.text, "html.parser")
        if not soup:
            return []

        chapters = []
        for idx, link in enumerate(self._select_all(soup, "chapter_list"), start=1):
            chapter_url = urljoin(self.base_url, link.get("href", ""))
            chapter_title = link.get_text(strip=True)
            chapters.append(
                {
                    "title": chapter_title,
                    "url": chapter_url,
                    "chapter_number": idx,
                }
            )
        return chapters

    def extract_chapter_content_from_response(self, response) -> Optional[ChapterInfo]:
        """從Scrapy response物件提取章節內容，避免重複HTTP請求"""
        soup = BeautifulSoup(response.text, "html.parser")
        if not soup:
            return None

        title = self._select_text(soup, "chapter_title") or "未知章節"
        content_el = soup.select_one(self.selectors.get("content", ""))
        content = content_el.get_text(separator="\n", strip=True) if content_el else ""

        # derive novel id and chapter number from url
        url_parts = urlparse(response.url).path.strip("/").split("/")
        novel_id = url_parts[1] if len(url_parts) > 1 else ""
        match = re.search(r"(\d+)", url_parts[-1])
        chapter_number = int(match.group(1)) if match else 1

        next_link = soup.select_one(self.selectors.get("next_chapter", ""))
        prev_link = soup.select_one(self.selectors.get("previous_chapter", ""))

        next_url = urljoin(response.url, next_link.get("href")) if next_link else None
        prev_url = urljoin(response.url, prev_link.get("href")) if prev_link else None

        return ChapterInfo(
            title=title,
            content=content,
            chapter_number=chapter_number,
            url=response.url,
            novel_id=novel_id,
            next_chapter_url=next_url,
            previous_chapter_url=prev_url,
        )
