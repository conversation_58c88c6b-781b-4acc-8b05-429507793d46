"""
CZBooks.net 網站適配器
實現對 CZBooks.net 小說網站的內容抓取功能
"""

import re
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
import logging

from .base_adapter import BaseAdapter, NovelInfo, ChapterInfo

logger = logging.getLogger(__name__)


class CZBooksAdapter(BaseAdapter):
    """CZBooks.net 網站適配器"""

    def __init__(self):
        super().__init__(base_url="https://czbooks.net", site_name="CZBooks.net")

    def extract_novel_info(self, novel_url: str) -> Optional[NovelInfo]:
        """從小說主頁提取小說基本資訊"""
        soup = self.get_soup(novel_url)
        if not soup:
            return None

        try:
            # 從標題中提取小說名稱 (格式: 【免費小說】《書名》...)
            title_tag = soup.find("title")
            if not title_tag:
                return None

            title_text = title_tag.get_text()
            title_match = re.search(r"《(.+?)》", title_text)
            title = (
                title_match.group(1)
                if title_match
                else title_text.split("|")[0].strip()
            )

            # 從頁面內容提取作者資訊
            author = "未知作者"

            # 查找章節連結以估算總章節數
            chapter_links = soup.find_all("a", href=re.compile(r"/n/\d+/\d+"))
            total_chapters = len(chapter_links) if chapter_links else None

            return NovelInfo(
                title=title,
                author=author,
                description=f"來自CZBooks.net的小說《{title}》",
                status="連載中",  # CZBooks通常為完結小說
                categories=["文學小說"],
                tags=[],
                source_site="CZBooks.net",
                total_chapters=total_chapters,
            )

        except Exception as e:
            logger.error(f"提取小說資訊失敗: {e}")
            return None

    def extract_chapter_list(self, novel_url: str) -> List[Dict[str, Any]]:
        """提取章節列表"""
        soup = self.get_soup(novel_url)
        if not soup:
            return []

        chapters = []

        # 查找所有章節連結
        chapter_links = soup.find_all("a", href=re.compile(r"/n/\d+/\d+"))

        for idx, link in enumerate(chapter_links, 1):
            chapter_url = urljoin(self.base_url, link.get("href"))
            chapter_title = link.get_text().strip()

            # 提取章節號碼
            chapter_match = re.search(r"第(\d+)章", chapter_title)
            chapter_number = int(chapter_match.group(1)) if chapter_match else idx

            chapters.append(
                {
                    "title": chapter_title,
                    "url": chapter_url,
                    "chapter_number": chapter_number,
                }
            )

        return chapters

    def extract_chapter_content(self, chapter_url: str) -> Optional[ChapterInfo]:
        """提取章節內容"""
        soup = self.get_soup(chapter_url)
        if not soup:
            return None

        try:
            # 從URL提取小說ID和章節號
            url_parts = urlparse(chapter_url)
            path_parts = url_parts.path.split("/")
            novel_id = path_parts[2] if len(path_parts) > 2 else None

            # 提取章節標題 - 通常在頁面標題中
            title_tag = soup.find("title")
            chapter_title = title_tag.get_text().strip() if title_tag else "未知章節"

            # 提取章節號碼
            chapter_match = re.search(r"第(\d+)章", chapter_title)
            chapter_number = int(chapter_match.group(1)) if chapter_match else 1

            # 提取章節內容 - 根據實際HTML結構調整
            content_elements = []

            # 查找包含文字內容的div標籤（包含子元素的文本）
            content_divs = soup.find_all("div")
            for div in content_divs:
                text = div.get_text().strip()
                if len(text) > 20:  # 降低最小長度限制，過濾掉空內容和極短文本
                    content_elements.append(text)

            content = "\n\n".join(content_elements)

            # 查找下一章連結
            next_link = soup.find("a", string=re.compile(r"下一章|下一頁"))
            next_chapter_url = None
            if next_link and next_link.get("href"):
                next_chapter_url = urljoin(self.base_url, next_link.get("href"))

            # 查找上一章連結
            prev_link = soup.find("a", string=re.compile(r"上一章|上一頁"))
            previous_chapter_url = None
            if prev_link and prev_link.get("href"):
                previous_chapter_url = urljoin(self.base_url, prev_link.get("href"))

            return ChapterInfo(
                title=chapter_title,
                content=content,
                chapter_number=chapter_number,
                url=chapter_url,
                novel_id=novel_id or "",
                next_chapter_url=next_chapter_url,
                previous_chapter_url=previous_chapter_url,
            )

        except Exception as e:
            logger.error(f"提取章節內容失敗: {e}")
            return None

    def validate_novel_url(self, url: str) -> bool:
        """驗證是否為有效的CZBooks小說URL"""
        pattern = r"https?://czbooks\.net/n/\d+"
        return bool(re.match(pattern, url))

    def search_novels(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索小說 (CZBooks.net可能沒有搜索功能，此處返回空列表)"""
        logger.warning("CZBooks.net暫不支持搜索功能")
        return []

    def get_search_results(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索小說 (CZBooks.net可能沒有搜索功能，此處返回空列表)"""
        logger.warning("CZBooks.net暫不支持搜索功能")
        return []
