"""
BaseAdapter 基礎適配器抽象類
提供所有網站適配器的標準介面和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import requests
from bs4 import BeautifulSoup
import logging

logger = logging.getLogger(__name__)


@dataclass
class NovelInfo:
    """小說基本資訊"""

    title: str
    author: str
    description: str
    status: str  # '連載中', '已完結', '暫停'
    categories: List[str]
    tags: List[str]
    source_site: str  # 來源網站名稱
    cover_url: Optional[str] = None
    total_chapters: Optional[int] = None
    last_updated: Optional[str] = None
    view_count: Optional[int] = None
    favorite_count: Optional[int] = None


@dataclass
class ChapterInfo:
    """章節資訊"""

    title: str
    content: str
    chapter_number: int
    url: str
    novel_id: str
    next_chapter_url: Optional[str] = None
    previous_chapter_url: Optional[str] = None


class BaseAdapter(ABC):
    """網站適配器基礎類"""

    def __init__(self, base_url: str, site_name: str):
        self.base_url = base_url.rstrip("/")
        self.site_name = site_name
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": (
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                    "AppleWebKit/537.36"
                )
            }
        )

    def get_soup(self, url: str) -> Optional[BeautifulSoup]:
        """獲取網頁內容並解析為BeautifulSoup對象"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            return BeautifulSoup(response.content, "html.parser")
        except Exception as e:
            logger.error(f"Failed to fetch {url}: {e}")
            return None

    @abstractmethod
    def extract_novel_info(self, novel_url: str) -> Optional[NovelInfo]:
        """從小說主頁提取小說基本資訊"""
        pass

    @abstractmethod
    def extract_chapter_list(self, novel_url: str) -> List[Dict[str, Any]]:
        """從小說主頁提取章節列表"""
        pass

    @abstractmethod
    def extract_chapter_content(self, chapter_url: str) -> Optional[ChapterInfo]:
        """從章節頁面提取章節內容"""
        pass

    @abstractmethod
    def search_novels(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索小說"""
        pass

    def validate_url(self, url: str) -> bool:
        """驗證URL是否屬於當前網站"""
        return url.startswith(self.base_url)

    def normalize_novel_id(self, novel_url: str) -> str:
        """從小說URL中提取標準化的小說ID"""
        if not self.validate_url(novel_url):
            raise ValueError(f"URL {novel_url} does not belong to {self.site_name}")
        return novel_url.replace(self.base_url, "").strip("/")
