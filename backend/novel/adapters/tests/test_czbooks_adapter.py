"""
CZBooks適配器測試
"""

import unittest
import requests_mock

from novel.adapters.base_adapter import BaseAdapter, NovelInfo, ChapterInfo
from novel.adapters.czbooks_adapter import CZBooksAdapter


class TestCZBooksAdapter(unittest.TestCase):
    """CZBooks適配器測試類"""

    def setUp(self):
        """測試設置"""
        self.adapter = CZBooksAdapter()

    def test_validate_novel_url(self):
        """測試URL驗證"""
        # 有效URL
        valid_urls = [
            "https://czbooks.net/n/12345",
            "http://czbooks.net/n/678",
        ]
        for url in valid_urls:
            self.assertTrue(self.adapter.validate_novel_url(url))

        # 無效URL
        invalid_urls = [
            "https://other-site.com/n/123",
            "https://czbooks.net/other/123",
            "invalid-url",
        ]
        for url in invalid_urls:
            self.assertFalse(self.adapter.validate_novel_url(url))

    @requests_mock.Mocker()
    def test_extract_novel_info(self, m):
        """測試小說資訊提取"""
        # 模擬HTML響應
        mock_html = """
        <html>
            <head>
                <title>【免費小說】《測試小說》最新章節</title>
            </head>
            <body>
                <div>
                    <a href="/n/123/1">第1章</a>
                    <a href="/n/123/2">第2章</a>
                    <a href="/n/123/3">第3章</a>
                </div>
            </body>
        </html>
        """

        test_url = "https://czbooks.net/n/123"
        m.get(test_url, text=mock_html)

        result = self.adapter.extract_novel_info(test_url)

        self.assertIsInstance(result, NovelInfo)
        self.assertEqual(result.title, "測試小說")
        self.assertEqual(result.total_chapters, 3)
        self.assertEqual(result.source_site, "CZBooks.net")

    @requests_mock.Mocker()
    def test_extract_chapter_content(self, m):
        """測試章節內容提取"""
        # 模擬章節頁面HTML
        mock_html = """
        <html>
            <head>
                <title>第1章 測試章節</title>
            </head>
            <body>
                <div>
                    這是章節內容的第一段。章節內容很長很長，超過了50個字符的限制，所以會被包含在內容中。
                </div>
                <div>
                    這是章節內容的第二段。這段文字也很長，包含了小說的主要情節發展。
                </div>
                <a href="/n/123/2">下一章</a>
                <a href="/n/123/0">上一章</a>
            </body>
        </html>
        """

        test_url = "https://czbooks.net/n/123/1"
        m.get(test_url, text=mock_html)

        result = self.adapter.extract_chapter_content(test_url)

        self.assertIsInstance(result, ChapterInfo)
        self.assertEqual(result.chapter_number, 1)
        self.assertIn("這是章節內容的第一段", result.content)
        self.assertIn("這是章節內容的第二段", result.content)
        self.assertIsNotNone(result.next_chapter_url)
        self.assertIsNotNone(result.previous_chapter_url)

    @requests_mock.Mocker()
    def test_extract_chapter_list(self, m):
        """測試章節列表提取"""
        mock_html = """
        <html>
            <body>
                <div>
                    <a href="/n/123/1">第1章 開始</a>
                    <a href="/n/123/2">第2章 發展</a>
                    <a href="/n/123/3">第3章 結局</a>
                </div>
            </body>
        </html>
        """

        test_url = "https://czbooks.net/n/123"
        m.get(test_url, text=mock_html)

        result = self.adapter.extract_chapter_list(test_url)

        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]["chapter_number"], 1)
        self.assertEqual(result[0]["title"], "第1章 開始")
        self.assertIn("https://czbooks.net/n/123/1", result[0]["url"])

    def test_base_adapter_load_yaml(self):
        import tempfile
        import os

        class DummyAdapter(BaseAdapter):
            def __init__(self, selector_path=None):
                super().__init__("http://example.com", "Test Site")
                self.selectors = {}
                if selector_path:
                    import yaml

                    with open(selector_path, "r") as f:
                        self.selectors = yaml.safe_load(f)

            def extract_novel_info(self, novel_url: str):
                return None

            def extract_chapter_list(self, novel_url: str):
                return []

            def extract_chapter_content(self, chapter_url: str):
                return None

            def search_novels(self, keyword: str):
                return []

        # Create a temporary yaml file
        with tempfile.NamedTemporaryFile(
            mode="w", suffix=".yaml", delete=False
        ) as tmp_file:
            tmp_file.write("title: h1\n")
            yaml_file_path = tmp_file.name

        try:
            adapter = DummyAdapter(selector_path=yaml_file_path)
            self.assertEqual(adapter.selectors["title"], "h1")
        finally:
            # Clean up the temporary file
            os.unlink(yaml_file_path)


if __name__ == "__main__":
    unittest.main()
