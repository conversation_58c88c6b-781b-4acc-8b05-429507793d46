"""PiaotianAdapter unit tests"""

import unittest
import requests_mock

from novel.adapters.piaotian_adapter import PiaotianAdapter
from novel.adapters.base_adapter import NovelInfo, ChapterInfo


class TestPiaotianAdapter(unittest.TestCase):
    def setUp(self):
        self.adapter = PiaotianAdapter()

    @requests_mock.Mocker()
    def test_extract_novel_info(self, m):
        mock_html = """
        <html>
            <body>
                <h1 class="title">測試小說</h1>
                <span class="author">作者甲</span>
                <div id="intro">簡介內容</div>
                <div class="chapter-list">
                    <a href="/html/123/1.html">第1章</a>
                    <a href="/html/123/2.html">第2章</a>
                </div>
            </body>
        </html>
        """
        url = "https://piaotian55.net/html/123/"
        m.get(url, text=mock_html)

        result = self.adapter.extract_novel_info(url)
        self.assertIsInstance(result, NovelInfo)
        self.assertEqual(result.title, "測試小說")
        self.assertEqual(result.author, "作者甲")
        self.assertEqual(result.total_chapters, 2)

    @requests_mock.Mocker()
    def test_extract_chapter_list(self, m):
        mock_html = """
        <html>
            <body>
                <div class="chapter-list">
                    <a href="/html/123/1.html">第1章</a>
                    <a href="/html/123/2.html">第2章</a>
                </div>
            </body>
        </html>
        """
        url = "https://piaotian55.net/html/123/"
        m.get(url, text=mock_html)

        chapters = self.adapter.extract_chapter_list(url)
        self.assertEqual(len(chapters), 2)
        self.assertEqual(chapters[0]["chapter_number"], 1)
        self.assertTrue(chapters[0]["url"].endswith("1.html"))

    @requests_mock.Mocker()
    def test_extract_chapter_content(self, m):
        mock_html = """
        <html>
            <body>
                <h1 class="chapter-title">第1章 開始</h1>
                <div class="content">這是內容</div>
                <a class="next" href="/html/123/2.html">下一章</a>
                <a class="prev" href="/html/123/0.html">上一章</a>
            </body>
        </html>
        """
        url = "https://piaotian55.net/html/123/1.html"
        m.get(url, text=mock_html)

        chapter = self.adapter.extract_chapter_content(url)
        self.assertIsInstance(chapter, ChapterInfo)
        self.assertEqual(chapter.chapter_number, 1)
        self.assertEqual(chapter.title, "第1章 開始")
        self.assertEqual(chapter.content, "這是內容")
        self.assertIsNotNone(chapter.next_chapter_url)
        self.assertIsNotNone(chapter.previous_chapter_url)


if __name__ == "__main__":
    unittest.main()
