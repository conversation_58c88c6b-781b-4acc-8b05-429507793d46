import os
import sys
import pytest
import asyncio
import logging
import django

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 設置測試環境變量
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
os.environ.setdefault("TESTING", "true")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "novel.settings")
os.environ.setdefault("DATABASE_URL", "sqlite:///:memory:")

# 初始化 Django
django.setup()


# 設置異步測試的默認事件循環範圍
@pytest.fixture(scope="function")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_env():
    """設置測試環境"""
    test_dir = os.path.join(PROJECT_ROOT, "tests")
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        logger.info(f"創建測試目錄: {test_dir}")

    return {
        "project_root": PROJECT_ROOT,
        "test_dir": test_dir,
        "python_path": sys.path,
    }
