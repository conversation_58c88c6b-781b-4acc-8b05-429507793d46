from django.core.management.base import BaseCommand
import redis
import logging
from scrapy.utils.project import get_project_settings
from crawler_engine.spiders.qidian_yuepiao import QidianYuepiaoSpider
from crawler_engine.monitoring.prometheus_exporter import start_metrics_server

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "運行分布式爬蟲"

    def add_arguments(self, parser):
        parser.add_argument(
            "--mode",
            type=str,
            choices=["master", "slave"],
            required=True,
            help="運行模式：master（主節點）或slave（從節點）",
        )
        parser.add_argument("--pages", type=int, default=3, help="爬取頁數，默認3頁")

    def handle(self, *args, **options):
        try:
            mode = options["mode"]
            pages = options["pages"]

            # 獲取Redis連接
            crawler_settings = get_project_settings()
            redis_client = redis.Redis(
                host=crawler_settings.get("REDIS_HOST", "localhost"),
                port=crawler_settings.get("REDIS_PORT", 6379),
                db=crawler_settings.get("REDIS_DB", 1),
                decode_responses=True,
            )

            if mode == "master":
                self.run_master(redis_client, pages)
            else:
                self.run_slave(pages)

        except Exception as e:
            logger.error(f"運行分布式爬蟲時發生錯誤: {str(e)}")
            self.stderr.write(self.style.ERROR(f"錯誤: {str(e)}"))

    def run_master(self, redis_client, pages):
        """運行主節點"""
        try:
            self.stdout.write(self.style.SUCCESS("啟動主節點..."))

            # 清理舊的請求隊列
            redis_client.delete(f"{QidianYuepiaoSpider.redis_key}")

            # 添加起始URL到Redis
            base_url = get_project_settings().get("QIDIAN_YUEPIAO_URL")
            for page in range(1, pages + 1):
                url = f"{base_url}?page={page}"
                redis_client.lpush(QidianYuepiaoSpider.redis_key, url)

            self.stdout.write(self.style.SUCCESS(f"已添加{pages}個起始URL到Redis隊列"))

        except Exception as e:
            logger.error(f"主節點運行錯誤: {str(e)}")
            raise

    def run_slave(self, pages):
        """運行從節點"""
        try:
            self.stdout.write(self.style.SUCCESS("啟動從節點..."))

            # 導入這裡以避免循環導入
            from scrapy.crawler import CrawlerProcess

            # 獲取Scrapy設置
            crawler_settings = get_project_settings()

            # 創建爬蟲進程
            process = CrawlerProcess(crawler_settings)

            # 啟動 Prometheus 指標服務
            start_metrics_server()

            # 添加爬蟲
            process.crawl(QidianYuepiaoSpider, pages=pages)

            # 運行爬蟲
            process.start()

        except Exception as e:
            logger.error(f"從節點運行錯誤: {str(e)}")
            raise
