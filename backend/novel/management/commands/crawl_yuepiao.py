from django.core.management.base import BaseCommand
from django.utils import timezone
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from crawler_engine.spiders.qidian_yuepiao import QidianYuepiaoSpider
from crawler_engine.monitoring.prometheus_exporter import start_metrics_server
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "爬取起點月票榜小說"

    def add_arguments(self, parser):
        parser.add_argument("--pages", type=int, default=3, help="爬取頁數，默認3頁")

    def handle(self, *args, **options):
        try:
            self.stdout.write(
                self.style.SUCCESS(f"開始爬取起點月票榜 - {timezone.now()}")
            )

            # 獲取Scrapy設置
            settings = get_project_settings()

            # 創建爬蟲進程
            process = CrawlerProcess(settings)

            # 啟動 Prometheus 指標服務
            start_metrics_server()

            # 添加爬蟲
            process.crawl(QidianYuepiaoSpider, pages=options["pages"])

            # 運行爬蟲
            process.start()

            self.stdout.write(self.style.SUCCESS(f"爬取完成 - {timezone.now()}"))

        except Exception as e:
            logger.error(f"爬取過程中發生錯誤: {str(e)}")
            self.stdout.write(self.style.ERROR(f"爬取失敗: {str(e)}"))
