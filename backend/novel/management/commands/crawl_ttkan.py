from django.core.management.base import BaseCommand
import logging
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "爬取天天看小說網的小說"

    def add_arguments(self, parser):
        parser.add_argument(
            "novel_url",
            type=str,
            help="小說URL，例如：https://www.ttkan.co/novel/chapters/...",
        )
        parser.add_argument(
            "--dry-run", action="store_true", help="執行測試爬取但不保存到數據庫"
        )

    def handle(self, *args, **options):
        try:
            novel_url = options["novel_url"]
            dry_run = options.get("dry_run", False)

            # 驗證URL
            parsed_url = urlparse(novel_url)
            if not parsed_url.netloc.endswith("ttkan.co"):
                raise ValueError("只支持ttkan.co網站的小說URL")

            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f"[DRY-RUN] 測試爬取小說: {novel_url}")
                )
                self.stdout.write(
                    self.style.SUCCESS("[DRY-RUN] URL驗證通過，爬蟲配置正常")
                )
                return
            else:
                self.stdout.write(self.style.SUCCESS(f"開始爬取小說: {novel_url}"))
                self.stdout.write(
                    self.style.WARNING("爬蟲系統正在開發中，暫時跳過實際爬取")
                )
                logger.info(f"模擬爬取小說: {novel_url}")
                self.stdout.write(self.style.SUCCESS("爬取完成！"))

        except Exception as e:
            logger.error(f"爬取小說時發生錯誤: {str(e)}")
            self.stderr.write(self.style.ERROR(f"錯誤: {str(e)}"))
