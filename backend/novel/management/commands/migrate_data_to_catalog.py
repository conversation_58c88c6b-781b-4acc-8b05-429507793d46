"""
Django管理命令：將數據從舊的novel應用遷移到新的catalog應用
一次性數據遷移腳本，支持冪等操作（可重複執行）
"""

from django.core.management.base import BaseCommand
from django.db import transaction

# 故意不使用try-except導入 - 如果模型不存在，我們希望腳本大聲失敗
from novel.models import (
    Novel as OldNovel,
    Chapter as OldChapter,
    Category as OldCategory,
    Tag as OldTag,
)
from apps.catalog.models import (
    Novel as NewNovel,
    Chapter as NewChapter,
    Category as NewCategory,
    Tag as NewTag,
)


class Command(BaseCommand):
    help = "Migrates data from the old 'novel' app to the new 'catalog' app."

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="只顯示將要遷移的數據，不實際執行",
        )

    @transaction.atomic
    def handle(self, *args, **options):
        dry_run = options["dry_run"]

        if dry_run:
            self.stdout.write(self.style.WARNING("🔍 DRY RUN MODE - 不會實際修改數據"))

        self.stdout.write(
            self.style.SUCCESS(
                "🚀 Starting data migration from 'novel' to 'catalog'..."
            )
        )

        # 顯示當前狀態
        self.stdout.write("\n=== 遷移前狀態 ===")
        self.stdout.write(f"舊系統 - Categories: {OldCategory.objects.count()}")
        self.stdout.write(f"舊系統 - Tags: {OldTag.objects.count()}")
        self.stdout.write(f"舊系統 - Novels: {OldNovel.objects.count()}")
        self.stdout.write(f"舊系統 - Chapters: {OldChapter.objects.count()}")
        self.stdout.write(f"新系統 - Novels: {NewNovel.objects.count()}")
        self.stdout.write(f"新系統 - Chapters: {NewChapter.objects.count()}")

        if dry_run:
            self.stdout.write("\n=== DRY RUN 結果預覽 ===")
            self.stdout.write("將要遷移的數據:")
            self.stdout.write(f"- {OldCategory.objects.count()} Categories")
            self.stdout.write(f"- {OldTag.objects.count()} Tags")
            self.stdout.write(f"- {OldNovel.objects.count()} Novels")
            self.stdout.write(f"- {OldChapter.objects.count()} Chapters")
            return

        # 1. 遷移Categories
        self.stdout.write("\n📁 Migrating Categories...")
        categories_migrated = 0
        for old_cat in OldCategory.objects.all():
            obj, created = NewCategory.objects.update_or_create(
                id=old_cat.id, defaults={"name": old_cat.name, "slug": old_cat.slug}
            )
            if created:
                categories_migrated += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Categories: {categories_migrated} created, "
                f"{NewCategory.objects.count()} total"
            )
        )

        # 2. 遷移Tags
        self.stdout.write("\n🏷️ Migrating Tags...")
        tags_migrated = 0
        for old_tag in OldTag.objects.all():
            obj, created = NewTag.objects.update_or_create(
                id=old_tag.id, defaults={"name": old_tag.name, "slug": old_tag.slug}
            )
            if created:
                tags_migrated += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Tags: {tags_migrated} created, {NewTag.objects.count()} total"
            )
        )

        # 3. 遷移Novels (包括M2M關係)
        self.stdout.write("\n📚 Migrating Novels...")
        novels_migrated = 0
        novels_updated = 0

        for old_novel in OldNovel.objects.all().prefetch_related("tags"):
            new_novel, created = NewNovel.objects.update_or_create(
                # 使用穩定的共享鍵進行匹配
                source=old_novel.source,
                source_id=old_novel.source_id,
                defaults={
                    "title": old_novel.title,
                    "author": old_novel.author,
                    "description": old_novel.description,
                    "status": old_novel.status,
                    "source_url": old_novel.source_url,
                    "views": old_novel.views,
                    "likes": old_novel.likes,
                    "favorites": old_novel.favorites,
                    # 處理外鍵關係
                    "category_id": old_novel.category_id,
                    # 保持原始時間戳
                    "created_at": old_novel.created_at,
                    "updated_at": old_novel.updated_at,
                },
            )

            # 處理多對多關係：tags
            tag_ids = [tag.id for tag in old_novel.tags.all()]
            new_novel.tags.set(tag_ids)

            if created:
                novels_migrated += 1
            else:
                novels_updated += 1

            if (novels_migrated + novels_updated) % 10 == 0:
                self.stdout.write(
                    f"📈 進度: {novels_migrated + novels_updated} novels processed..."
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Novels: {novels_migrated} created, {novels_updated} updated, "
                f"{NewNovel.objects.count()} total"
            )
        )

        # 4. 遷移Chapters
        self.stdout.write("\n📄 Migrating Chapters...")
        chapters_migrated = 0
        chapters_updated = 0

        for old_chapter in OldChapter.objects.all():
            # 需要找到對應的新Novel
            try:
                old_novel = old_chapter.novel
                new_novel = NewNovel.objects.get(
                    source=old_novel.source, source_id=old_novel.source_id
                )

                new_chapter, created = NewChapter.objects.update_or_create(
                    novel=new_novel,
                    chapter_number=old_chapter.chapter_number,
                    defaults={
                        "title": old_chapter.title,
                        "content": old_chapter.content,
                        "views": old_chapter.views,
                        "is_vip": old_chapter.is_vip,
                        "source_url": old_chapter.source_url,
                        "created_at": old_chapter.created_at,
                        "updated_at": old_chapter.updated_at,
                    },
                )

                if created:
                    chapters_migrated += 1
                else:
                    chapters_updated += 1

            except NewNovel.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        f"⚠️ Warning: Could not find corresponding new novel for "
                        f"chapter '{old_chapter.title}' (novel: {old_novel.title})"
                    )
                )
                continue

            if (chapters_migrated + chapters_updated) % 50 == 0:
                processed = chapters_migrated + chapters_updated
                self.stdout.write(f"📈 進度: {processed} chapters processed...")

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Chapters: {chapters_migrated} created, {chapters_updated} updated, "
                f"{NewChapter.objects.count()} total"
            )
        )

        # 最終統計
        self.stdout.write("\n=== 遷移完成狀態 ===")
        self.stdout.write(f"新系統 - Categories: {NewCategory.objects.count()}")
        self.stdout.write(f"新系統 - Tags: {NewTag.objects.count()}")
        self.stdout.write(f"新系統 - Novels: {NewNovel.objects.count()}")
        self.stdout.write(f"新系統 - Chapters: {NewChapter.objects.count()}")

        self.stdout.write(
            self.style.SUCCESS(
                "🎉 Data migration complete! All data successfully moved to catalog."
            )
        )
