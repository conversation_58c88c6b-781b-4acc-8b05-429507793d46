import json
import os
import sys
from pathlib import Path

from django.core.management.base import BaseCommand

# Add backend config to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "config"))
from settings import Settings  # noqa: E402


class Command(BaseCommand):
    help = "Export config JSON schema"

    def handle(self, *args, **options):
        schema = Settings.model_json_schema()
        Path("settings.schema.json").write_text(
            json.dumps(schema, indent=2, ensure_ascii=False)
        )
        self.stdout.write(self.style.SUCCESS("settings.schema.json generated"))
