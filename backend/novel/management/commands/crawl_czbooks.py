"""
Django管理命令：爬取CZBooks.net小說
"""

import logging
import os
import sys

from django.conf import settings as django_settings
from django.core.management.base import BaseCommand, CommandError
from crawler_engine.monitoring.prometheus_exporter import start_metrics_server

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """CZBooks爬蟲管理命令"""

    help = "爬取CZBooks.net小說內容"

    def add_arguments(self, parser):
        """添加命令參數"""
        parser.add_argument(
            "--url",
            type=str,
            help="指定要爬取的小說URL",
        )
        parser.add_argument(
            "--urls",
            type=str,
            nargs="+",
            help="指定多個要爬取的小說URL",
        )
        parser.add_argument(
            "--output",
            type=str,
            default="czbooks_output.json",
            help="輸出檔案名稱 (默認: czbooks_output.json)",
        )
        parser.add_argument(
            "--concurrent",
            type=int,
            default=1,
            help="並發請求數 (默認: 1)",
        )

    def handle(self, *args, **options):
        """執行爬蟲命令"""
        # 動態導入Scrapy模組，避免E402錯誤
        sys.path.append(os.path.join(django_settings.BASE_DIR, "novel", "crawler"))

        from scrapy.crawler import CrawlerProcess
        from scrapy.utils.project import get_project_settings  # <--- 關鍵導入
        from crawler_engine.spiders.czbooks_spider import CZBooksSpider

        self.stdout.write(self.style.SUCCESS("🚀 開始執行CZBooks.net爬蟲..."))

        # 設置爬蟲參數
        novel_urls = []
        if options["url"]:
            novel_urls = [options["url"]]
        elif options["urls"]:
            novel_urls = options["urls"]
        else:
            # 如果沒有指定URL，使用分類頁面（避免403錯誤）
            novel_urls = None  # 讓spider使用自己的start_urls

        # --- 這是關鍵修改 ---
        # 設置環境變數以確保Scrapy找到正確的設置模組
        os.environ["SCRAPY_SETTINGS_MODULE"] = "crawler_engine.settings"

        # 獲取項目設置（包含ITEM_PIPELINES）
        settings = get_project_settings()

        # 覆蓋特定設置
        settings.set("CONCURRENT_REQUESTS", options["concurrent"])
        settings.set(
            "FEEDS",
            {
                options["output"]: {
                    "format": "json",
                    "encoding": "utf8",
                    "store_empty": False,
                },
            },
        )

        # 使用項目設置創建CrawlerProcess
        process = CrawlerProcess(settings)
        # --- 修改結束 ---

        # 啟動 Prometheus 指標服務
        start_metrics_server()

        try:
            # 啟動爬蟲
            process.crawl(CZBooksSpider, novel_urls=novel_urls)
            process.start()

            self.stdout.write(
                self.style.SUCCESS(f'✅ 爬蟲執行完成！結果保存到: {options["output"]}')
            )

        except Exception as e:
            logger.error(f"爬蟲執行失敗: {e}")
            raise CommandError(f"爬蟲執行失敗: {e}")
