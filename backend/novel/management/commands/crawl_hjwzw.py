"""
Django管理命令：爬取黃金屋中文網小說
基於czbooks經驗的完美實現
"""

import logging
import os
import sys

from django.conf import settings as django_settings
from django.core.management.base import BaseCommand, CommandError
from crawler_engine.monitoring.prometheus_exporter import start_metrics_server

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """黃金屋中文爬蟲管理命令"""

    help = "爬取黃金屋中文網 (hjwzw.com) 小說內容"

    def add_arguments(self, parser):
        """添加命令參數"""
        parser.add_argument(
            "--categories",
            type=str,
            nargs="+",
            help="指定要爬取的分類 (如: 玄幻 都市 歷史)",
        )
        parser.add_argument(
            "--output",
            type=str,
            default="hjwzw_output.json",
            help="輸出檔案名稱 (默認: hjwzw_output.json)",
        )
        parser.add_argument(
            "--concurrent",
            type=int,
            default=1,
            help="並發請求數 (默認: 1)",
        )

    def handle(self, *args, **options):
        """執行爬蟲命令"""
        # 動態導入Scrapy模組
        sys.path.append(os.path.join(django_settings.BASE_DIR, "novel", "crawler"))

        from scrapy.crawler import CrawlerProcess
        from scrapy.utils.project import get_project_settings  # 關鍵：載入項目設置
        from crawler_engine.spiders.hjwzw_spider import HjwzwSpider

        self.stdout.write(self.style.SUCCESS("🚀 開始執行黃金屋中文爬蟲..."))

        # 設置爬蟲參數
        spider_kwargs = {}
        if options["categories"]:
            # 如果指定了分類，覆蓋默認的start_urls
            base_url = "https://tw.hjwzw.com/List/"
            custom_urls = [f"{base_url}{cat}" for cat in options["categories"]]
            spider_kwargs["start_urls"] = custom_urls

        # 關鍵修復：設置環境變數並獲取項目設置
        os.environ["SCRAPY_SETTINGS_MODULE"] = "crawler_engine.settings"
        settings = get_project_settings()

        # 覆蓋特定設置
        settings.set("CONCURRENT_REQUESTS", options["concurrent"])
        settings.set(
            "FEEDS",
            {
                options["output"]: {
                    "format": "json",
                    "encoding": "utf8",
                    "store_empty": False,
                },
            },
        )

        # 使用項目設置創建CrawlerProcess
        process = CrawlerProcess(settings)

        # 啟動 Prometheus 指標服務 (如果可用)
        try:
            start_metrics_server()
        except Exception as e:
            logger.warning(f"Prometheus 指標服務啟動失敗: {e}")

        try:
            # 啟動爬蟲
            process.crawl(HjwzwSpider, **spider_kwargs)
            process.start()

            self.stdout.write(
                self.style.SUCCESS(f'✅ 爬蟲執行完成！結果保存到: {options["output"]}')
            )

        except Exception as e:
            logger.error(f"爬蟲執行失敗: {e}")
            raise CommandError(f"爬蟲執行失敗: {e}")
