# Generated by Django 4.2.23 on 2025-06-12 09:57

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("novel", "0002_alter_novel_options"),
    ]

    operations = [
        migrations.DeleteModel(
            name="AdPlacement",
        ),
        migrations.AlterUniqueTogether(
            name="chapter",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="chapter",
            name="novel",
        ),
        migrations.AlterUniqueTogether(
            name="novel",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="novel",
            name="category",
        ),
        migrations.RemoveField(
            model_name="novel",
            name="tags",
        ),
        migrations.AlterUniqueTogether(
            name="readinghistory",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="readinghistory",
            name="chapter",
        ),
        migrations.RemoveField(
            model_name="readinghistory",
            name="user_profile",
        ),
        migrations.RemoveField(
            model_name="searchhistory",
            name="category",
        ),
        migrations.RemoveField(
            model_name="searchhistory",
            name="user",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="favorite_novels",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="reading_history",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="user",
        ),
        migrations.DeleteModel(
            name="Category",
        ),
        migrations.DeleteModel(
            name="Chapter",
        ),
        migrations.DeleteModel(
            name="Novel",
        ),
        migrations.DeleteModel(
            name="ReadingHistory",
        ),
        migrations.DeleteModel(
            name="SearchHistory",
        ),
        migrations.DeleteModel(
            name="Tag",
        ),
        migrations.DeleteModel(
            name="UserProfile",
        ),
    ]
