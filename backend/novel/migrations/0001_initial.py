# Generated by Django 3.2.25 on 2025-06-04 19:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AdPlacement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "position",
                    models.CharField(
                        choices=[
                            ("chapter_top", "章節頂部"),
                            ("chapter_bottom", "章節底部"),
                            ("chapter_middle", "章節中間"),
                            ("sidebar", "側邊欄"),
                            ("homepage", "首頁"),
                        ],
                        max_length=20,
                    ),
                ),
                ("ad_code", models.TextField()),
                ("is_active", models.BooleanField(default=True)),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField(blank=True, null=True)),
                ("impressions", models.IntegerField(default=0)),
                ("clicks", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100, unique=True)),
            ],
            options={
                "verbose_name_plural": "categories",
            },
        ),
        migrations.CreateModel(
            name="Chapter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField()),
                ("chapter_number", models.IntegerField()),
                ("views", models.IntegerField(default=0)),
                ("is_vip", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("source_url", models.URLField()),
            ],
            options={
                "ordering": ["chapter_number"],
            },
        ),
        migrations.CreateModel(
            name="Novel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("author", models.CharField(max_length=100)),
                ("description", models.TextField()),
                (
                    "cover",
                    models.ImageField(blank=True, null=True, upload_to="covers/"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("ongoing", "連載中"), ("completed", "已完結")],
                        default="ongoing",
                        max_length=20,
                    ),
                ),
                ("views", models.IntegerField(default=0)),
                ("likes", models.IntegerField(default=0)),
                ("favorites", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("source", models.CharField(max_length=50)),
                ("source_id", models.CharField(max_length=100)),
                ("source_url", models.URLField()),
                (
                    "category",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="novel.category",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ReadingHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("last_read", models.DateTimeField(auto_now=True)),
                (
                    "chapter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="novel.chapter"
                    ),
                ),
            ],
            options={
                "ordering": ["-last_read"],
            },
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "favorite_novels",
                    models.ManyToManyField(
                        related_name="favorited_by", to="novel.Novel"
                    ),
                ),
                (
                    "reading_history",
                    models.ManyToManyField(
                        through="novel.ReadingHistory", to="novel.Chapter"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SearchHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("query", models.CharField(max_length=200)),
                ("filters", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_anonymous", models.BooleanField(default=False)),
                ("session_id", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="novel.category",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddField(
            model_name="readinghistory",
            name="user_profile",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="novel.userprofile"
            ),
        ),
        migrations.AddField(
            model_name="novel",
            name="tags",
            field=models.ManyToManyField(to="novel.Tag"),
        ),
        migrations.AddField(
            model_name="chapter",
            name="novel",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chapters",
                to="novel.novel",
            ),
        ),
        migrations.AddIndex(
            model_name="searchhistory",
            index=models.Index(
                fields=["user", "-created_at"], name="novel_searc_user_id_818c8c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="searchhistory",
            index=models.Index(
                fields=["session_id", "-created_at"],
                name="novel_searc_session_0c19f3_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="readinghistory",
            unique_together={("user_profile", "chapter")},
        ),
        migrations.AlterUniqueTogether(
            name="novel",
            unique_together={("source", "source_id")},
        ),
        migrations.AlterUniqueTogether(
            name="chapter",
            unique_together={("novel", "chapter_number")},
        ),
    ]
