"""
資料庫配置管理
"""

import os
from typing import Optional

from .base import BaseConfig, ENVIRONMENT_KEY

from pydantic import Field, field_validator


class DatabaseConfig(BaseConfig):
    """
    統一資料庫配置

    支援 DATABASE_URL 或分離的環境變數配置
    提供向後兼容性
    """

    # PostgreSQL 配置 (統一使用 DB_ 前綴)
    postgres_host: str = Field(
        default="localhost",
        validation_alias="DB_HOST",
        description="PostgreSQL host",
    )

    postgres_port: int = Field(
        default=5432,
        ge=1,
        le=65535,
        validation_alias="DB_PORT",
        description="PostgreSQL port",
    )

    postgres_db: str = Field(
        default="novelwebsite",
        validation_alias="DB_NAME",
        description="PostgreSQL database name",
    )

    postgres_user: str = Field(
        default="postgres",
        validation_alias="DB_USER",
        description="PostgreSQL username",
    )

    postgres_password: Optional[str] = Field(
        default="",
        validation_alias="DB_PASSWORD",
        description="PostgreSQL password",
    )

    # URL 覆蓋選項 (優先使用)
    database_url: Optional[str] = Field(
        default=None,
        validation_alias="DATABASE_URL",
        description="Complete PostgreSQL URL (overrides individual settings)",
    )

    # SSL 設定
    postgres_ssl: bool = Field(
        default=False,
        validation_alias="DB_SSL",
        description="Enable PostgreSQL SSL",
    )

    postgres_ssl_mode: str = Field(
        default="require",
        validation_alias="DB_SSL_MODE",
        description="PostgreSQL SSL mode",
    )

    @property
    def postgres_url(self) -> str:
        """
        生成 PostgreSQL 連接字串

        優先使用 DATABASE_URL，否則從個別環境變數組合
        """
        if self.database_url:
            return self.database_url

        # 構建連接字串
        password = self.postgres_password or ""
        url = (
            f"postgresql://{self.postgres_user}:{password}"
            f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
        )

        # 添加 SSL 參數
        if self.postgres_ssl:
            url += f"?sslmode={self.postgres_ssl_mode}"

        return url

    @property
    def postgres_config_dict(self) -> dict:
        """
        返回 Django-style 的資料庫配置字典
        """
        return {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": self.postgres_db,
            "USER": self.postgres_user,
            "PASSWORD": self.postgres_password or "",
            "HOST": self.postgres_host,
            "PORT": self.postgres_port,
            "OPTIONS": {
                "client_encoding": "UTF8",
                **({"sslmode": self.postgres_ssl_mode} if self.postgres_ssl else {}),
            },
            "TEST": {
                "NAME": f"test_{self.postgres_db}",
            },
        }

    @property
    def django_database_config(self) -> dict:
        """
        返回完整的 Django DATABASES 配置
        """
        return {"default": self.postgres_config_dict}

    @field_validator("postgres_password")
    @classmethod
    def validate_postgres_password(cls, v, info):
        """驗證生產環境必須設定密碼"""
        environment = os.getenv(ENVIRONMENT_KEY, "development")
        if environment == "production" and not v:
            raise ValueError("生產環境必須設定 PostgreSQL 密碼")
        return v

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v):
        """驗證 DATABASE_URL 格式"""
        if v and not v.startswith(("postgresql://", "postgres://")):
            raise ValueError("DATABASE_URL 必須是有效的 PostgreSQL URL")
        return v
