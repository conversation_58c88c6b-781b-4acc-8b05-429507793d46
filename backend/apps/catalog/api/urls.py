from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r"categories", views.CategoryViewSet)
router.register(r"tags", views.TagViewSet)
router.register(r"novels", views.NovelViewSet)
router.register(r"chapters", views.ChapterViewSet)
router.register(r"profile", views.UserProfileViewSet, basename="profile")
router.register(r"ads", views.AdPlacementViewSet)
router.register(
    r"search-history", views.SearchHistoryViewSet, basename="search-history"
)

urlpatterns = [
    path("", include(router.urls)),
]
