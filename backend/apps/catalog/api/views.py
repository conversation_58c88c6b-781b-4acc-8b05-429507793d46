from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response

from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import F, Q
from django.utils import timezone
from django.core.paginator import Paginator, EmptyPage
from ..models import (
    Category,
    Tag,
    Novel,
    Chapter,
    UserProfile,
    ReadingHistory,
    AdPlacement,
    SearchHistory,
)
from .serializers import (
    CategorySerializer,
    TagSerializer,
    NovelListSerializer,
    NovelDetailSerializer,
    ChapterListSerializer,
    ChapterDetailSerializer,
    UserProfileSerializer,
    ReadingHistorySerializer,
    AdPlacementSerializer,
    SearchHistorySerializer,
)


class CategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    lookup_field = "slug"
    filter_backends = [filters.SearchFilter]
    search_fields = ["name"]


class TagViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    lookup_field = "slug"
    filter_backends = [filters.SearchFilter]
    search_fields = ["name"]


class NovelViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Novel.objects.all()
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["category", "status"]
    search_fields = ["title", "author", "description"]
    ordering_fields = ["updated_at", "views", "favorites"]

    def get_serializer_class(self):
        if self.action == "list":
            return NovelListSerializer
        return NovelDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.views = F("views") + 1
        instance.save()
        instance.refresh_from_db()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def favorite(self, request, pk=None):
        novel = self.get_object()
        user_profile = request.user.catalog_profile
        if user_profile.favorite_novels.filter(id=novel.id).exists():
            user_profile.favorite_novels.remove(novel)
            novel.favorites = F("favorites") - 1
            message = "已取消收藏"
        else:
            user_profile.favorite_novels.add(novel)
            novel.favorites = F("favorites") + 1
            message = "已加入收藏"
        novel.save()
        return Response({"message": message})

    @action(detail=True, methods=["get"])
    def chapters(self, request, pk=None):
        novel = self.get_object()
        chapters = novel.chapters.all().order_by("chapter_number")
        serializer = ChapterListSerializer(chapters, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def search(self, request):
        query = request.query_params.get("query", "")
        page = int(request.query_params.get("page", 1))
        limit = int(request.query_params.get("limit", 20))

        novels = Novel.objects.filter(
            Q(title__icontains=query) | Q(author__icontains=query)
        ).order_by("-updated_at")

        paginator = Paginator(novels, limit)
        try:
            page_obj = paginator.page(page)
        except EmptyPage:
            return Response({"novels": [], "total": 0, "page": page, "total_pages": 0})

        serializer = NovelListSerializer(page_obj, many=True)
        return Response(
            {
                "novels": serializer.data,
                "total": paginator.count,
                "page": page,
                "total_pages": paginator.num_pages,
            }
        )


class ChapterViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Chapter.objects.all()
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["novel__title"]
    ordering_fields = ["chapter_number"]

    def get_serializer_class(self):
        if self.action == "list":
            return ChapterListSerializer
        return ChapterDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.views = F("views") + 1
        instance.save()
        instance.refresh_from_db()

        # 記錄閱讀歷史
        if request.user.is_authenticated:
            ReadingHistory.objects.update_or_create(
                user_profile=request.user.catalog_profile,
                chapter=instance,
                defaults={"last_read": timezone.now()},
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class UserProfileViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserProfileSerializer

    def get_queryset(self):
        return UserProfile.objects.filter(user=self.request.user)

    @action(detail=False)
    def reading_history(self, request):
        history = ReadingHistory.objects.filter(
            user_profile=request.user.catalog_profile
        ).select_related("chapter", "chapter__novel")[:50]
        serializer = ReadingHistorySerializer(history, many=True)
        return Response(serializer.data)


class AdPlacementViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = AdPlacement.objects.filter(is_active=True)
    serializer_class = AdPlacementSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["position"]

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.impressions = F("impressions") + 1
        instance.save()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class SearchHistoryViewSet(viewsets.ModelViewSet):
    serializer_class = SearchHistorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            return SearchHistory.objects.filter(user=self.request.user)
        return SearchHistory.objects.filter(
            session_id=self.request.session.session_key, is_anonymous=True
        )

    def perform_create(self, serializer):
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user)
        else:
            if not self.request.session.session_key:
                self.request.session.save()
            serializer.save(
                session_id=self.request.session.session_key, is_anonymous=True
            )

    @action(detail=False, methods=["delete"])
    def clear(self, request):
        self.get_queryset().delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
