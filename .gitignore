# Environment variables
.env
**/.env
**/.env.production
**/.env.local
.env.development.local
.env.test.local
.env.production.local

# Node modules and build outputs
node_modules/
backend/node_modules/
frontend/node_modules/
frontend/build/
frontend/.next/
backend/dist/
docs/**/_build/
frontend/node_modules/.cache/
node_modules/.cache/

# Ignore generated HTML files from build processes
/frontend/build/**/*.html
/docs/**/*.html
# Keep Django templates and static HTML files

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Package manager lock files (prevent mixing)
package-lock.json
yarn.lock

# OS files
.DS_Store
Thumbs.db

# Sensitive credentials
novel-admin_accessKeys.csv
novel-bastion-key.pem

# 🛡️ 大型檔案和壓縮檔防護 (防止意外提交)
# 所有壓縮檔案
*.zip
*.tar.gz
*.tar.bz2
*.tar.xz
*.rar
*.7z
*.gz
*.bz2
*.xz

# 大型資料檔案
*.csv
*.json
*.xml
*.sql
*.dump

# 實驗和臨時檔案
*experiment*
temp/
*.temp
*tmp*
*backup*
optimization.patch
*.patch

# Python cache files
**/__pycache__/

# Redis and debugging files
dump.rdb
page.html
*.py[cod]
*$py.class
*.pyc

# 🛡️ Python虛擬環境防護
.venv/
venv/
env/
ENV/
.python-version

# Django static files and media
/staticfiles/
/media/

# Database files
*.sqlite3
*.db
*.rdb

# Runtime artifacts and outputs
/output/
/temp/
debug_*.py
deploy.sh

# Exception: Allow test fixtures BEFORE ignoring _data.json files
!**/fixtures/*_data.json
# Exception: Allow Docker CI package files
!infra/docker/package-ci.json
*_data.json
*_output.json

# Coverage reports
.coverage
coverage/
htmlcov/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Task Master AI files
tasks.json
tasks/

# Act - Local GitHub Actions runner
# https://github.com/nektos/act
.act/
.secrets
.env
.env.devcontext

# MCP configuration with credentials
# MCP (Model Context Protocol) configuration
.cursor/mcp.json          # Local config with secrets - copy from .cursor/mcp.json.template
.cursor/mcp.json.local    # Local overrides for development
.cursor/mcp.json

cursor_id_modifier_0.0.6_darwin_amd64
sessionmanager-bundle/
sessionmanager-bundle.zip
frontend/storybook-static/
oracle-freetier-instance-creation/
