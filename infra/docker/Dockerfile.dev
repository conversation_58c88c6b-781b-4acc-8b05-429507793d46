FROM python:3.9-slim

# 安裝系統依賴和開發工具
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    libpq-dev \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    docker.io \
    && rm -rf /var/lib/apt/lists/*

# 安裝Node.js (用於前端開發)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# 設置工作目錄
WORKDIR /workspace

# 安裝Python依賴
COPY requirements.txt /workspace/
RUN pip install --no-cache-dir -r requirements.txt

# 安裝額外的開發工具
RUN pip install --no-cache-dir \
    ipython \
    black \
    flake8 \
    pytest \
    pytest-django \
    django-extensions

# 設置環境變量
ENV PYTHONPATH=/workspace/backend
ENV DJANGO_SETTINGS_MODULE=novel.settings

# 創建entrypoint腳本
RUN echo '#!/bin/bash\necho "🐳 NovelWebsite 開發環境已就緒！"\necho "📁 工作目錄: /workspace"\necho "🐍 Python版本: $(python --version)"\necho "📦 Node版本: $(node --version)"\necho "🔧 可用工具: git, vim, nano, htop, tree, ipython, black, flake8, pytest"\necho ""\nexec "$@"' > /entrypoint.sh \
    && chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["bash"]
