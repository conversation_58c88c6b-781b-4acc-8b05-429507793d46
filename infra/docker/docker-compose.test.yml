version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
      POSTGRES_DB: test_novelwebsite
    ports:
      - "5432:5432"
    tmpfs:
      - /var/lib/postgresql/data
    command: postgres -c log_statement=all -c log_destination=stderr

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    tmpfs:
      - /data
