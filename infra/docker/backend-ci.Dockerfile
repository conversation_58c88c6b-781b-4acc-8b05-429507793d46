# Tier 1.5 後端 Docker 映像 - CI 精簡版
# 移除不必要的 crawl4ai 依賴，遵循 YAGNI 原則

FROM python:3.11-slim AS base

# 設置工作目錄
WORKDIR /workspace

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    git \
    gcc \
    g++ \
    libpq-dev \
    libxml2-dev \
    libxslt1-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 升級 pip
RUN pip install --upgrade pip

# 🐍 確保 Python 與 pip 命令在所有路徑下可用
RUN ln -sf /usr/local/bin/python3 /usr/local/bin/python \
    && ln -sf /usr/local/bin/python3 /usr/bin/python \
    && ln -sf /usr/local/bin/python3 /usr/bin/python3 \
    && ln -sf /usr/local/bin/pip3 /usr/local/bin/pip \
    && ln -sf /usr/local/bin/pip3 /usr/bin/pip \
    && ln -sf /usr/local/bin/python3 /usr/local/bin/Python \
    && ln -sf /usr/local/bin/python3 /usr/bin/Python \
    && python3 --version && python --version && Python --version && pip --version

# 複製 requirements.txt 並創建 CI 精簡版
COPY backend/requirements.txt ./requirements-full.txt

# 創建 CI 專用的精簡版 requirements
RUN grep -v "crawl4ai" requirements-full.txt > requirements-ci.txt && \
    echo "📦 CI Requirements (移除 crawl4ai):" && \
    cat requirements-ci.txt

# 安裝精簡版依賴
RUN pip install --no-cache-dir -r requirements-ci.txt

# 安裝 Playwright（但只安裝必要的瀏覽器）
RUN playwright install chromium && \
    playwright install-deps chromium

# 設置用戶權限
RUN useradd -m -s /bin/bash ci-user && \
    chown -R ci-user:ci-user /workspace
USER ci-user

# 設置環境變數
ENV PYTHONPATH=/workspace/backend
ENV DJANGO_SETTINGS_MODULE=backend.settings.test
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python --version || exit 1

# 標籤
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="1.0-ci"
LABEL description="Backend CI image with YAGNI optimization (removed crawl4ai)"
LABEL tier="1.5-ci"
LABEL optimization="YAGNI - removed unused crawl4ai dependency"

# 預設命令
CMD ["bash"]
