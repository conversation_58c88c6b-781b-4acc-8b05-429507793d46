# Frontend CI Dockerfile - 完美層快取策略優化
# 🎯 目標：4分48秒 → <1分鐘 (節省85%建置時間)
# 🚀 策略：最少變更到最多變更的順序

FROM node:20-bullseye

# 1. 設置工作目錄
WORKDIR /workspace

# 2. 安裝系統依賴 (最少變更，重度快取)
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates \
    fonts-liberation \
    chromium \
    chromium-sandbox \
    git \
    --no-install-recommends \
 && rm -rf /var/lib/apt/lists/*

# 3. 啟用 corepack 和 pnpm
RUN corepack enable && corepack prepare pnpm@9.4.0 --activate

# 4. 設置 pnpm 全局 bin 目錄到 PATH
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# 5. 現在安全地安裝全域工具並驗證版本
RUN pnpm install -g @lhci/cli \
 && pnpm --version && which pnpm

# 6. 只複製依賴定義檔 (快取關鍵點)
COPY frontend/package.json frontend/pnpm-lock.yaml ./

# 7. 安裝依賴 (只在 lockfile 變更時重建)
RUN echo "📦 Installing dependencies from lockfile..." \
 && pnpm install --frozen-lockfile \
 && echo "✅ Dependencies installed."

# 8. 最後複製應用程式源碼 (最多變更)
# 當程式碼變更時，只有這個快速步驟和後續步驟會重建
COPY . .

# 設置環境變數
ENV CHROME_PATH=/usr/bin/chromium \
    CHROMIUM_PATH=/usr/bin/chromium \
    LHCI_CHROMIUM_PATH=/usr/bin/chromium \
    NODE_ENV=test \
    CI=true

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node --version && pnpm --version || exit 1

# 標籤 - 反映最新優化
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="6.0-ultimate-cache"
LABEL description="Frontend CI image with ultimate layer caching strategy"
LABEL optimization="system→global→deps→source (perfect order)"
LABEL expected-improvement="4m48s→<1min (85% faster)"
LABEL pnpm-version="9.4.0-locked"

# 預設命令
CMD ["/bin/bash"]
