# 🐳 Docker Socket 掛載 + act 環境配置
# 使用Docker Socket共享方案，簡潔高效

services:
  # 🛠️ 主要開發環境 + act執行器
  novel-dev-act:
    build:
      context: .
      dockerfile: Dockerfile.act
    container_name: novel-dev-act
    restart: unless-stopped

    # 🔒 Docker Socket掛載 + 項目掛載
    volumes:
      # 項目代碼掛載
      - ./:/workspace
      # Docker Socket掛載 (核心功能)
      - /var/run/docker.sock:/var/run/docker.sock
      # act緩存和配置持久化
      - act_cache:/root/.cache/act
      - act_config:/root/.config/act
      # GitHub Actions工作流程緩存
      - github_actions_cache:/tmp/act

    # 🌐 網絡配置
    networks:
      - novel_ci_network

    # 🔧 環境變數
    environment:
      # Python/Django配置
      - DJANGO_SETTINGS_MODULE=novel.settings
      - PYTHONPATH=/workspace/backend
      - PYTHONUNBUFFERED=1

      # Docker配置
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DOCKER_BUILDKIT=1
      - COMPOSE_DOCKER_CLI_BUILD=1

      # act配置
      - ACT_LOG_LEVEL=info
      - ACT_PLATFORM_MAP=ubuntu-latest=catthehacker/ubuntu:act-latest
      - ACT_CACHE_SERVER_ENABLED=true

      # GitHub Actions模擬配置
      - GITHUB_REPOSITORY=MumuTW/novel-web
      - GITHUB_ACTOR=claude-dev

      # 開發環境標識
      - CI_ENVIRONMENT=docker-act

    # 📁 工作目錄
    working_dir: /workspace

    # 🚀 初始化命令
    command: >
      bash -c "
        echo '🚀 Docker Socket + act 環境初始化中...' &&
        echo '📦 驗證Docker連接...' &&
        docker --version &&
        docker info --format '{{.ServerVersion}}' &&
        echo '🎭 驗證act安裝...' &&
        act --version &&
        echo '✅ 環境準備完成，啟動開發模式...' &&
        exec tail -f /dev/null
      "

    # 🏥 健康檢查
    healthcheck:
      test: ["CMD", "docker", "info"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # ⚡ 資源限制和安全配置
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.25'

    # 🔒 安全配置
    security_opt:
      - no-new-privileges:true

    # 📝 只讀根文件系統 (排除必要的寫入目錄)
    read_only: false  # act需要寫入權限

    # 🚫 禁用特權模式
    privileged: false

    # 👤 用戶配置 (容器內非root用戶)
    user: "1000:1000"

  # 🔄 Redis服務 (支持act測試中的緩存需求)
  redis-act:
    image: redis:7-alpine
    container_name: novel-redis-act
    restart: unless-stopped

    volumes:
      - redis_act_data:/data

    networks:
      - novel_ci_network

    command: redis-server --appendonly yes --maxmemory 256mb

    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # 🗄️ PostgreSQL測試資料庫 (支持act CI測試)
  postgres-act:
    image: postgres:15-alpine
    container_name: novel-postgres-act
    restart: unless-stopped

    volumes:
      - postgres_act_data:/var/lib/postgresql/data

    networks:
      - novel_ci_network

    environment:
      - POSTGRES_DB=test_novelwebsite
      - POSTGRES_USER=test_user
      - POSTGRES_PASSWORD=test_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C

    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_novelwebsite"]
      interval: 10s
      timeout: 5s
      retries: 5

# 🌐 網絡配置
networks:
  novel_ci_network:
    driver: bridge
    name: novel_ci_network

# 💾 持久化存儲卷
volumes:
  # act相關存儲
  act_cache:
    driver: local
    name: novel_act_cache

  act_config:
    driver: local
    name: novel_act_config

  github_actions_cache:
    driver: local
    name: novel_github_actions_cache

  # 服務數據存儲
  redis_act_data:
    driver: local
    name: novel_redis_act_data

  postgres_act_data:
    driver: local
    name: novel_postgres_act_data
